package com.engine.zcyl.gyl.job;

import weaver.conn.RecordSet;
import weaver.integration.logging.Logger;
import weaver.integration.logging.LoggerFactory;
import weaver.interfaces.schedule.BaseCronJob;

/**
 * @FileName UpdateTouziYearJob.java
 * @Description 更新投资计划台账
 * 每年1月1日执行
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/1/8
 */
public class UpdateTouziYearJob extends BaseCronJob {
    private static final Logger log = LoggerFactory.getLogger(UpdateTouziYearJob.class);

    /**
     * 执行
     */
    @Override
    public void execute() {
        log.info("UpdateTouziYearJob---START");
        RecordSet rs = new RecordSet();
        String sql1 = "update uf_gqtzjh set dnnf = YEAR(GETDATE()) ";
        String sql2 = "update uf_gdzctzjh set dqnf= YEAR(GETDATE()) ";
        if (!rs.executeUpdate(sql1)) {
            log.error("更新uf_gqtzjh出错，" + rs.getExceptionMsg());
        }
        if (!rs.executeUpdate(sql2)) {
            log.error("更新uf_gdzctzjh出错，" + rs.getExceptionMsg());
        }
        log.info("UpdateTouziYearJob---END");
    }
}
