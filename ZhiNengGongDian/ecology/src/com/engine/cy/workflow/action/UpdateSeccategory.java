package com.engine.cy.workflow.action;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;

import java.util.HashMap;
import java.util.Map;

public class UpdateSeccategory extends BaseBean implements Action {

    public String secid;

    public String cols;

    @Override
    public String execute(RequestInfo requestInfo) {
        RequestManager rm = requestInfo.getRequestManager();
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        Map<String, String> map = new HashMap<>();
        for (Property property : propertys) {
            String str = property.getName();
            String value = property.getValue();
            map.put(str, value);
        }
        String ids = map.get(cols);
        if (ids != null) {
            String[] docids = ids.split(",");
            for (String id :
                    docids) {
                String sql = "UPDATE docdetail set SECCATEGORY = '" + secid + "' where id = '" + id + "'";
                RecordSet recordSet = new RecordSet();
                boolean flag = recordSet.execute(sql);
                if (!flag) {
                    rm.setMessagecontent(sql + "____" + recordSet.getExceptionMsg());
                    return Action.FAILURE_AND_CONTINUE;
                }
            }
        }
        return Action.SUCCESS;
    }

    public String getSecid() {
        return secid;
    }

    public void setSecid(String secid) {
        this.secid = secid;
    }

    public String getCols() {
        return cols;
    }

    public void setCols(String cols) {
        this.cols = cols;
    }
}
