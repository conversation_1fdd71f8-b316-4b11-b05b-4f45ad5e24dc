<%@ page language="java" contentType="text/html; charset=UTF-8" %>
<%@ page import="weaver.general.*,weaver.conn.RecordSet" %>
<%@ page import="weaver.integration.logging.Logger" %>
<%@ page import="weaver.integration.logging.LoggerFactory" %>
<%@ page import="java.util.*" %>
<%@ page import="java.security.*" %>
<%@ page import="java.text.SimpleDateFormat" %>

<%!
    public String encodestr(String str) {
        return java.net.URLEncoder.encode(str);
    }

    private String MD5Self(String sourceStr, int aa) {
        String result = "";
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(sourceStr.getBytes());
            byte[] b = md.digest();
            int i;
            StringBuilder sb = new StringBuilder("");
            for (byte value : b) {
                i = value;
                if (i < 0)
                    i += 256;
                if (i < 16)
                    sb.append("0");
                sb.append(Integer.toHexString(i));
            }
            result = sb.toString();
            if (aa == 16) {
                result = sb.substring(8, 24);
            }

        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return result;
    }
%>
<%
    Logger log = LoggerFactory.getLogger();
    BaseBean bb = new BaseBean();
    RecordSet rs = new RecordSet();
    String loginid = "";
    // 获取传递过来的数据主键
    String tododataid = request.getParameter("tododataid");
    String isRemark = request.getParameter("isremark");
    bb.writeLog("tododataid=" + tododataid);
    bb.writeLog("isRemark=" + isRemark);
    if( "0".equals(isRemark) || "8".equals(isRemark)){
        //待办
        // 更新数据为已读
        rs.executeUpdate("update ofs_todo_data set viewtype = 1 where id = ?", tododataid);
        //查询该条待办数据
        rs.executeQuery("select * from ofs_todo_data where id = ?", tododataid);
        rs.next();
    }else{
        //已办
        // 更新数据为已读
        rs.executeUpdate("update ofs_done_data set viewtype = 1 where id = ?", tododataid);
        //查询该条待办数据
        rs.executeQuery("select * from ofs_done_data where id = ?", tododataid);
        rs.next();
    }

    String appurlsrc = Util.null2String(rs.getString("appurlsrc"));
    String sysid = Util.null2String(rs.getString("sysid"));
    String userid = Util.null2String(rs.getString("userid"));
    String receiver = Util.null2String(rs.getString("receiver"));
    bb.writeLog("appurlsrc=" + appurlsrc);
    bb.writeLog("sysid=" + sysid);
    bb.writeLog("userid=" + userid);
    bb.writeLog("receiver=" + receiver);


    // 开发跳转逻辑
    rs.executeQuery("select * from ofs_sysinfo where sysid = ?", sysid);
    if (rs.next()) {
//        String Appprefixurl = Util.null2String(rs.getString("Appprefixurl"));
//        String pcprefixurl = Util.null2String(rs.getString("pcprefixurl"));
        //外部URL
        String pcouterfixurl = Util.null2String(rs.getString("pcouterfixurl"));

        String sysCode = Util.null2String(rs.getString("syscode"));
        String token = "";
        bb.writeLog("pcouterfixurl=" + pcouterfixurl);
//        bb.writeLog("pcprefixurl=" + pcprefixurl);
        //生成约定的MD5加密的token
        String sql = "select * from uf_entrance where sysid= ? ";
        rs.executeQuery(sql, sysCode);
        if (rs.next()) {
            String appid = Util.null2String(rs.getString("appid")).trim();
            String secret = Util.null2String(rs.getString("secret")).trim();
            String hrmcode = Util.null2String(rs.getString("hrmcode")).trim();
            if ("".equals(hrmcode)) {
                hrmcode = "loginid";
            }

            RecordSet rs1 = new RecordSet();
            rs1.execute("select * from hrmresource where id=" +userid);
            if (rs1.next()) {
                loginid = Util.null2String(rs1.getString(hrmcode)).trim();
            }
            SimpleDateFormat tt = new SimpleDateFormat("yyyyMMdd");
            String date = tt.format(new Date());
            String p = loginid + date + appid + secret;
            token = MD5Self(p, 32);
            token = token.toLowerCase();

        } else {
            out.println("<body><span style=\"font-size:32pt;color:#FF0000\" >未维护相应单点登录系统，请联系系统管理员</span></body>");
            return;
        }

//        //TODO 1 调用e9接口获取token
//        OutputStreamWriter oout = null;
//        BufferedReader iin = null;
//        String result = "";
//        try {
//            // 发送请求参数
//            rs.executeQuery(" select loginid from hrmresource where id = ?", userid);
//            rs.next();
//            String loginid = rs.getString("loginid");
//            URL realUrl = new URL(pcprefixurl + "/ssologin/getToken?appid=fore9&loginid=" + java.net.URLEncoder.encode(receiver, "UTF-8"));
//            // 打开和URL之间的连接
//            URLConnection conn = realUrl.openConnection();
//            // 设置通用的请求属性
//            conn.setRequestProperty("accept", "*/*");
//            conn.setRequestProperty("connection", "Keep-Alive");
//            conn.setRequestProperty("Content-Type", "application/json");
//            // 发送POST请求必须设置如下两行
//            conn.setDoOutput(true);
//            conn.setDoInput(true);
//            // 获取URLConnection对象对应的输出流
//            oout = new OutputStreamWriter(conn.getOutputStream(), "UTF-8");
//
//            oout.write("");
//            // flush输出流的缓冲
//            oout.flush();
//            // 定义BufferedReader输入流来读取URL的响应
//            iin = new BufferedReader(
//                    new InputStreamReader(conn.getInputStream(), "UTF-8"));
//            String line;
//            while ((line = iin.readLine()) != null) {
//                result += line;
//            }
//            token = result;
//            log.error("token：" + result);
//        } catch (Exception e) {
//            log.error("发送 POST 请求出现异常！", e);
//            e.printStackTrace();
//        }
//        //使用finally块来关闭输出流、输入流
//        finally {
//            try {
//                if (oout != null) {
//                    oout.close();
//                }
//                if (iin != null) {
//                    iin.close();
//                }
//            } catch (IOException ex) {
//                ex.printStackTrace();
//            }
//        }
        //TODO 2 拼接到token到appurl中
        String tourl = pcouterfixurl + appurlsrc;
        String connector = "?";
        if(tourl.contains("?")){
            connector = "&";
        }
        if (tourl.contains("#")) {
            String[] split = tourl.split("#");
            tourl = split[0] + connector+"token=" + token +"&loginid="+loginid+"#" + split[1];
        } else {
            tourl = tourl + connector+"token=" + token+"&loginid="+loginid;
        }
        bb.writeLog("tourl=" + tourl);
        bb.writeLog("移动端跳转地址：" + tourl);

%>
<script type="text/javascript">

    location.replace('<%=tourl%>');
</script>
<%
    } else {
        log.error("根据标识：" + sysid + "未查询到数据");
        out.println("<body><span style=\"font-size:32pt;color:#FF0000\" >根据标识： "+sysid+" 未查询到数据</span></body>");

        return;
    }
%>
