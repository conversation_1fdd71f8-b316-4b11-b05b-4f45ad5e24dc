<%@ page
        import="weaver.general.*,java.util.*,java.math.*,java.text.*,weaver.system.*,weaver.conn.*,weaver.file.*,com.help.DateHelper" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="/browserTag" prefix="brow" %>
<%@ taglib uri="/WEB-INF/weaver.tld" prefix="wea" %>
<%@ include file="/systeminfo/init_wev8.jsp" %>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page"/>
<jsp:useBean id="rs0" class="weaver.conn.RecordSet" scope="page"/>
<jsp:useBean id="rs1" class="weaver.conn.RecordSet" scope="page"/>
<jsp:useBean id="rs2" class="weaver.conn.RecordSet" scope="page"/>
<script type="text/javascript" src="/formmode/js/modebrow_wev8.js?v=1"></script>
<script type="text/javascript" src="/formmode/js/AddMode_wev8.js"></script>
<script type="text/javascript" src="/js/jquery-autocomplete/browser_wev8.js"></script>
<SCRIPT language="javascript" src="/js/datetime_wev8.js"></script>
<SCRIPT language="javascript" src="/js/JSDateTime/WdatePicker_wev8.js"></script>
<script src="/js/workflow/wfbrow_wev8.js" type="text/javascript"></script>
<script src="/js/messagejs/simplehrm_wev8.js" type="text/javascript"></script>
<HTML>
<HEAD>
    <LINK href="/css/Weaver_wev8.css" type=text/css rel=STYLESHEET>
    <link rel="stylesheet" href="/css/ecology8/request/requestTopMenu_wev8.css" type="text/css"/>
</head>
    <%
//数据初始化
int userid=user.getUID();
String departmentid=user.getUserDepartment()+"";
DateHelper dateHelp=new DateHelper();
String ld=Util.null2String(request.getParameter("ld"),"");
String ldHtmlStr=Util.null2String(request.getParameter("ldHtml"),"");
String ldHtml = "";
String[] ldList = ld.split(",");
String[] ldHtmlList = ldHtmlStr.split(",");

if(!ld.equals("")){
	for(int i=0;i<ldHtmlList.length;i++){
	ldHtml=ldHtml+"<span class=\"e8_showNameClass\"><a title=\"\">"+ldHtmlList[i]+"</a>&nbsp;<span class=\"e8_delClass\" id=\""+ldList[i]+"\" onclick=\"del(event,this,1,false,{});\" style=\"opacity: 1; visibility: hidden;\">&nbsp;x&nbsp;</span></span>";
	}
}

String bm=Util.null2String(request.getParameter("bm"),"");
String bmHtmlStr=Util.null2String(request.getParameter("bmHtml"),"");
String bmHtml ="";
String[] bmList = bm.split(",");
String[] bmHtmlList = bmHtmlStr.split(",");
if(!bm.equals("")){
	for(int i=0;i<bmHtmlList.length;i++){
	bmHtml+="<span class=\"e8_showNameClass\"><a href=\"/hrm/company/HrmDepartmentDsp.jsp?id="+bmList[i]+"\" target=\"_new\">"+bmHtmlList[i]+"</a><span class=\"e8_delClass\" id=\""+bmList[i]+"\" onclick=\"del(event,this,1,false,{});\" style=\"opacity: 1; visibility: hidden;\">&nbsp;x&nbsp;</span></span>";	
	}
}
String isShow=Util.null2String(request.getParameter("isShow"),"");
String isShowStr="display:none;";
if(isShow.equals("1")){
	isShowStr="";
}
String firstDate=Util.null2String(request.getParameter("firstDate"),dateHelp.getThisWeekMonday("yyyy-MM-dd"));
String weekType=Util.null2String(request.getParameter("weekType"),"0");
String dateType="yyyy-MM-dd";
String firstDayOfWeek=dateHelp.getFirstDayOfWeek(firstDate, dateType);
String startDate="";
if(weekType.equals("1")){//上周
    firstDate=dateHelp.getDayByAdd(firstDayOfWeek,-7,dateType);
	startDate=firstDate.split("-")[0]+"年"+firstDate.split("-")[1]+"月"+firstDate.split("-")[2]+"日";
}else if(weekType.equals("2")){//下周
	firstDate=dateHelp.getDayByAdd(firstDayOfWeek,7,dateType);
	startDate=firstDate.split("-")[0]+"年"+firstDate.split("-")[1]+"月"+firstDate.split("-")[2]+"日";
}else{
	startDate=firstDayOfWeek.split("-")[0]+"年"+firstDayOfWeek.split("-")[1]+"月"+firstDayOfWeek.split("-")[2]+"日";
}
weekType = "0";
String endDate=dateHelp.getLastDay(startDate,"yyyy年MM月dd日");
String RiChengType=Util.null2String(request.getParameter("RiChengType"),"1");
int LingDaoRoleId=84;//公司领导角色ID
%>
<body style="text-align:center;font-family: 微软雅黑 !important;">
<!--Tab标签切换-->
<table border="0" cellpadding="0" cellspacing="0" style="width:100%;">
    <tr style="height:20px;">
    </tr>
    <tr style="height:40px;">
        <td style="width:67%">
        </td>
        <%if (RiChengType.equals("1")) {%>
        <td align="center" bgcolor="#008df6" style="border:1px solid #008df6;width:10% " onclick="tiaoZhuan(1)">
            <span style="font-size:14px;color:white;cursor: pointer;">集团领导会议</span>
        </td>
        <td style="width:1%">
        </td>
        <td align="center" style="border:1px solid #008df6;width:10%" onclick="tiaoZhuan(2)">
            <span style="font-size:14px;color:#008df6;cursor: pointer;">部门会议</span>
        </td>
        <td style="width:1%">
        </td>
        <td align="center" style="border:1px solid #008df6;width:10%" onclick="tiaoZhuan(3)">
            <span style="font-size:14px;color:#008df6;cursor: pointer;">我的会议</span>
        </td>
        <%} else if (RiChengType.equals("2")) {%>
        <td align="center" style="border:1px solid #008df6;width:10%" onclick="tiaoZhuan(1)">
            <span style="font-size:14px;color:#008df6;cursor: pointer;">集团领导会议</span>
        </td>
        <td style="width:1%">
        </td>
        <td align="center" bgcolor="#008df6" style="border:1px solid #008df6;width:10%" onclick="tiaoZhuan(2)">
            <span style="font-size:14px;color:white;cursor: pointer;">部门会议</span>
        </td>
        <td style="width:1%">
        </td>
        <td align="center" style="border:1px solid #008df6;width:10%" onclick="tiaoZhuan(3)">
            <span style="font-size:14px;color:#008df6;cursor: pointer;">我的会议</span>
        </td>
        <%} else {%>
        <td align="center" style="border:1px solid #008df6;width:10%" onclick="tiaoZhuan(1)">
            <span style="font-size:14px;color:#008df6;cursor: pointer;">集团领导会议</span>
        </td>
        <td style="width:1%">
        </td>
        <td align="center" style="border:1px solid #008df6;width:10%" onclick="tiaoZhuan(2)">
            <span style="font-size:14px;color:#008df6;cursor: pointer;">部门会议</span>
        </td>
        <td style="width:1%">
        </td>
        <td align="center" bgcolor="#008df6" style="border:1px solid #008df6;width:10%" onclick="tiaoZhuan(3)">
            <span style="font-size:14px;color:white;cursor: pointer;">我的会议</span>
        </td>
        <%}%>
        <td style="width:1%">
        </td>
    </tr>
</table>

<!--日程列表-->
<table width="100%" align="center" valign="center">
    <tr style="height:10px;"></tr>
    <tr style="background-color:#EBEBEB;height:40px;">
        <td style="vertical-align: middle;text-align:right;padding-right:1%;">
            <span style="margin-left:10px;float:right;margin-top:5px;cursor: pointer;height:23px;border:1px solid #008df6;font-size:14px;color:#008df6;padding-left:10px;padding-right:10px;padding-top:3px;"
                  onclick="search()" id="btn1">查询</span>
            <span style="margin-left:10px;float:right;margin-top:5px;cursor: pointer;height:23px;border:1px solid #008df6;font-size:14px;color:#008df6;padding-left:10px;padding-right:10px;padding-top:3px;"
                  onclick="shuaxin()" id="btn2">刷新</span>
            <span style="margin-left:10px;float:right;margin-top:5px;cursor: pointer;height:23px;border:1px solid #008df6;font-size:14px;color:#008df6;padding-left:10px;padding-right:10px;padding-top:3px;"
                  onclick="download()" id="btn3">导出</span>
            <span style="margin-left:10px;float:right;margin-top:5px;cursor: pointer;height:23px;border:1px solid #008df6;font-size:14px;color:#008df6;padding-left:10px;padding-right:10px;padding-top:3px;"
                  onclick="thisWeek()" id="btn4">本周</span>
            <image style="float:right;margin-top:6px;margin-bottom:5px;height: 30px" src="/right.png" title="下一周"
                   onclick="down()" id="btn6">
<span style="margin-right:10px;float:right;margin-top:7.5px;font-size:16px;color:#008df6;">
<%=startDate%>
&nbsp;
--
&nbsp;
<%=endDate%>
</span>
                <image style="margin-right:10px;float:right;margin-top:6px;margin-bottom:5px;height: 30px"
                       src="/left.png" title="上一周" onclick="up()" id="btn7">

                    <span style="margin-right:10px;float:right;margin-top:5px;margin-bottom:5px;cursor: pointer;height:23px;border:1px solid #008df6;font-size:14px;color:#008df6;padding-left:10px;padding-right:10px;padding-top:3px;"
                          onclick="newMeeting()" id="btn5">新建</span>


        </td>
    </tr>

    <tr id="trid" style="<%=isShowStr%>background-color:rgb(248,248,248);font-size:16px;">
        <td>
            <%if (RiChengType.equals("1")) {%>
            <!--公司领导-->
            <table style="float:right;">
                <tr>
                    <td>
                        公司领导：
                    </td>
                    <td>
                        <div>
                            <div class="e8_os" style="width:auto;min-width:210px;">

                                <div class="e8_innerShow e8_innerShow_button e8_innerShow_button_right30"
                                     style="max-height:2178px;"><span class="e8_spanFloat"><span
                                        class="e8_browserSpan "><button class="e8_browflow" id="field9343_browserbtn"
                                                                        type="button"
                                                                        onclick="onShowResourceRole1('9343','/systeminfo/BrowserMain.jsp?url=/hrm/resource/RoleResourceBrowser.jsp?roleid=','/hrm/resource/HrmResource.jsp?id=','160',field9343.getAttribute('viewtype'),'44a0b1');"></button></span></span>
                                </div>
                                <div class="e8_innerShow e8_innerShowMust" style="max-height:2178px;"><span
                                        name="field9343spanimg" class="e8_spanFloat" id="field9343spanimg"></span></div>
                                <div class="e8_outScroll" id="outfield9343div"
                                     style="width: 100%; margin-right: -30px; overflow-y: hidden; outline: none;"
                                     tabindex="5000">
                                    <div class="e8_innerShow e8_innerShowContent"
                                         style="max-height: 2178px; margin-right: 30px; overflow-y: hidden; outline: none;"
                                         id="innerContentfield9343div" tabindex="5001">
                                        <div style="margin-left:31px;" id="innerfield9343div" hasadd="false"
                                             hasbrowser="true"><span style="float:none;" name="field9343span"
                                                                     id="field9343span"></span>
                                            <input onblur="__browserNamespace__.setAutocompleteOff(this)" type="text"
                                                   value="" issingle="false" class="e8_browserInputMore ac_input"
                                                   name="field9343__" id="field9343__"
                                                   onpropertychange="wfbrowvaluechange(this,9343)"
                                                   onkeydown="__browserNamespace__.delByBS(event,'field9343__',1,false,{});"
                                                   autocomplete="off" style="width: 17px;"></div>
                                    </div>
                                    <div id="ascrail2001"
                                         style="width: 8px; z-index: 10003; position: absolute; top: 1px; left: 173px; height: 22px; display: none; opacity: 0;">
                                        <div style="position: relative; top: 0px; float: right; width: 8px; height: 0px; background-color: rgb(153, 153, 153); border: none; background-clip: padding-box; border-radius: 5px;"></div>
                                    </div>
                                </div>
                            </div>

                            <script type="text/javascript">
                                window.setTimeout(function () {
                                    var isSingle = false;

                                    var browserBoxId = '';

                                    var browserBox = null;

                                    if (browserBoxId) {

                                        browserBox = jQuery('#' + browserBoxId);

                                    }

                                    if (!browserBox || browserBox.length() == 0) {

                                        browserBox = jQuery('#field9343span').closest('div.e8_os');

                                    }

                                    var _this = browserBox;

                                    if (isSingle == "false" || isSingle == false) {
                                        browserBox.hover(function () {

                                            if (!_this.data("_perfect")) {

                                                _this.find("#outfield9343div").perfectScrollbar();
                                                _this.data("_perfect", true);

                                            }

                                        }, function () {
                                        });

                                    }
                                    ;

                                    browserBox.hover(function () {

                                        if (!_this.data("_autocomplete")) {

                                            __browserNamespace__.hoverShowNameSpan(_this.find("span.e8_showNameClass"));
                                            __browserNamespace__.e8autocomplete({
                                                nameAndId: 'field9343',
                                                inputNameAndId: 'field9343__',
                                                isMustInput: 1,
                                                hasAdd: false,
                                                completeUrl: "javascript:getajaxurl(160,'','','9343','44a0b1')",
                                                isSingle: false,
                                                extraParams: {"_exclude": "getNameAndIdVal"},
                                                row_height: 22,
                                                linkUrl: "/hrm/resource/HrmResource.jsp?id=",
                                                needHidden: false,
                                                sb: {},
                                                _callback: '',
                                                _callbackParams: '',
                                                type: '',
                                                browserBox: ''
                                            });

                                            __browserNamespace__.e8formatInitData({
                                                nameAndId: 'field9343',
                                                name: 'field9343',
                                                isMustInput: 1,
                                                hasInput: true,
                                                browserBox: ''
                                            });

                                            _this.data("_autocomplete", true);

                                        }

                                    }, function () {
                                    });
                                }, 500);</script>
                            <input type="hidden" viewtype="0" id="field9343" name="field9343" value="" __value=""
                                   temptitle="领导"
                                   onpropertychange="try{checkLengthbrow('field9343','field9343span','0','领导','文本长度不能超过','1个中文字符等于3个长度',field9343.getAttribute('viewtype'))}catch(e){}"
                                   _listener="try{checkLengthbrow('field9343','field9343span','0','领导','文本长度不能超过','1个中文字符等于3个长度',field9343.getAttribute('viewtype'))}catch(e){}"
                                   _hasinit="true">
                        </div>
                    </td>
                </tr>
            </table>
            <%} else if (RiChengType.equals("2")) {%>
            <!--部门-->
            <table style="float:right;">
                <tr>
                    <td>
                        部门：
                    </td>
                    <td>
                        <div>
                            <div class="e8_os" style="width:230px;">

                                <div class="e8_innerShow e8_innerShow_button e8_innerShow_button_right30"
                                     style="max-height:66px;"><span class="e8_spanFloat"><span class="e8_browserSpan "><button
                                        class="e8_browflow" id="field7616_browserbtn" type="button"
                                        onclick="onShowBrowser21('7616','/systeminfo/BrowserMain.jsp?url=/hrm/company/MultiDepartmentBrowserByOrder.jsp','/hrm/company/HrmDepartmentDsp.jsp?id=','57',field7616.getAttribute('viewtype'));"></button></span></span>
                                </div>
                                <div class="e8_innerShow e8_innerShowMust" style="max-height:66px;"><span
                                        name="field7616spanimg" class="e8_spanFloat" id="field7616spanimg"></span></div>
                                <div class="e8_outScroll" id="outfield7616div"
                                     style="width: 100%; margin-right: -30px; overflow-y: hidden; outline: none;"
                                     tabindex="5002">
                                    <div class="e8_innerShow e8_innerShowContent"
                                         style="max-height: 66px; margin-right: 30px; overflow-y: hidden; outline: none;"
                                         id="innerContentfield7616div" tabindex="5003">
                                        <div style="margin-left:31px;" id="innerfield7616div" hasadd="false"
                                             hasbrowser="true"><span style="float:none;" name="field7616span"
                                                                     id="field7616span"></span>
                                            <input onblur="__browserNamespace__.setAutocompleteOff(this)" type="text"
                                                   value="" issingle="false" class="e8_browserInputMore ac_input"
                                                   name="field7616__" id="field7616__"
                                                   onpropertychange="wfbrowvaluechange(this,7616)"
                                                   onkeydown="__browserNamespace__.delByBS(event,'field7616__',1,false,{});"
                                                   _hasinit="true" autocomplete="off" style="width: 17px;"></div>
                                    </div>
                                    <div id="ascrail2003"
                                         style="width: 8px; z-index: 10003; position: absolute; top: 50px; left: 193px; height: 22px; display: none; opacity: 0;">
                                        <div style="position: relative; top: 0px; float: right; width: 8px; height: 0px; background-color: rgb(153, 153, 153); border: none; background-clip: padding-box; border-radius: 5px;"></div>
                                    </div>
                                </div>
                            </div>
                            <script type="text/javascript">
                                jQuery('#field7616').val('');
                                window.setTimeout(function () {
                                    var isSingle = false;

                                    var browserBoxId = '';

                                    var browserBox = null;

                                    if (browserBoxId) {

                                        browserBox = jQuery('#' + browserBoxId);

                                    }

                                    if (!browserBox || browserBox.length() == 0) {

                                        browserBox = jQuery('#field7616span').closest('div.e8_os');

                                    }

                                    var _this = browserBox;

                                    if (isSingle == "false" || isSingle == false) {
                                        browserBox.hover(function () {

                                            if (!_this.data("_perfect")) {

                                                _this.find("#outfield7616div").perfectScrollbar();
                                                _this.data("_perfect", true);

                                            }

                                        }, function () {
                                        });

                                    }
                                    ;

                                    browserBox.hover(function () {

                                        if (!_this.data("_autocomplete")) {

                                            __browserNamespace__.hoverShowNameSpan(_this.find("span.e8_showNameClass"));
                                            __browserNamespace__.e8autocomplete({
                                                nameAndId: 'field7616',
                                                inputNameAndId: 'field7616__',
                                                isMustInput: 1,
                                                hasAdd: false,
                                                completeUrl: "javascript:getajaxurl(57)",
                                                isSingle: false,
                                                extraParams: {"_exclude": "getNameAndIdVal"},
                                                row_height: 22,
                                                linkUrl: "#",
                                                needHidden: false,
                                                sb: {},
                                                _callback: '',
                                                _callbackParams: '',
                                                type: '',
                                                browserBox: ''
                                            });

                                            __browserNamespace__.e8formatInitData({
                                                nameAndId: 'field7616',
                                                name: 'field7616',
                                                isMustInput: 1,
                                                hasInput: true,
                                                browserBox: ''
                                            });

                                            _this.data("_autocomplete", true);

                                        }

                                    }, function () {
                                    });
                                }, 500);</script>
                            <input type="hidden" viewtype="0" id="field7616" name="field7616"
                                   ecologyname="uf_LiuLanAnNiu.bm" value="" temptitle="多部门"
                                   onpropertychange="checkLengthbrow('field7616','field7616span','0','多部门','文本长度不能超过','1个中文字符等于3个长度',field7616.getAttribute('viewtype'))"
                                   _hasinit="true">
                        </div>
                    </td>
                </tr>
            </table>
            <%}%>
            <!--end-->
        </td>
    </tr>

    <tr style="height:20px;">
    </tr>
    <tr>
        <td>
            <table style="margin-left:1%;width:98%;border-collapse:collapse;">
                <tr style="height:40px;font-weight:bold;font-size:16px;">
                    <td style="border:1px solid #B8B8B8; width:10%;background-color:#008df6; color:white; text-align:center">
                        日程
                    </td>
                    <td style="border:1px solid #B8B8B8; width:45%;background-color:#008df6; color:white; text-align:center">
                        上午
                    </td>
                    <td style="border:1px solid #B8B8B8; width:45%;background-color:#008df6; color:white; text-align:center">
                        下午
                    </td>
                </tr>
                <%
                    String date_rq = "";
                    String date_week = "";
                    for (int i = 0; i < 7; i++) {
                        if (i == 0) {
                            date_rq = firstDate;
                        } else {
                            date_rq = dateHelp.getNextDay(date_rq, "yyyy-MM-dd");
                        }
                        date_week = dateHelp.getWeekOfDate(date_rq);

                        String searchSql = "";
                        String sqlWhere = "";
                        String sqlWhereOther = "";
                        if (RiChengType.equals("1")) {//领导日程
                            rs.execute("select b.resourceid from HrmRoles a,HrmRoleMembers b where a.id=b.roleid and a.id=" + LingDaoRoleId);
                            while (rs.next()) {
                                sqlWhere += " ','+resourceid+',' like '%," + rs.getString("resourceid") + ",%' or";
                            }
                            if (sqlWhere.length() > 0) {
                                sqlWhere = sqlWhere.substring(0, sqlWhere.length() - 2);
                            }
                        } else if (RiChengType.equals("2")) {//部门日程
                            if (!bm.equals("")) {
                                departmentid = bm;
                            }
                            rs.execute("select id from HrmResource where departmentid in (" + departmentid + ")");
                            while (rs.next()) {
                                sqlWhere += " ','+resourceid+',' like '%," + rs.getString("id") + ",%' or";
                            }
                            //E9没有参会部门字段
//		String newdepmembersBySearch[] = departmentid.split(",");
//		for(int j=0;j<newdepmembersBySearch.length;j++){
//			sqlWhere+=" ','+newdepmembers+',' like '%,"+newdepmembersBySearch[j]+",%' or";
//		}
                            if (sqlWhere.length() > 0) {
                                sqlWhere = sqlWhere.substring(0, sqlWhere.length() - 2);
                            }


                        } else if (RiChengType.equals("3")) {//个人日程
                            sqlWhere += " ','+resourceid+',' like '%," + userid + ",%'";
                        }
                        if (!ld.equals("")) {
                            String resourceSql = "";
                            for (int j = 0; j < ldList.length; j++) {
                                resourceSql += " ','+resourceid+',' like '%," + ldList[j] + ",%' or";
                            }
                            if (!resourceSql.equals("")) {
                                resourceSql = resourceSql.substring(0, resourceSql.length() - 2);
                            }

                            sqlWhereOther += " and (" + resourceSql + ")";
                        }
                %>
                <%if (i % 2 == 0) {%>
                <tr style="background-color:rgb(248,248,248);font-size:12px;">
                        <%}else{%>
                <tr style="font-size:12px;">
                    <%}%>
                    <!--日期-->
                    <td style="border:1px solid #C6C6C6;text-align:center;color:#008df6;">
                        <%=date_week%><br/>
                        <%=date_rq.split("-")[0] + "年" + date_rq.split("-")[1] + "月" + date_rq.split("-")[2] + "日"%>
                    </td>
                    <!--上午-->
                    <td align="left"
                        style="padding-left:5px;padding-right:5px;border:1px solid #C6C6C6;line-height:22px">
                        <%
                            searchSql = "select othermembers,hrmmembers,'' as newdepmembers,a.id,a.meetingid,b.name,a.resourceid,b.begindate,b.begintime,b.enddate,b.endtime,b.address,b.customizeAddress " +
                                    //"(select name from MeetingRoom c where b.address=c.id) meetingroom "
                                    " from workplan a,meeting b where a.meetingid=b.id and b.cancel is null and b.meetingstatus=2 "
                                    + sqlWhereOther
                                    + " and (" + sqlWhere + ")"
                                    + " and ("
                                    + " (a.begindate='" + date_rq + "' and a.begintime<'13:00') or "
                                    + " (a.begindate<'" + date_rq + "' and a.enddate='" + date_rq + "') or "
                                    + " (a.begindate<'" + date_rq + "' and a.enddate>'" + date_rq + "') "
                                    + " ) order by b.begindate+b.begintime,b.enddate+b.endtime ";
                            //out.println(searchSql);
                            BaseBean baseBean = new BaseBean();
                            baseBean.writeLog("searchSql:" + searchSql);
                            rs0.execute(searchSql);
                            while (rs0.next()) {
                                String id = rs0.getString("meetingid");
                                String newhrmmembers = rs0.getString("hrmmembers");//参会人员
                                String newhrmmembersStr = "";
                                if (!newhrmmembers.equals("")) {
//                                    rs1.execute(" select lastname from HrmResource where id in(" + newhrmmembers + ") order by case lastname " +
//                                            " when '傅红岩' then 1" +
//                                            " when '林炯' then 2 " +
//                                            " when '朱民' then 3 " +
//                                            " when '朱云' then 4 " +
//                                            " when '杜松杨' then 5 " +
//                                            " when '项亦男' then 6 " +
//                                            " else 7 " +
//                                            " end " +
//                                            " asc ,charindex(CONVERT(CHAR,id),'" + newhrmmembers + "' ) ");
                                    rs1.execute(" select lastname from HrmResource where id in(" + newhrmmembers + ") order by dsporder ");
                                    new BaseBean().writeLog("newhrmmembersSql:" + " select lastname from HrmResource where id in(" + newhrmmembers + ") order by charindex(CONVERT(CHAR,id),'" + newhrmmembers + "' ) ");
                                    while (rs1.next()) {
                                        newhrmmembersStr += rs1.getString("lastname") + "，";
                                    }
                                }

                                //查询address对应的地址
                                String addressRs = rs0.getString("address");//地址
                                String newAddresStrAll = "";
                                if (!"".equals(addressRs)) {
                                    RecordSet rsAddress = new RecordSet();
                                    String[] addressArray = addressRs.split(",");
                                    StringBuilder sb = new StringBuilder();
                                    sb.append(" select name from MeetingRoom a where 1=1 ");
                                    sb.append(" and ( ");
                                    for (int j = 0; j < addressArray.length; j++) {
                                        if (j > 0) {
                                            sb.append(" or ");
                                        }
                                        sb.append(" (a.id = ").append(addressArray[j]).append(") ");
                                    }
                                    sb.append("  ) ");
                                    rsAddress.executeQuery(sb.toString());
                                    while (rsAddress.next()) {
                                        newAddresStrAll += rsAddress.getString("name") + "，";
                                    }
                                    if (!"".equals(newAddresStrAll)) {
                                        newAddresStrAll = newAddresStrAll.substring(0, newAddresStrAll.length() - 1);
                                    }
                                }


                                String newdepmembers = rs0.getString("newdepmembers");//参会部门
                                rs1.execute("select departmentname from HrmDepartment where id in(" + newdepmembers + ") order by CHARINDEX(CONVERT(CHAR,id),'" + newdepmembers + "' )");
                                String newdepmembersStr = "";
                                while (rs1.next()) {
                                    newdepmembersStr += rs1.getString("departmentname") + "，";
                                }

                                String othermembers = rs0.getString("othermembers");//其他参会人员
                                String lastname = newhrmmembersStr + newdepmembersStr + othermembers;
                                String address = rs0.getString("customizeAddress") + newAddresStrAll;
                                if (lastname.endsWith("，")) {
                                    lastname = lastname.substring(0, lastname.length() - 1);
                                }
                                String begintime = rs0.getString("begintime");
                                String endtime = rs0.getString("endtime");
                                if (!rs0.getString("begindate").equals(date_rq) && !rs0.getString("enddate").equals(date_rq)) {
                                    begintime = "09:00";
                                    endtime = "13:00";
                                } else if (!rs0.getString("begindate").equals(date_rq) && rs0.getString("enddate").equals(date_rq)) {
                                    begintime = "09:00";
                                    if (Integer.parseInt(endtime.replace(":", "")) > 1300) {
                                        endtime = "13:00";
                                    }
                                } else if (rs0.getString("begindate").equals(date_rq) && !rs0.getString("enddate").equals(date_rq)) {
                                    endtime = "13:00";
                                } else if (rs0.getString("begindate").equals(date_rq) && rs0.getString("enddate").equals(date_rq)) {
                                    if (Integer.parseInt(endtime.replace(":", "")) > 1300) {
                                        endtime = "13:00";
                                    }
                                }
                        %>
                        <%=begintime%> - <%=endtime%> <span style="color:#008df6;cursor: pointer;"
                                                            onclick="openRiCheng(<%=id%>)"><%=rs0.getString("name")%></span>
                        [<%=lastname%>]<%if (!address.equals("")) {%> [<%=address%>]<%}%>
                        <br/>
                        <%}%>
                    </td>
                    <!--下午-->
                    <td align="left"
                        style="padding-left:5px;padding-right:5px;border:1px solid #C6C6C6;line-height:22px">
                        <%
                            searchSql = "select othermembers,hrmmembers,'' as newdepmembers,a.id,a.meetingid,b.name,a.resourceid,b.begindate,b.begintime,b.enddate,b.endtime,b.address,b.customizeAddress " +
                                    //"(select name from MeetingRoom c where b.address=c.id) meetingroom "
                                    " from workplan a,meeting b where a.meetingid=b.id and b.cancel is null and b.meetingstatus=2 "
                                    + sqlWhereOther
                                    + " and (" + sqlWhere + ")"
                                    + " and ("
                                    + " (a.begindate='" + date_rq + "' and a.begintime>'13:00') or"
                                    + " (a.begindate='" + date_rq + "' and a.enddate>'" + date_rq + "') or"
                                    + " (a.enddate='" + date_rq + "' and a.endtime>'13:00') or"
                                    + " (a.begindate<'" + date_rq + "' and a.enddate>'" + date_rq + "')"
                                    + " ) order by b.begindate+b.begintime,b.enddate+b.endtime ";
//out.println(searchSql);					
                            rs0.execute(searchSql);
                            while (rs0.next()) {
                                String id = rs0.getString("meetingid");
                                String newhrmmembers = rs0.getString("hrmmembers");//参会人员
                                String newhrmmembersStr = "";
                                if (!newhrmmembers.equals("")) {
//                                    rs1.execute("select lastname from HrmResource where id in(" + newhrmmembers + ") order by case lastname " +
//                                            "when '傅红岩' then 1 " +
//                                            "when '林炯' then 2  " +
//                                            "when '朱民' then 3 " +
//                                            "when '杜松杨' then 4 " +
//                                            "when '项亦男' then 5 " +
//                                            "else 6 " +
//                                            "end " +
//                                            "asc ,CHARINDEX(CONVERT(CHAR,id),'" + newhrmmembers + "' )");
                                    rs1.execute("select lastname from HrmResource where id in(" + newhrmmembers + ") order by dsporder");
                                    while (rs1.next()) {
                                        newhrmmembersStr += rs1.getString("lastname") + "，";
                                    }
                                }
                                //查询address对应的地址
                                String addressRs = rs0.getString("address");//地址
                                String newAddresStrAll = "";
                                if (!"".equals(addressRs)) {
                                    RecordSet rsAddress = new RecordSet();
                                    String[] addressArray = addressRs.split(",");
                                    StringBuilder sb = new StringBuilder();
                                    sb.append(" select name from MeetingRoom a where 1=1 ");
                                    sb.append(" and ( ");
                                    for (int j = 0; j < addressArray.length; j++) {
                                        if (j > 0) {
                                            sb.append(" or ");
                                        }
                                        sb.append(" (a.id = ").append(addressArray[j]).append(") ");
                                    }
                                    sb.append("  ) ");
                                    rsAddress.executeQuery(sb.toString());
                                    while (rsAddress.next()) {
                                        newAddresStrAll += rsAddress.getString("name") + "，";
                                    }
                                    if (!"".equals(newAddresStrAll)) {
                                        newAddresStrAll = newAddresStrAll.substring(0, newAddresStrAll.length() - 1);
                                    }
                                }
                                String newdepmembers = rs0.getString("newdepmembers");//参会部门
                                rs1.execute("select departmentname from HrmDepartment where id in(" + newdepmembers + ") order by CHARINDEX(CONVERT(CHAR,id),'" + newdepmembers + "' )");
                                String newdepmembersStr = "";
                                while (rs1.next()) {
                                    newdepmembersStr += rs1.getString("departmentname") + "，";
                                }

                                String othermembers = rs0.getString("othermembers");//其他参会人员
                                String lastname = newhrmmembersStr + newdepmembersStr + othermembers;
                                String address = rs0.getString("customizeAddress") + newAddresStrAll;
                                if (lastname.endsWith("，")) {
                                    lastname = lastname.substring(0, lastname.length() - 1);
                                }

                                String begintime = rs0.getString("begintime");
                                String endtime = rs0.getString("endtime");
                                if (!rs0.getString("begindate").equals(date_rq) && !rs0.getString("enddate").equals(date_rq)) {
                                    begintime = "13:00";
                                    endtime = "17:00";
                                } else if (rs0.getString("begindate").equals(date_rq) && !rs0.getString("enddate").equals(date_rq)) {
                                    endtime = "17:00";
                                    if (Integer.parseInt(begintime.split(":")[0]) < 13) {
                                        begintime = "13:00";
                                    }
                                } else if (!rs0.getString("begindate").equals(date_rq) && rs0.getString("enddate").equals(date_rq)) {
                                    begintime = "13:00";
                                } else if (rs0.getString("begindate").equals(date_rq) && rs0.getString("enddate").equals(date_rq)) {
                                    if (Integer.parseInt(begintime.split(":")[0]) < 13) {
                                        begintime = "13:00";
                                    }
                                }
                        %>
                        <%=begintime%> - <%=endtime%> <span style="color:#008df6;cursor: pointer;"
                                                            onclick="openRiCheng(<%=id%>)"><%=rs0.getString("name")%></span>
                        [<%=lastname%>]<%if (!address.equals("")) {%> [<%=address%>]<%}%>
                        <br/>
                        <%}%>
                    </td>
                </tr>
                <%}%>
            </table>
        </td>
    </tr>
</table>
<script language=javascript>
    function search() {
        if ("<%=RiChengType%>" != "3") {
            jQuery("#trid").toggle();
        }
    }

    function shuaxin() {
        window.location.reload();
    }

    function p(s) {
        return s < 10 ? '0' + s : s;
    }

    function newMeeting() {
        var myDate = new Date();
        //获取当前年
        var year = myDate.getFullYear();
        //获取当前月
        var month = myDate.getMonth() + 1;
        //获取当前日
        var date = myDate.getDate();
        var now = year + '-' + p(month) + "-" + p(date);

        if (window.top.Dialog) {
            diag_vote = new window.top.Dialog();
        } else {
            diag_vote = new Dialog();
        }
        diag_vote.currentWindow = window;
        diag_vote.Width = 800;
        diag_vote.Height = 550;
        diag_vote.Modal = true;
        diag_vote.maxiumnable = true;
        diag_vote.checkDataChange = false;
        diag_vote.Title = "<%=SystemEnv.getHtmlLabelName(82,user.getLanguage())%><%=SystemEnv.getHtmlLabelName(2103,user.getLanguage())%>";
        diag_vote.URL = "/meeting/data/NewMeetingTab.jsp?startdate=" + now + "&enddate=" + now + "&starttime=06:00&endtime=06:00";
        diag_vote.show();
        //window.open("../meeting/data/NewMeeting.jsp");
    }

    function tiaoZhuan(obj) {
        window.location.href = "../RiChengList.jsp?RiChengType=" + obj + "&firstDate=<%=firstDate%>";
    }

    function onSearch() {
        window.location.href = "../RiChengList.jsp?weekType=0&firstDate=<%=firstDate%>&RiChengType=<%=RiChengType%>";
    }

    function up() {
        window.location.href = "../RiChengList.jsp?weekType=1&firstDate=<%=firstDate%>&RiChengType=<%=RiChengType%>";
    }

    function thisWeek() {
        window.location.href = "../RiChengList.jsp?weekType=0&RiChengType=<%=RiChengType%>";
    }

    function down() {
        window.location.href = "../RiChengList.jsp?weekType=2&firstDate=<%=firstDate%>&RiChengType=<%=RiChengType%>";
    }

    function download() {
        window.open("../ExportRiChengList.jsp?ld=<%=ld%>&bm=<%=bm%>&firstDate=<%=firstDate%>&RiChengType=<%=RiChengType%>&userid=<%=userid%>&departmentid=<%=departmentid%>");
    }

    function openRiCheng(obj) {
        if (window.top.Dialog) {
            diag_vote = new window.top.Dialog();
        } else {
            diag_vote = new Dialog();
        }
        diag_vote.currentWindow = window;
        diag_vote.Width = 800;
        diag_vote.Height = 550;
        diag_vote.Modal = true;
        diag_vote.maxiumnable = true;
        diag_vote.checkDataChange = false;
        diag_vote.Title = "查看会议";
        diag_vote.URL = "/meeting/data/ViewMeetingTab.jsp?meetingid=" + obj;
        diag_vote.show();
        //window.open("../meeting/data/ViewMeetingTab.jsp?meetingid="+obj);
    }

    function onShowBrowser2_old(id, url, linkurl, type1, ismand, funFlag) {
        var id1 = null;

        if (type1 == 9 && false) {
            if (wuiUtil.isNotNull(funFlag) && funFlag == 3) {
                url = "/systeminfo/BrowserMain.jsp?url=/docs/docs/DocBrowser.jsp"
            } else {
                url = "/systeminfo/BrowserMain.jsp?url=/docs/docs/DocBrowserWord.jsp";
            }
        }
        if (type1 == 23) {
            url += "?billid=-48";
        }
        if (type1 == 2 || type1 == 19) {
            spanname = "field" + id + "span";
            inputname = "field" + id;

            if (type1 == 2) {
                onFlownoShowDate(spanname, inputname, ismand);
            } else {
                onWorkFlowShowTime(spanname, inputname, ismand);
            }
        } else {
            if (type1 != 256 && type1 != 257 && type1 != 162 && type1 != 171 && type1 != 152 && type1 != 142 && type1 != 135 && type1 != 17 && type1 != 18 && type1 != 27 && type1 != 37 && type1 != 56 && type1 != 57 && type1 != 65 && type1 != 165 && type1 != 166 && type1 != 167 && type1 != 168 && type1 != 4 && type1 != 167 && type1 != 164 && type1 != 169 && type1 != 170 && type1 != 194) {
                if (wuiUtil.isNotNull(funFlag) && funFlag == 3) {
                    id1 = window.showModalDialog(url, "", "dialogWidth=550px;dialogHeight=550px");
                } else {
                    if (type1 == 161 || type1 == 224 || type1 == 225 || type1 == 226 || type1 == 227) {
                        id1 = window.showModalDialog(url + "|" + id, window, "dialogWidth=550px;dialogHeight=550px");
                    } else {
                        id1 = window.showModalDialog(url, window, "dialogWidth=550px;dialogHeight=550px");
                    }
                }
            } else {
                if (type1 == 135) {
                    tmpids = $GetEle("field" + id).value;
                    id1 = window.showModalDialog(url + "?projectids=" + tmpids, "", "dialogWidth=550px;dialogHeight=550px");
                } else if (type1 == 4 || type1 == 164 || type1 == 169 || type1 == 170 || type1 == 194) {
                    tmpids = $GetEle("field" + id).value;
                    id1 = window.showModalDialog(url + "?selectedids=" + tmpids, "", "dialogWidth=550px;dialogHeight=550px");
                } else if (type1 == 37) {
                    tmpids = $GetEle("field" + id).value;
                    id1 = window.showModalDialog(url + "?documentids=" + tmpids, "", "dialogWidth=550px;dialogHeight=550px");
                } else if (type1 == 142) {
                    tmpids = $GetEle("field" + id).value;
                    id1 = window.showModalDialog(url + "?receiveUnitIds=" + tmpids, "", "dialogWidth=550px;dialogHeight=550px");
                } else if (type1 == 162) {
                    tmpids = $GetEle("field" + id).value;

                    if (wuiUtil.isNotNull(funFlag) && funFlag == 3) {
                        url = url + "&beanids=" + tmpids;
                        url = url.substring(0, url.indexOf("url=") + 4) + escape(url.substr(url.indexOf("url=") + 4));
                        id1 = window.showModalDialog(url, "", "dialogWidth=700px;dialogHeight=550px");
                    } else {
                        url = url + "|" + id + "&beanids=" + tmpids;
                        url = url.substring(0, url.indexOf("url=") + 4) + escape(url.substr(url.indexOf("url=") + 4));
                        id1 = window.showModalDialog(url, window, "dialogWidth=700px;dialogHeight=550px");
                    }
                } else if (type1 == 256 || type1 == 257) {
                    tmpids = $GetEle("field" + id).value;
                    url = url + "&selectedids=" + tmpids;
                    url = url.substring(0, url.indexOf("url=") + 4) + escape(url.substr(url.indexOf("url=") + 4));
                    id1 = window.showModalDialog(url, window, "dialogWidth=550px;dialogHeight=550px");
                } else if (type1 == 165 || type1 == 166 || type1 == 167 || type1 == 168) {
                    index = (id + "").indexOf("_");
                    if (index != -1) {
                        tmpids = uescape("?isdetail=1&isbill=1&fieldid=" + id.substring(0, index) + "&resourceids=" + $GetEle("field" + id).value + "&selectedids=" + $GetEle("field" + id).value);
                        id1 = window.showModalDialog(url + tmpids, "", "dialogWidth=550px;dialogHeight=550px");
                    } else {
                        tmpids = uescape("?fieldid=" + id + "&isbill=1&resourceids=" + $GetEle("field" + id).value + "&selectedids=" + $GetEle("field" + id).value);
                        id1 = window.showModalDialog(url + tmpids, "", "dialogWidth=550px;dialogHeight=550px");
                    }
                } else {
                    tmpids = $GetEle("field" + id).value;
                    id1 = window.showModalDialog(url + "?resourceids=" + tmpids, "", "dialogWidth=550px;dialogHeight=550px");
                }
            }
            if (id1 != undefined && id1 != null) {
                if (type1 == 171 || type1 == 152 || type1 == 142 || type1 == 135 || type1 == 17 || type1 == 18 || type1 == 27 || type1 == 37 || type1 == 56 || type1 == 57 || type1 == 65 || type1 == 166 || type1 == 168 || type1 == 170 || type1 == 194) {
                    if (wuiUtil.getJsonValueByIndex(id1, 0) != "") {
                        var resourceids = wuiUtil.getJsonValueByIndex(id1, 0);
                        var resourcename = wuiUtil.getJsonValueByIndex(id1, 1);
                        var sHtml = ""

                        if (resourceids != "" && resourceids.indexOf(",") == 0) {
                            resourceids = resourceids.substr(1);
                        }
                        if (resourcename != "" && resourcename.indexOf(",") == 0) {
                            resourcename = resourcename.substr(1);
                        }

                        var resourceIdArray = resourceids.split(",");
                        var resourceNameArray = resourcename.split(",");
                        for (var _i = 0; _i < resourceIdArray.length; _i++) {
                            var curid = resourceIdArray[_i];
                            var curname = resourceNameArray[_i];
                            if (linkurl == "/hrm/resource/HrmResource.jsp?id=") {
                                sHtml += "<a href=javaScript:openhrm(" + curid + "); onclick='pointerXY(event);'>" + curname + "</a>&nbsp";
                            } else {
                                sHtml += "<a href=" + linkurl + curid + " target=_blank>" + curname + "</a>&nbsp";
                            }
                        }
                        $GetEle("field" + id + "span").innerHTML = sHtml;
                        $GetEle("field" + id).value = resourceids;
                    } else {
                        if (ismand == 0) {
                            $GetEle("field" + id + "span").innerHTML = "";
                        } else {
                            $GetEle("field" + id + "span").innerHTML = "<img src='/images/BacoError_wev8.gif' align=absmiddle>";
                        }
                        $GetEle("field" + id).value = "";
                    }

                } else {
                    if (wuiUtil.getJsonValueByIndex(id1, 0) != "") {
                        if (type1 == 162) {
                            var ids = wuiUtil.getJsonValueByIndex(id1, 0);
                            var names = wuiUtil.getJsonValueByIndex(id1, 1);
                            var descs = wuiUtil.getJsonValueByIndex(id1, 2);
                            var href = wuiUtil.getJsonValueByIndex(id1, 3);
                            sHtml = ""
                            if (ids.indexOf(",") == 0) {
                                ids = ids.substr(1);
                                names = names.substr(1);
                                descs = descs.substr(1);
                            }
                            $GetEle("field" + id).value = ids;
                            var idArray = ids.split(",");
                            var nameArray = names.split("~~WEAVERSplitFlag~~");
                            if (nameArray.length < idArray.length) {
                                nameArray = names.split(",");
                            }
                            var descArray = descs.split(",");
                            for (var _i = 0; _i < idArray.length; _i++) {
                                var curid = idArray[_i];
                                var curname = nameArray[_i];
                                var curdesc = descArray[_i];
                                //sHtml += "<a title='" + curdesc + "' >" + curname + "</a>&nbsp";
                                if (href == '') {
                                    sHtml += "<a title='" + curdesc + "' >" + curname + "</a>&nbsp";
                                } else {
                                    sHtml += "<a title='" + curdesc + "' href='" + href + curid + "' target='_blank'>" + curname + "</a>&nbsp";
                                }
                            }

                            $GetEle("field" + id + "span").innerHTML = sHtml;
                            return;
                        }
                        if (type1 == 161) {
                            var ids = wuiUtil.getJsonValueByIndex(id1, 0);
                            var names = wuiUtil.getJsonValueByIndex(id1, 1);
                            var descs = wuiUtil.getJsonValueByIndex(id1, 2);
                            var href = wuiUtil.getJsonValueByIndex(id1, 3);
                            $GetEle("field" + id).value = ids;
                            //sHtml = "<a title='" + descs + "'>" + names + "</a>&nbsp";
                            if (href == '') {
                                sHtml = "<a title='" + descs + "'>" + names + "</a>&nbsp";
                            } else {
                                sHtml = "<a title='" + descs + "' href='" + href + ids + "' target='_blank'>" + names + "</a>&nbsp";
                            }
                            $GetEle("field" + id + "span").innerHTML = sHtml
                            return;
                        }
                        if (type == 257) {
                            var ids = wuiUtil.getJsonValueByIndex(id1, 0);
                            var names = wuiUtil.getJsonValueByIndex(id1, 1);
                            var sHtml = ""
                            var idArray = ids.split(",");
                            var nameArray = names.split(",");
                            for (var _i = 0; _i < idArray.length; _i++) {
                                var curid = idArray[_i];
                                var curname = nameArray[_i];
                                sHtml += curname + "&nbsp";
                            }
                            $GetEle("field" + id).value = ids;
                            $GetEle("field" + id + "span").innerHTML = sHtml;
                            return;
                        }
                        if (type1 == 9 && false) {
                            tempid = wuiUtil.getJsonValueByIndex(id1, 0);
                            $GetEle("field" + id + "span").innerHTML = "<a href='#' onclick='createDoc(" + id + ", " + tempid + ", 1)'>" + wuiUtil.getJsonValueByIndex(id1, 1) + "</a><button type=button id='createdoc' style='display:none' class=AddDocFlow onclick=createDoc(" + id + ", " + tempid + ",1)></button>";
                        } else {
                            if (linkurl == "") {
                                $GetEle("field" + id + "span").innerHTML = wuiUtil.getJsonValueByIndex(id1, 1);
                            } else {
                                if (linkurl == "/hrm/resource/HrmResource.jsp?id=") {
                                    $GetEle("field" + id + "span").innerHTML = "<a href=javaScript:openhrm(" + wuiUtil.getJsonValueByIndex(id1, 0) + "); onclick='pointerXY(event);'>" + wuiUtil.getJsonValueByIndex(id1, 1) + "</a>&nbsp";
                                } else {
                                    $GetEle("field" + id + "span").innerHTML = "<a href=" + linkurl + wuiUtil.getJsonValueByIndex(id1, 0) + " target='_new'>" + wuiUtil.getJsonValueByIndex(id1, 1) + "</a>";
                                }
                            }
                        }
                        $GetEle("field" + id).value = wuiUtil.getJsonValueByIndex(id1, 0);
                        if (type1 == 9 && false) {
                            $GetEle("CreateNewDoc").innerHTML = "";
                        }
                    } else {
                        if (ismand == 0) {
                            $GetEle("field" + id + "span").innerHTML = "";
                        } else {
                            $GetEle("field" + id + "span").innerHTML = "<img src='/images/BacoError_wev8.gif' align=absmiddle>"
                        }
                        $GetEle("field" + id).value = "";
                        if (type1 == 9 && false) {
                            $GetEle("createNewDoc").innerHTML = "<button type=button id='createdoc' class=AddDocFlow onclick=createDoc(" + id + ",'','1') title='新建'>新建</button>";
                        }
                    }
                }
            }
        }
    }

    //多人力，单部门
    function onShowBrowser21(id, url, linkurl, type1, ismand, funFlag, _fieldStr) {
        //if(false){// 不适应旧的browser   旧的browser方法在Google下会出现异常 Google新版本不支持window.showModalDialog --mcw 2014-11-3
        if (false) {
            onShowBrowser2_old(id, url, linkurl, type1, ismand, funFlag);
        } else {
            var ismast = 1;//2：必须输：可编辑
            if (ismand == 0) {
                ismast = 1;
            } else {
                ismast = 2;
            }
            var dialogurl = url;

            if (_fieldStr == null) {
                _fieldStr = "field";
            }

            var id1 = null;

            if (type1 == 23) {
                url += "?billid=-78";
            }
            if (type1 == 224 || type1 == 225 || type1 == 226 || type1 == 227) {
                if (url.indexOf("|") == -1) {
                    url += "|" + id;
                } else {
                    var index = url.indexOf("|");
                    url = url.substring(0, index);
                    url += "|" + id;
                }
                if (url.indexOf("-") == -1) {
                    if (id.split("_")[1]) {
                        //zzl-拼接行号
                        url += "_" + id.split("_")[1];
                    }
                }
                dialogurl = url;
            }


            if (type1 == 2 || type1 == 19) {
                spanname = _fieldStr + id + "span";
                inputname = _fieldStr + id;
                var isMustInput = jQuery("#" + inputname).attr("isMustInput");
                if (isMustInput) {
                    if (isMustInput == "2") {//必填
                        ismand = 1;
                    } else if (isMustInput == "1") {//可编辑
                        ismand = 0;
                    }
                }
                if (type1 == 2) {
                    onFlownoShowDate(spanname, inputname, ismand);
                } else {
                    onWorkFlowShowTime(spanname, inputname, ismand);
                }
            } else {
                if (type1 != 256 && type1 != 257 && type1 != 162 && type1 != 171 && type1 != 152 && type1 != 142 && type1 != 135 && type1 != 17 && type1 != 18 && type1 != 27 && type1 != 37 && type1 != 56 && type1 != 57 && type1 != 65 && type1 != 165 && type1 != 166 && type1 != 167 && type1 != 168 && type1 != 4 && type1 != 167 && type1 != 164 && type1 != 169 && type1 != 170 && type1 != 194) {
                    if (wuiUtil.isNotNull(funFlag) && funFlag == 3) {
                        //id1 = window.showModalDialog(url, "", "dialogWidth=550px;dialogHeight=550px");
                    } else {
                        if (type1 == 161) {
                            //id1 = window.showModalDialog(url + "|" + id, window, "dialogWidth=550px;dialogHeight=550px");
                            dialogurl = url + "|" + id;
                        }
                    }
                } else {

                    if (type1 == 135) {
                        tmpids = $GetEle(_fieldStr + id).value;
                        dialogurl = url + "?projectids=" + tmpids;
                        //id1 = window.showModalDialog(url + "?projectids=" + tmpids, "", "dialogWidth=550px;dialogHeight=550px");
                        //} else if (type1 == 4 || type1 == 167 || type1 == 164 || type1 == 169 || type1 == 170) {
                        //type1 = 167 分权单部分部 不应该包含在这里ypc 2012-09-06 修改
                    } else if (type1 == 4 || type1 == 164 || type1 == 169 || type1 == 170 || type1 == 194) {
                        tmpids = $GetEle(_fieldStr + id).value;
                        dialogurl = url + "?selectedids=" + tmpids;
                        //id1 = window.showModalDialog(url + "?selectedids=" + tmpids, "", "dialogWidth=550px;dialogHeight=550px");
                    } else if (type1 == 37) {
                        tmpids = $GetEle(_fieldStr + id).value;
                        dialogurl = url + "?documentids=" + tmpids;
                        //id1 = window.showModalDialog(url + "?documentids=" + tmpids, "", "dialogWidth=550px;dialogHeight=550px");
                    } else if (type1 == 142) {
                        tmpids = $GetEle(_fieldStr + id).value;
                        dialogurl = url + "?receiveUnitIds=" + tmpids;
                        //id1 = window.showModalDialog(url + "?receiveUnitIds=" + tmpids, "", "dialogWidth=550px;dialogHeight=550px");
                    } else if (type1 == 162) {
                        tmpids = $GetEle(_fieldStr + id).value;

                        if (wuiUtil.isNotNull(funFlag) && funFlag == 3) {
                            url = url + "&beanids=" + tmpids;
                            url = url.substring(0, url.indexOf("url=") + 4) + escape(url.substr(url.indexOf("url=") + 4));
                            dialogurl = url;
                            //id1 = window.showModalDialog(url, "", "dialogWidth=550px;dialogHeight=550px");
                        } else {
                            url = url + "|" + id + "&beanids=" + tmpids;
                            url = url.substring(0, url.indexOf("url=") + 4) + escape(url.substr(url.indexOf("url=") + 4));
                            dialogurl = url;
                            //id1 = window.showModalDialog(url, window, "dialogWidth=550px;dialogHeight=550px");
                        }
                    } else if (type1 == 256 || type1 == 257) {
                        tmpids = $GetEle(_fieldStr + id).value;
                        var url1 = "/formmode/browser/CommonBrowserSqlAjax.jsp";
                        var treerootnode = "";
                        jQuery.ajax({
                            url: url1,
                            type: "post",
                            processData: false,
                            data: "fieldid=" + id,
                            dataType: "json",
                            async: false,//改为同步
                            success: function (msg) {
                                if (msg != null) {
                                    var fhtm = msg.fieldHtmlTypeMap;
                                    //sqlwhere = msg.sqlwhere.sql;
                                    for (var k in fhtm) {
                                        var field_id = k;
                                        var field_type = fhtm[field_id].split("_")[0];
                                        var type = fhtm[field_id].split("_")[1];
                                        var vv = document.getElementById("field" + field_id);
                                        var _value = "";
                                        if (vv) {
                                            if (field_type == '2') {//textarea
                                                _value = vv.innerHTML;
                                            } else if (field_type == '4') {//checkbox
                                                if (vv.checked) {
                                                    _value = "1";
                                                } else {
                                                    _value = "0";
                                                }
                                            } else if (field_type == '5') {//select
                                                var _value_index = vv.selectedIndex;
                                                _value = vv.options[_value_index].value;
                                            } else if (field_type == '1' && (type == '2' || type == '3' || type == '4' || type == '5')) {
                                                if (vv.value == '') {
                                                    _value = '';
                                                } else {
                                                    _value = vv.value;
                                                }
                                            } else {
                                                _value = vv.value;
                                                if (type == '256' || type == '257') {
                                                    var values = _value.split(",");
                                                    var splitValue = "";
                                                    for (var i = 0; i < values.length; i++) {
                                                        var index = values[i].indexOf("_") + 1;
                                                        splitValue += values[i].substring(index) + ",";
                                                    }
                                                    if (splitValue != null && splitValue != '') {
                                                        splitValue = splitValue.substring(0, splitValue.length - 1);
                                                        _value = splitValue;
                                                    }
                                                }

                                            }
                                            if (_value != null && _value != '') {
                                                treerootnode += _value + ",";
                                            }

                                        }
                                    }
                                }
                            }
                        });
                        //执行js
                        if (typeof (getTreerootNode) == 'function') {
                            try {
                                var json = getTreerootNode();
                                var treeNode = json[_fieldStr + id];
                                if (treeNode != undefined) {
                                    treerootnode += treeNode;
                                }

                            } catch (e) {
                                alert('函数getTreerootNode执行异常:' + e.description);
                            }
                        } else {
                            if (treerootnode != null && treerootnode != '') {
                                treerootnode = treerootnode.substring(0, treerootnode.length - 1);
                            }
                        }
                        url = url + "_" + type1 + "&selectedids=" + tmpids + "&treerootnode=" + treerootnode;
                        url = url.substring(0, url.indexOf("url=") + 4) + escape(url.substr(url.indexOf("url=") + 4));
                        dialogurl = url;
                        //id1 = window.showModalDialog(url, window, "dialogWidth=550px;dialogHeight=550px");
                    } else if (type1 == 165 || type1 == 166 || type1 == 167 || type1 == 168) {
                        index = (id + "").indexOf("_");
                        if (index != -1) {
                            tmpids = uescape("?isdetail=1&isbill=1&fieldid=" + id.substring(0, index) + "&resourceids=" + $GetEle(_fieldStr + id).value + "&selectedids=" + $GetEle(_fieldStr + id).value);
                            dialogurl = url + tmpids;
                            //id1 = window.showModalDialog(url + tmpids, "", "dialogWidth=550px;dialogHeight=550px");
                        } else {
                            tmpids = uescape("?fieldid=" + id + "&isbill=1&resourceids=" + $GetEle(_fieldStr + id).value + "&selectedids=" + $GetEle(_fieldStr + id).value);
                            dialogurl = url + tmpids;
                            //把此行的dialogWidth=550px; 改为600px
                            //id1 = window.showModalDialog(url + tmpids, "", "dialogWidth=600px;dialogHeight=550px");
                        }
                    } else {
                        tmpids = $GetEle(_fieldStr + id).value;
                        dialogurl = url + "?resourceids=" + tmpids;
                        //id1 = window.showModalDialog(url + "?resourceids=" + tmpids, "", "dialogWidth=550px;dialogHeight=550px");
                    }
                }

                var otherParm = "";
                var formmodefieldid = "";
                if (type1 == 161 || type1 == 162) {//自定义单选和自定义多选解析sqlwhere和sqlcondition
                    var url1 = "/formmode/browser/CommonBrowserSqlAjax.jsp";
                    var sqlwhere = "";
                    var sqlcondition = "";
                    jQuery.ajax({
                        url: url1,
                        type: "post",
                        processData: false,
                        data: "fieldid=" + id,
                        dataType: "json",
                        async: false,//改为同步
                        success: function (msg) {
                            var fhtm = msg.fieldHtmlTypeMap;
                            if (msg.formmodefieldid) {
                                formmodefieldid = msg.formmodefieldid;
                            }
                            if (msg.sqlwhere) {
                                sqlwhere = msg.sqlwhere.sql;
                                for (var k in fhtm) {
                                    var field_id = k;
                                    var field_type = fhtm[field_id].split("_")[0];
                                    var type = fhtm[field_id].split("_")[1];
                                    var vv = document.getElementById("field" + field_id);
                                    var _value = "";
                                    if (vv) {
                                        if (field_type == '2') {//textarea
                                            _value = vv.innerHTML;
                                        } else if (field_type == '4') {//checkbox
                                            if (vv.checked) {
                                                _value = "1";
                                            } else {
                                                _value = "0";
                                            }
                                        } else if (field_type == '5') {//select
                                            var _value_index = vv.selectedIndex;
                                            _value = vv.options[_value_index].value;
                                        } else if (field_type == '1' && (type == '2' || type == '3' || type == '4' || type == '5')) {
                                            if (vv.value == '') {
                                                _value = '';
                                            } else {
                                                _value = vv.value;
                                            }
                                        } else {
                                            _value = vv.value;
                                        }
                                        //sqlwhere = sqlwhere.replace("$"+field_id+"$",_value);
                                        otherParm += "&" + field_id + "=" + _value;
                                    }
                                }
                            }
                            if (msg.sqlcondition) {
                                for (var k in msg.sqlcondition) {
                                    var v = msg.sqlcondition[k];
                                    var field_id = v;
                                    if (field_id.indexOf("-") > -1 && field_id.split("-").length == 2) {
                                        var field_id1 = field_id.split("-")[0];
                                        var field_type1 = fhtm[field_id1].split("_")[0];
                                        var type1 = fhtm[field_id1].split("_")[1];
                                        var vv1 = document.getElementById("field" + field_id1);
                                        var field_id2 = field_id.split("-")[1];
                                        var field_type2 = fhtm[field_id2].split("_")[0];
                                        var type2 = fhtm[field_id2].split("_")[1];
                                        var vv2 = document.getElementById("field" + field_id2);
                                        if (field_type1 == '1' && field_type2 == '1') {
                                            if ((type1 == '2' || type1 == '3' || type1 == '4' || type1 == '5')
                                                && (type1 == type2)) {
                                                if (vv1 && vv2) {
                                                    var _value1 = vv1.value;
                                                    var _value2 = vv2.value;
                                                    //sqlcondition += " {&} "+k+"="+_value1+"-"+_value2;
                                                    otherParm += "&" + k + "=" + _value1 + "-" + _value2;
                                                }
                                            }
                                        }
                                    } else {
                                        var field_type = fhtm[field_id].split("_")[0];
                                        var type = fhtm[field_id].split("_")[1];
                                        var vv = document.getElementById("field" + field_id);
                                        if (vv) {
                                            if (field_type == '2') {//textarea
                                                var _value = vv.innerHTML;
                                                sqlcondition += " {&} " + k + "=" + _value + "";
                                            } else if (field_type == '4') {//checkbox
                                                if (vv.checked) {
                                                    //sqlcondition += " {&} "+k+"=1";
                                                    otherParm += "&" + k + "=1";
                                                } else {
                                                    //sqlcondition += " {&} "+k+"=";
                                                    otherParm += "&" + k + "=";
                                                }
                                            } else if (field_type == '5') {//select
                                                var _value_index = vv.selectedIndex;
                                                var _value = vv.options[_value_index].value;
                                                //sqlcondition += " {&} "+k+"="+_value;
                                                otherParm += "&" + k + "=" + _value;
                                            } else {
                                                var _value = vv.value;
                                                //sqlcondition += " {&} "+k+"="+_value;
                                                otherParm += "&" + k + "=" + _value;
                                            }
                                            i++;
                                        }
                                    }
                                }
                            }
                        }
                    });
                    if (otherParm != "") {
                        dialogurl += encodeURI(otherParm);
                    }
                    dialogurl += "&formmodefieldid=" + formmodefieldid;
                }

                var dialog = new window.top.Dialog();
                dialog.currentWindow = window;
                //dialog.callbackfunParam = null;
                dialog.URL = dialogurl;
                dialog.callbackfun = function (paramobj, id1) {
                    if (id1 != undefined && id1 != null) {
                        searchBuMen(wuiUtil.getJsonValueByIndex(id1, 0), wuiUtil.getJsonValueByIndex(id1, 1));//add by zp
                        return false;
                        if (type1 == 171 || type1 == 152 || type1 == 142 || type1 == 135 || type1 == 17 || type1 == 18 || type1 == 27 || type1 == 37 || type1 == 56 || type1 == 57 || type1 == 65 || type1 == 166 || type1 == 168 || type1 == 170 || type1 == 178 || type1 == 194) {
                            if (wuiUtil.getJsonValueByIndex(id1, 0) != "" && wuiUtil.getJsonValueByIndex(id1, 0) != "0") {
                                var resourceids = wuiUtil.getJsonValueByIndex(id1, 0);
                                var resourcename = wuiUtil.getJsonValueByIndex(id1, 1);
                                var sHtml = ""

                                if (resourceids.indexOf(",") == 0) {
                                    resourceids = resourceids.substr(1);
                                    resourcename = resourcename.substr(1);
                                }
                                var resourceIdArray = resourceids.split(",");
                                var resourceNameArray = resourcename.split(",");
                                for (var _i = 0; _i < resourceIdArray.length; _i++) {
                                    var curid = resourceIdArray[_i];
                                    var curname = resourceNameArray[_i];
                                    if (linkurl == "/hrm/resource/HrmResource.jsp?id=") {
                                        sHtml += wrapshowhtml("<a href=javaScript:openhrm(" + curid + "); onclick='pointerXY(event);'>" + curname + "</a>&nbsp", curid, ismast);
                                    } else {
                                        sHtml += wrapshowhtml("<a href=" + linkurl + curid + " target=_blank>" + curname + "</a>&nbsp", curid, ismast);
                                    }

                                }
                                jQuery($GetEle(_fieldStr + id + "span")).html(sHtml);
                                showOrHideBrowserImg(ismand, _fieldStr, id, resourceids);
                                $GetEle(_fieldStr + id).value = resourceids;
                            } else {
                                $GetEle(_fieldStr + id).value = "";
                                jQuery($GetEle(_fieldStr + id + "span")).html("");
                                showOrHideBrowserImg(ismand, _fieldStr, id, "");
                            }
                        } else {
                            if (wuiUtil.getJsonValueByIndex(id1, 0) != "") {
                                if (type1 == 162) {
                                    var ids = wuiUtil.getJsonValueByIndex(id1, 0);
                                    var names = wuiUtil.getJsonValueByIndex(id1, 1);
                                    var descs = wuiUtil.getJsonValueByIndex(id1, 2);
                                    var href = wuiUtil.getJsonValueByIndex(id1, 3);
                                    sHtml = ""
                                    if (ids.indexOf(",") == 0) {
                                        ids = ids.substr(1);
                                        names = names.substr(1);
                                        descs = descs.substr(1);
                                    }
                                    $GetEle(_fieldStr + id).value = ids;
                                    var idArray = ids.split(",");
                                    var nameArray = names.split("~~WEAVERSplitFlag~~");
                                    if (nameArray.length < idArray.length) {
                                        nameArray = names.split(",");
                                    }
                                    var descArray = descs.split(",");
                                    for (var _i = 0; _i < idArray.length; _i++) {
                                        var curid = idArray[_i];
                                        var curname = nameArray[_i];
                                        var curdesc = descArray[_i];
                                        if (curdesc == '' || curdesc == 'undefined' || curdesc == null) {
                                            curdesc = curname;
                                        }
                                        if (curdesc) {
                                            curdesc = curname;
                                        }
                                        curname = curname.replace(new RegExp(/(<)/g), "&lt;");
                                        curname = curname.replace(new RegExp(/(>)/g), "&gt;");
                                        //sHtml += "<a title='" + curdesc + "' >" + curname + "</a>&nbsp";
                                        if (href == '') {
                                            sHtml += wrapshowhtml("<a title='" + curdesc + "' >" + curname + "</a>&nbsp", curid, ismast);
                                        } else {
                                            curid = curid.replace("&amp;", "&");
                                            curid = curid.replace("&lt;", "<");
                                            curid = curid.replace("&gt;", ">");
                                            curid = curid.replace("weaver2017", "+");
                                            var tempurl = dealChineseOfFieldParams(href, curid);
                                            tempurl = replaceModeidByFormidAndBillid(tempurl);
                                            sHtml += wrapshowhtml("<a title='" + curdesc + "' href='" + tempurl + "' target='_blank'>" + curname + "</a>&nbsp", curid, ismast);
                                        }
                                    }

                                    jQuery($GetEle(_fieldStr + id + "span")).html(sHtml);
                                    showOrHideBrowserImg(ismand, _fieldStr, id, ids);
                                }
                                if (type1 == 161) {
                                    var ids = wuiUtil.getJsonValueByIndex(id1, 0);
                                    var names = wuiUtil.getJsonValueByIndex(id1, 1);
                                    var descs = wuiUtil.getJsonValueByIndex(id1, 2);
                                    var href = wuiUtil.getJsonValueByIndex(id1, 3);
                                    $GetEle(_fieldStr + id).value = ids;
                                    names = names.replace(new RegExp(/(<)/g), "&lt;")
                                    names = names.replace(new RegExp(/(>)/g), "&gt;")
                                    //sHtml = "<a title='" + descs + "'>" + names + "</a>&nbsp";
                                    if (href == '') {
                                        sHtml = wrapshowhtml("<a title='" + descs + "'>" + names + "</a>&nbsp", ids, ismast);
                                    } else {
                                        var tempurl = dealChineseOfFieldParams(href, ids);
                                        sHtml = wrapshowhtml("<a title='" + descs + "' href='" + tempurl + "' target='_blank'>" + names + "</a>&nbsp", ids, ismast);
                                    }
                                    jQuery($GetEle(_fieldStr + id + "span")).html(sHtml);
                                    showOrHideBrowserImg(ismand, _fieldStr, id, ids);
                                }
                                if (linkurl == "") {
                                    if (type1 == 256 || type1 == 257) {
                                        var ids = wuiUtil.getJsonValueByIndex(id1, 0);
                                        var names = wuiUtil.getJsonValueByIndex(id1, 1);
                                        var idArray = ids.split(",");
                                        var nameArray = names.split(">,");
                                        var sHtml = "";
                                        for (var _i = 0; _i < idArray.length; _i++) {
                                            var curid = idArray[_i];
                                            var curname = (idArray.length - 1 == _i) ? nameArray[_i] : nameArray[_i] + ">";
                                            sHtml += wrapshowhtml(curname + "&nbsp", curid, ismast);
                                        }
                                        jQuery($GetEle(_fieldStr + id + "span")).html(sHtml);
                                    } else if (type1 == 161 || type1 == 162) {
                                        jQuery($GetEle(_fieldStr + id + "span")).html(sHtml);
                                    } else {
                                        jQuery($GetEle(_fieldStr + id + "span")).html(wrapshowhtml(wuiUtil.getJsonValueByIndex(id1, 1), "", ismast));
                                    }
                                } else {
                                    if (linkurl == "/hrm/resource/HrmResource.jsp?id=") {
                                        jQuery($GetEle(_fieldStr + id + "span")).html(wrapshowhtml("<a href=javaScript:openhrm(" + wuiUtil.getJsonValueByIndex(id1, 0) + "); onclick='pointerXY(event);'>" + wuiUtil.getJsonValueByIndex(id1, 1) + "</a>&nbsp", wuiUtil.getJsonValueByIndex(id1, 0), ismast));
                                    } else {
                                        jQuery($GetEle(_fieldStr + id + "span")).html(wrapshowhtml("<a href=" + linkurl + wuiUtil.getJsonValueByIndex(id1, 0) + " target='_new'>" + wuiUtil.getJsonValueByIndex(id1, 1) + "</a>", wuiUtil.getJsonValueByIndex(id1, 0), ismast));
                                    }
                                }
                                if (type1 != 161 && type1 != 162) {
                                    $GetEle(_fieldStr + id).value = wuiUtil.getJsonValueByIndex(id1, 0);
                                    showOrHideBrowserImg(ismand, _fieldStr, id, $GetEle(_fieldStr + id).value);
                                }
                            } else {
                                $GetEle(_fieldStr + id).value = "";
                                jQuery($GetEle(_fieldStr + id + "span")).html("");
                                showOrHideBrowserImg(ismand, _fieldStr, id, "");
                            }
                        }
                    }
                    hoverShowNameSpan(".e8_showNameClass");
                    try {
                        //eval(jQuery("#"+ _fieldStr + id).attr('onpropertychange'));
                        eval(jQuery("#" + _fieldStr + id + "__").attr('onpropertychange'));
                    } catch (e) {
                    }
                };
                dialog.Title = "请选择";//请选择
                dialog.Width = 550;
                if (url.indexOf("/MutiResourceBrowser.jsp") != -1) {
                    dialog.Width = 648;
                }
                dialog.Height = 600;
                dialog.Drag = true;
                //dialog.maxiumnable = true;
                dialog.show();


            }
        }
    }

    /**
     * 角色人员
     */
    function onShowResourceRole1(id, url, linkurl, type1, ismand, roleid) {
        var tmpids = $GetEle("field" + id).value;
        url = url + roleid + "_" + tmpids;
        //id1 = window.showModalDialog(url);
        var dialog = new window.top.Dialog();
        dialog.currentWindow = window;
        //dialog.callbackfunParam = null;
        dialog.URL = url;
        dialog.callbackfun = function (paramobj, id1) {
            if (id1) {
                if (wuiUtil.getJsonValueByIndex(id1, 0) != ""
                    && wuiUtil.getJsonValueByIndex(id1, 0) != "0") {

                    var resourceids = wuiUtil.getJsonValueByIndex(id1, 0);
                    var resourcename = wuiUtil.getJsonValueByIndex(id1, 1);
                    var sHtml = "";

                    if (resourceids.indexOf(",") == 0) {
                        resourceids = resourceids.substr(1);
                        resourcename = resourcename.substr(1);
                    }
                    var idArray = resourceids.split(",");
                    var nameArray = resourcename.split(",");
                    for (var _i = 0; _i < idArray.length; _i++) {
                        var curid = idArray[_i];
                        var curname = nameArray[_i];

                        sHtml += wrapshowhtml0($G("field" + id).getAttribute("viewtype"),
                            "<a title='" + curname + "' href='" + linkurl +
                            curid + "' target='_new'>" + curname + "</a>&nbsp", curid);
                    }

                    //$GetEle("field" + id + "span").innerHTML = sHtml;
                    jQuery($GetEle("field" + id + "span")).html(sHtml);
                    $GetEle("field" + id).value = resourceids;
                    hoverShowNameSpan(".e8_showNameClass");
                    try {
                        if (!isIE()) {
                            var onppchgfnstr = jQuery("#field" + id).attr('onpropertychange');
                            eval(onppchgfnstr);
                            if (onppchgfnstr.indexOf("function onpropertychange") != -1) {
                                onpropertychange();
                            }
                        }
                    } catch (e) {
                    }
                    try {
                        var onppchgfnstr = jQuery("#field" + id + "__").attr('onpropertychange').toString();
                        eval(onppchgfnstr);
                        if (onppchgfnstr.indexOf("function onpropertychange") != -1) {
                            onpropertychange();
                        }
                    } catch (e) {
                    }
                } else {
                    if (ismand == 0) {
                        $GetEle("field" + id + "span").innerHTML = "";
                    } else {
                        $GetEle("field" + id + "span").innerHTML = "<img src='/images/BacoError_wev8.gif' align=absmiddle>";
                    }
                    $GetEle("field" + id).value = "";
                }
                var _ismand = $G("field" + id).getAttribute("viewtype");
                if ($GetEle("field" + id).value == "") {
                    if (_ismand == 0) {
                        jQuery($GetEle("field" + id + "spanimg")).html("");
                    } else {
                        jQuery($GetEle("field" + id + "spanimg")).html("<img src='/images/BacoError_wev8.gif' align=absmiddle>");
                    }
                } else {
                    jQuery($GetEle("field" + id + "spanimg")).html("");
                }
                searchResource(wuiUtil.getJsonValueByIndex(id1, 0), wuiUtil.getJsonValueByIndex(id1, 1));//add by zp
            }

        };
        try {
            dialog.Title = SystemEnv.getHtmlNoteName(3418, languageid);
        } catch (e) {
            dialog.Title = SystemEnv.getHtmlNoteName(3418, languageid);
        }
        dialog.Width = 550;
        if (url.indexOf("/MutiResourceBrowser.jsp") != -1) {
            dialog.Width = 648;
        }
        dialog.Height = 600;
        dialog.Drag = true;
        //dialog.maxiumnable = true;
        dialog.show();
    }

    function searchResource(obj1, obj2) {
        window.location.href = "/RiChengList.jsp?isShow=1&ldHtml=" + obj2 + "&ld=" + obj1 + "&weekType=<%=weekType%>&firstDate=<%=firstDate%>&RiChengType=<%=RiChengType%>";
    }

    function searchBuMen(obj1, obj2) {
        window.location.href = "/RiChengList.jsp?isShow=1&bmHtml=" + obj2 + "&bm=" + obj1 + "&weekType=<%=weekType%>&firstDate=<%=firstDate%>&RiChengType=<%=RiChengType%>";
    }

    jQuery(document).ready(function () {
        jQuery("#field7616").val("<%=bm%>");
        jQuery("#field7616span").html('<%=bmHtml%>');
        jQuery("#field9343").val("<%=ld%>");
        jQuery("#field9343span").html('<%=ldHtml%>');
        jQuery("#btn1,#btn2,#btn3,#btn4,#btn5").hover(function () {
            jQuery(this).css("color", "white");
            jQuery(this).css("background-color", "#008df6");
        }, function () {
            jQuery(this).css("color", "#008df6");
            jQuery(this).css("background-color", "#EBEBEB");
        });
        jQuery("#btn6").hover(function () {
            jQuery(this).attr("src", "right.png");
        }, function () {
            jQuery(this).attr("src", "right.png");
        });
        jQuery("#btn7").hover(function () {
            jQuery(this).attr("src", "left.png");
        }, function () {
            jQuery(this).attr("src", "left.png");
        });
    });

    //页面刷新的方法
    function refresh() {
        window.location.reload();
    }

    setInterval('refresh()', 900000);//多次
</script>
</body>