package engine;

import com.alibaba.fastjson.JSONObject;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.upgradetool.httpclient.org.apache.http.client.methods.CloseableHttpResponse;
import weaver.upgradetool.httpclient.org.apache.http.client.methods.HttpPost;
import weaver.upgradetool.httpclient.org.apache.http.impl.client.CloseableHttpClient;
import weaver.upgradetool.httpclient.org.apache.http.impl.client.HttpClients;
import weaver.upgradetool.httpcore.org.apache.http.HttpEntity;
import weaver.upgradetool.httpcore.org.apache.http.ParseException;
import weaver.upgradetool.httpcore.org.apache.http.entity.StringEntity;
import weaver.upgradetool.httpcore.org.apache.http.message.BasicHeader;
import weaver.upgradetool.httpcore.org.apache.http.protocol.HTTP;
import weaver.upgradetool.httpcore.org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class Project4fofsAction extends BaseBean implements Action {

    public String projName;

    public String managerAccount;

    public String projType;

    public String getProjName() {
        return projName;
    }

    public void setProjName(String projName) {
        this.projName = projName;
    }

    public String getManagerAccount() {
        return managerAccount;
    }

    public void setManagerAccount(String managerAccount) {
        this.managerAccount = managerAccount;
    }

    public String getProjType() {
        return projType;
    }

    public void setProjType(String projType) {
        this.projType = projType;
    }

    @Override
    public String execute(RequestInfo requestInfo) {
        Map<String,String> map = new HashMap<>();
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        for( Property property : propertys){
            String str = property.getName();
            String value = property.getValue();
            map.put(str,value);
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("projName",map.get(projName));
        jsonObject.put("managerAccount",map.get(managerAccount));
        jsonObject.put("projType",map.get(projType));
        String result="";
        JSONObject res;
        try {
            result = send("https://fofs.livedata.cn/sstic/app/projs/portal-proj-generate",jsonObject,"utf-8",getToken());
            res = JSONObject.parseObject(result);
        } catch (Exception e) {
            requestInfo.getRequestManager().setMessagecontent("接口不通报错");
            return Action.FAILURE_AND_CONTINUE;
        }
        if ("操作成功".equals(res.get("data"))&&"COMSOOO1".equals(res.get("status"))){
            return Action.SUCCESS;
        }else {
            requestInfo.getRequestManager().setMessagecontent((String)res.get("data"));
        }
            return Action.FAILURE_AND_CONTINUE;
    }


    public Token getToken() throws IOException {
        Project4fofsAction project4fofsAction = new Project4fofsAction();
        JSONObject jsonObject = new JSONObject();
        String str=  project4fofsAction.send("https://fofs.livedata.cn/sstic/app/login?username=oagroup&password=1qaz1QAZ",jsonObject,"utf-8");
        JSONObject result = JSONObject.parseObject(str);
        Map tokenMap = (Map) result.get("data");
        Token token = new Token();
        token.setToken((String) tokenMap.get("token"));
        return token;
    }

    //无 token post 请求
    public String send(String url, JSONObject jsonObject, String encoding) throws ParseException, IOException {
        String body = "";
        //创建httpclient对象
        CloseableHttpClient client = HttpClients.createDefault();
        //创建post方式请求对象
        HttpPost httpPost = new HttpPost(url);
        //装填参数
        StringEntity s = new StringEntity(jsonObject.toString(), "utf-8");
        s.setContentEncoding(new BasicHeader(HTTP.CONTENT_TYPE,
                "application/json"));
        //设置参数到请求对象中
        httpPost.setEntity(s);
        System.out.println("请求地址："+url);
//        System.out.println("请求参数："+nvps.toString());
        //设置header信息
        //指定报文头【Content-type】、【User-Agent】
//        httpPost.setHeader("Content-type", "application/x-www-form-urlencoded");
        httpPost.setHeader("Content-type", "application/json");
        httpPost.setHeader("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        //执行请求操作，并拿到结果（同步阻塞）
//        if(!"".equals(token.getToken())){
//            httpPost.setHeader("token",token.getToken());
//        }
        CloseableHttpResponse response = client.execute(httpPost);
        //获取结果实体
        HttpEntity entity = response.getEntity();
        if (entity != null) {
            //按指定编码转换结果实体为String类型
            body = EntityUtils.toString(entity, encoding);
        }
        EntityUtils.consume(entity);
        //释放链接
        response.close();
        client.close();
        return body;
    }

    //tokenpost 请求
    public String send(String url, JSONObject jsonObject, String encoding, Token token) throws ParseException, IOException {
        String body = "";
        //创建httpclient对象
        CloseableHttpClient client = HttpClients.createDefault();
        //创建post方式请求对象
        HttpPost httpPost = new HttpPost(url);
        //装填参数
        StringEntity s = new StringEntity(jsonObject.toString(), "utf-8");
        s.setContentEncoding(new BasicHeader(HTTP.CONTENT_TYPE,
                "application/json"));
        //设置参数到请求对象中
        httpPost.setEntity(s);
        System.out.println("请求地址："+url);
//        System.out.println("请求参数："+nvps.toString());
        //设置header信息
        //指定报文头【Content-type】、【User-Agent】
//        httpPost.setHeader("Content-type", "application/x-www-form-urlencoded");
        httpPost.setHeader("Content-type", "application/json");
        httpPost.setHeader("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        //执行请求操作，并拿到结果（同步阻塞）
        httpPost.setHeader("Authorization",token.getToken());
        CloseableHttpResponse response = client.execute(httpPost);
        //获取结果实体
        HttpEntity entity = response.getEntity();
        if (entity != null) {
            //按指定编码转换结果实体为String类型
            body = EntityUtils.toString(entity, encoding);
        }
        EntityUtils.consume(entity);
        //释放链接
        response.close();
        client.close();
        return body;
    }

}
