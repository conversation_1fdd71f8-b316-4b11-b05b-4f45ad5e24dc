<%@ page
        import="weaver.general.*,java.util.*,java.math.*,java.text.*,weaver.system.*,weaver.conn.*,weaver.file.*,com.help.DateHelper" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="/browserTag" prefix="brow" %>
<%@ taglib uri="/WEB-INF/weaver.tld" prefix="wea" %>
<%@ include file="/systeminfo/init_wev8.jsp" %>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page"/>
<jsp:useBean id="rs0" class="weaver.conn.RecordSet" scope="page"/>
<jsp:useBean id="rs1" class="weaver.conn.RecordSet" scope="page"/>
<jsp:useBean id="rs2" class="weaver.conn.RecordSet" scope="page"/>

<SCRIPT language="javascript" src="/js/datetime_wev8.js"></script>
<SCRIPT language="javascript" src="/js/JSDateTime/WdatePicker_wev8.js"></script>
<HTML>
<HEAD>
    <LINK href="/css/Weaver_wev8.css" type=text/css rel=STYLESHEET>
    <link rel="stylesheet" href="/css/ecology8/request/requestTopMenu_wev8.css" type="text/css"/>
</head>
    <%
//数据初始化
int userid=user.getUID();
int departmentid=user.getUserDepartment();
DateHelper dateHelp=new DateHelper();
String firstDate=Util.null2String(request.getParameter("firstDate"),dateHelp.getThisWeekMonday("yyyy-MM-dd"));
String weekType=Util.null2String(request.getParameter("weekType"),"0");
String dateType="yyyy-MM-dd";
String firstDayOfWeek=dateHelp.getFirstDayOfWeek(firstDate, dateType);
String startDate="";
if(weekType.equals("1")){//上周
    firstDate=dateHelp.getDayByAdd(firstDayOfWeek,-7,dateType);
	startDate=firstDate.split("-")[0]+"年"+firstDate.split("-")[1]+"月"+firstDate.split("-")[2]+"日";
}else if(weekType.equals("2")){//下周
	firstDate=dateHelp.getDayByAdd(firstDayOfWeek,7,dateType);
	startDate=firstDate.split("-")[0]+"年"+firstDate.split("-")[1]+"月"+firstDate.split("-")[2]+"日";
}else{
	startDate=firstDayOfWeek.split("-")[0]+"年"+firstDayOfWeek.split("-")[1]+"月"+firstDayOfWeek.split("-")[2]+"日";
}
String endDate=dateHelp.getLastDay(startDate,"yyyy年MM月dd日");
String RiChengType=Util.null2String(request.getParameter("RiChengType"),"1");
int LingDaoRoleId=84;//公司领导角色ID
%>
<body style="text-align:center;font-family: 微软雅黑 !important;">
<!--Tab标签切换-->
<table border="1" cellpadding="0" cellspacing="0" style="width:100%;">
    <tr>
        <%if (RiChengType.equals("1")) {%>
        <td align="center" bgcolor="#008df6" style="border:1px solid #008df6;width:33%" onclick="tiaoZhuan(1)">
            <span style="font-size:1.2rem;color:white;cursor: pointer;">集团领导会议</span>
        </td>

        <td align="center" style="border:1px solid #008df6;width:33%" onclick="tiaoZhuan(2)">
            <span style="font-size:1.2rem;color:#008df6;cursor: pointer;">部门会议</span>
        </td>

        <td align="center" style="border:1px solid #008df6;width:33%" onclick="tiaoZhuan(3)">
            <span style="font-size:1.2rem;color:#008df6;cursor: pointer;">我的会议</span>
        </td>
        <%} else if (RiChengType.equals("2")) {%>
        <td align="center" style="border:1px solid #008df6;width:33%" onclick="tiaoZhuan(1)">
            <span style="font-size:1.2rem;color:#008df6;cursor: pointer;">集团领导会议</span>
        </td>
        <td align="center" bgcolor="#008df6" style="border:1px solid #008df6;width:33%" onclick="tiaoZhuan(2)">
            <span style="font-size:1.2rem;color:white;cursor: pointer;">部门会议</span>
        </td>
        <td align="center" style="border:1px solid #008df6;width:33%" onclick="tiaoZhuan(3)">
            <span style="font-size:1.2rem;color:#008df6;cursor: pointer;">我的会议</span>
        </td>
        <%} else {%>
        <td align="center" style="border:1px solid #008df6;width:33%" onclick="tiaoZhuan(1)">
            <span style="font-size:1.2rem;color:#008df6;cursor: pointer;">集团领导会议</span>
        </td>
        <td align="center" style="border:1px solid #008df6;width:33%" onclick="tiaoZhuan(2)">
            <span style="font-size:1.2rem;color:#008df6;cursor: pointer;">部门会议</span>
        </td>
        <td align="center" bgcolor="#008df6" style="border:1px solid #008df6;width:33%" onclick="tiaoZhuan(3)">
            <span style="font-size:1.2rem;color:white;cursor: pointer;">我的会议</span>
        </td>
        <%}%>
    </tr>
</table>

<!--<div style="display: table-cell;vertical-align: middle;text-align: center;height:80px;">-->
<img title="上一周"
     style="cursor: pointer;height:1.5rem;margin-left:1px;margin-top:1px;margin-bottom:1px;border:1px solid #008df6;float:left;"
     src="left.png" onclick="up()">
<span title="本周" style="cursor: pointer;font-size:1rem;margin-left:2px;border:1px solid #008df6;color:#008df6"
      onclick="thisWeek()">本周</span>
<span style="font-size:1rem;color:#008df6;margin-right:2px"><%=startDate%>--<%=endDate%></span>
<img title="下一周"
     style="cursor: pointer;height:1.5rem;margin-right:1px;margin-top:1px;margin-bottom:1px;border:1px solid #008df6;float:right;"
     src="right.png" onclick="down()">
<!--</div>-->

<!--日程列表-->
<table style="width:100%;border-collapse:collapse;">
    <%
        String date_rq = "";
        String date_week = "";
        for (int i = 0; i < 7; i++) {
            if (i == 0) {
                date_rq = firstDate;
            } else {
                date_rq = dateHelp.getNextDay(date_rq, "yyyy-MM-dd");
            }
            date_week = dateHelp.getWeekOfDate(date_rq);

            String searchSql = "";
            String sqlWhere = "";
            if (RiChengType.equals("1")) {//领导日程
                rs.execute("select b.resourceid from HrmRoles a,HrmRoleMembers b where a.id=b.roleid and a.id=" + LingDaoRoleId);
                while (rs.next()) {
                    sqlWhere += " ','+resourceid+',' like '%," + rs.getString("resourceid") + ",%' or";
                }
                if (sqlWhere.length() > 0) {
                    sqlWhere = sqlWhere.substring(0, sqlWhere.length() - 2);
                }
            } else if (RiChengType.equals("2")) {//部门日程
                rs.execute("select id from HrmResource where departmentid=" + departmentid);
                while (rs.next()) {
                    sqlWhere += " ','+resourceid+',' like '%," + rs.getString("id") + ",%' or";
                }
                if (sqlWhere.length() > 0) {
                    sqlWhere = sqlWhere.substring(0, sqlWhere.length() - 2);
                }
            } else if (RiChengType.equals("3")) {//个人日程
                sqlWhere += " ','+resourceid+',' like '%," + userid + ",%'";
            }
    %>
    <tr>
        <!--日期-->
        <td rowspan="4" style="font-size:1.1rem;border:1px solid #008df6;text-align:center;width:20%;">
            <%=date_week%><br/>
            <%=date_rq.split("-")[1] + "/" + date_rq.split("-")[2]%>
        </td>
        <td bgcolor="#008df6" style="color:white;font-size:1.1rem;border:1px solid #008df6;">
            上午
        </td>
    </tr>
    <tr>
        <td style="font-size:15px;border:1px solid #008df6;line-height:18px;text-align:left;font-weight:700">
            <%
                searchSql = "select othermembers,hrmmembers,'' as newdepmembers,a.id,b.name,a.resourceid,b.begindate,b.begintime,b.enddate,b.endtime,b.address,b.customizeAddress " +
                        //" (select name from MeetingRoom c where b.address=c.id) meetingroom "
                        " from workplan a,meeting b where a.meetingid=b.id and b.cancel is null and b.meetingstatus=2 "
                        + " and (" + sqlWhere + ")"
                        + " and ("
                        + " (a.begindate='" + date_rq + "' and a.begintime<'13:00') or "
                        + " (a.begindate<'" + date_rq + "' and a.enddate='" + date_rq + "') or "
                        + " (a.begindate<'" + date_rq + "' and a.enddate>'" + date_rq + "') "
                        + " ) order by b.begindate+b.begintime,b.enddate+b.endtime ";
                BaseBean baseBean = new BaseBean();
                baseBean.writeLog("searchSql:" + searchSql);
                rs0.execute(searchSql);
                if (rs0.getCounts() == 0) {
            %>
            <br/>
            <br/>
            <%
                }
                while (rs0.next()) {
                    String id = rs0.getString("id");
                    String newhrmmembers = rs0.getString("hrmmembers");//参会人员
                    String newhrmmembersStr = "";
                    if (!newhrmmembers.equals("")) {
//						rs1.execute("select lastname from HrmResource where id in(" + newhrmmembers + ") order by case lastname " +
//								"when '傅红岩' then 1 " +
//								"when '林炯' then 2  " +
//								"when '朱民' then 3  " +
//								"when '杜松杨' then 4 " +
//								"when '项亦男' then 5 " +
//								"else 6 " +
//								"end " +
//								"asc ,charindex(CONVERT(CHAR,id),'" + newhrmmembers + "' ) ");
                        rs1.execute("select lastname from HrmResource where id in(" + newhrmmembers + ") order by dsporder ");
                        while (rs1.next()) {
                            newhrmmembersStr += rs1.getString("lastname") + "，";
                        }
                    }

                    //查询address对应的地址
                    String addressRs = rs0.getString("address");//地址
                    String newAddresStrAll = "";
                    if (!"".equals(addressRs)) {
                        RecordSet rsAddress = new RecordSet();
                        String[] addressArray = addressRs.split(",");
                        StringBuilder sb = new StringBuilder();
                        sb.append(" select name from MeetingRoom a where 1=1 ");
                        sb.append(" and ( ");
                        for (int j = 0; j < addressArray.length; j++) {
                            if (j > 0) {
                                sb.append(" or ");
                            }
                            sb.append(" (a.id = ").append(addressArray[j]).append(") ");
                        }
                        sb.append("  ) ");
                        rsAddress.executeQuery(sb.toString());
                        while (rsAddress.next()) {
                            newAddresStrAll += rsAddress.getString("name") + "，";
                        }
                        if (!"".equals(newAddresStrAll)) {
                            newAddresStrAll = newAddresStrAll.substring(0, newAddresStrAll.length() - 1);
                        }
                    }


                    String newdepmembers = rs0.getString("newdepmembers");//参会部门
                    rs1.execute("select departmentname from HrmDepartment where id in(" + newdepmembers + ") order by CHARINDEX(CONVERT(CHAR,id),'" + newdepmembers + "' ) ");
                    String newdepmembersStr = "";
                    while (rs1.next()) {
                        newdepmembersStr += rs1.getString("departmentname") + "，";
                    }

                    String othermembers = rs0.getString("othermembers");//其他参会人员
                    String lastname = newhrmmembersStr + newdepmembersStr + othermembers;
                    String address = rs0.getString("customizeAddress") + newAddresStrAll;
                    if (lastname.endsWith("，")) {
                        lastname = lastname.substring(0, lastname.length() - 1);
                    }
                    String begintime = rs0.getString("begintime");
                    String endtime = rs0.getString("endtime");
                    if (!rs0.getString("begindate").equals(date_rq) && !rs0.getString("enddate").equals(date_rq)) {
                        begintime = "09:00";
                        endtime = "13:00";
                    } else if (!rs0.getString("begindate").equals(date_rq) && rs0.getString("enddate").equals(date_rq)) {
                        begintime = "09:00";
                        if (Integer.parseInt(endtime.replace(":", "")) > 1300) {
                            endtime = "13:00";
                        }
                    } else if (rs0.getString("begindate").equals(date_rq) && !rs0.getString("enddate").equals(date_rq)) {
                        endtime = "13:00";
                    } else if (rs0.getString("begindate").equals(date_rq) && rs0.getString("enddate").equals(date_rq)) {
                        if (Integer.parseInt(endtime.replace(":", "")) > 1300) {
                            endtime = "13:00";
                        }
                    }
            %>
            <%=begintime%> - <%=endtime%> <span
                style="color:#008df6;cursor: pointer;">[<%=rs0.getString("name")%>]</span><br/>[<%=lastname%>
            ]<%if (!address.equals("")) {%> [<%=address%>]<%}%>
            <br/><br/>
            <%}%>
        </td>
    <tr>
        <td bgcolor="#008df6" style="color:white;font-size:1.1rem;border:1px solid #008df6;">
            下午
        </td>
    <tr>
        <td style="font-size:15px;border:1px solid #008df6;line-height:18px;text-align:left;font-weight:700">
            <%
                searchSql = "select othermembers,hrmmembers,'' as newdepmembers,a.id,b.name,a.resourceid,b.begindate,b.begintime,b.enddate,b.endtime,b.address,b.customizeAddress " +
                        //"(select name from MeetingRoom c where b.address=c.id) meetingroom "
                        " from workplan a,meeting b where a.meetingid=b.id and b.cancel is null and b.meetingstatus=2 "
                        + " and (" + sqlWhere + ")"
                        + " and ("
                        + " (a.begindate='" + date_rq + "' and a.begintime>'13:00') or"
                        + " (a.begindate='" + date_rq + "' and a.enddate>'" + date_rq + "') or"
                        + " (a.enddate='" + date_rq + "' and a.endtime>'13:00') or"
                        + " (a.begindate<'" + date_rq + "' and a.enddate>'" + date_rq + "')"
                        + " ) order by b.begindate+b.begintime,b.enddate+b.endtime ";

                rs0.execute(searchSql);
                if (rs0.getCounts() == 0) {
            %>
            <br/>
            <br/>
            <%
                }
                while (rs0.next()) {
                    String id = rs0.getString("id");
                    String newhrmmembers = rs0.getString("hrmmembers");//参会人员
                    String newhrmmembersStr = "";
                    if (!newhrmmembers.equals("")) {
//                        rs1.execute("select lastname from HrmResource where id in(" + newhrmmembers + ") order by case lastname " +
//                                "when '傅红岩' then 1 " +
//                                "when '林炯' then 2  " +
//                                "when '朱民' then 3 " +
//                                "when '杜松杨' then 4 " +
//                                "when '项亦男' then 5 " +
//                                "else 6 " +
//                                "end " +
//                                "asc ,CHARINDEX(CONVERT(CHAR,id),'" + newhrmmembers + "' ) ");
                        rs1.execute("select lastname from HrmResource where id in(" + newhrmmembers + ") order by dsporder ");
                        while (rs1.next()) {
                            newhrmmembersStr += rs1.getString("lastname") + "，";
                        }
                    }

                    //查询address对应的地址
                    String addressRs = rs0.getString("address");//地址
                    String newAddresStrAll = "";
                    if (!"".equals(addressRs)) {
                        RecordSet rsAddress = new RecordSet();
                        String[] addressArray = addressRs.split(",");
                        StringBuilder sb = new StringBuilder();
                        sb.append(" select name from MeetingRoom a where 1=1 ");
                        sb.append(" and ( ");
                        for (int j = 0; j < addressArray.length; j++) {
                            if (j > 0) {
                                sb.append(" or ");
                            }
                            sb.append(" (a.id = ").append(addressArray[j]).append(") ");
                        }
                        sb.append("  ) ");
                        rsAddress.executeQuery(sb.toString());
                        while (rsAddress.next()) {
                            newAddresStrAll += rsAddress.getString("name") + "，";
                        }
                        if (!"".equals(newAddresStrAll)) {
                            newAddresStrAll = newAddresStrAll.substring(0, newAddresStrAll.length() - 1);
                        }
                    }

                    String newdepmembers = rs0.getString("newdepmembers");//参会部门
                    rs1.execute("select departmentname from HrmDepartment where id in(" + newdepmembers + ") order by CHARINDEX(CONVERT(CHAR,id),'" + newdepmembers + "' ) ");
                    String newdepmembersStr = "";
                    while (rs1.next()) {
                        newdepmembersStr += rs1.getString("departmentname") + "，";
                    }

                    String othermembers = rs0.getString("othermembers");//其他参会人员
                    String lastname = newhrmmembersStr + newdepmembersStr + othermembers;
                    String address = rs0.getString("customizeAddress") + newAddresStrAll;
                    if (lastname.endsWith("，")) {
                        lastname = lastname.substring(0, lastname.length() - 1);
                    }

                    String begintime = rs0.getString("begintime");
                    String endtime = rs0.getString("endtime");
                    if (!rs0.getString("begindate").equals(date_rq) && !rs0.getString("enddate").equals(date_rq)) {
                        begintime = "13:00";
                        endtime = "17:00";
                    } else if (rs0.getString("begindate").equals(date_rq) && !rs0.getString("enddate").equals(date_rq)) {
                        endtime = "17:00";
                        if (Integer.parseInt(begintime.split(":")[0]) < 13) {
                            begintime = "13:00";
                        }
                    } else if (!rs0.getString("begindate").equals(date_rq) && rs0.getString("enddate").equals(date_rq)) {
                        begintime = "13:00";
                    } else if (rs0.getString("begindate").equals(date_rq) && rs0.getString("enddate").equals(date_rq)) {
                        if (Integer.parseInt(begintime.split(":")[0]) < 13) {
                            begintime = "13:00";
                        }
                    }
            %>

            <%=begintime%> - <%=endtime%> <span
                style="color:#008df6;cursor: pointer;">[<%=rs0.getString("name")%>]</span><br/> [<%=lastname%>
            ]<%if (!address.equals("")) {%>[<%=address%>]<%}%>
            <br/><br/>


            <%}%>
        </td>
    </tr>
    <%}%>
</table>
<script language=javascript>
    function tiaoZhuan(obj) {
        window.location.href = "/mobile/plugin/RiChengList.jsp?firstDate=<%=firstDate%>&RiChengType=" + obj;
    }

    function thisWeek() {
        window.location.href = "/mobile/plugin/RiChengList.jsp?RiChengType=<%=RiChengType%>";
    }

    function up() {
        window.location.href = "/mobile/plugin/RiChengList.jsp?weekType=1&firstDate=<%=firstDate%>&RiChengType=<%=RiChengType%>";
    }

    function down() {
        window.location.href = "/mobile/plugin/RiChengList.jsp?weekType=2&firstDate=<%=firstDate%>&RiChengType=<%=RiChengType%>";
    }
</script>
</body>