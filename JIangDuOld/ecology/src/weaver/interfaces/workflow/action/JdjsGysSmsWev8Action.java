package weaver.interfaces.workflow.action;

import java.math.*;
import java.text.*;
import java.util.UUID;

import weaver.conn.RecordSet;
import weaver.general.*;
import weaver.sms.SMSSaveAndSend;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.workflow.WorkflowComInfo;

/**
 * 江都建设，根据配置，发送供应商短信
 * <AUTHOR>
 *
 */
public class JdjsGysSmsWev8Action extends BaseBean implements Action{

	public String smsid = "";

	@Override
	public String execute(RequestInfo request) {
		// TODO Auto-generated method stub
		String retStr = Action.SUCCESS;
		try{
			char flag = Util.getSeparator();
			WorkflowComInfo workflowComInfo = new WorkflowComInfo();
			int requestid = Util.getIntValue(request.getRequestid());
			int workflowid = Util.getIntValue(request.getWorkflowid());
			int formid = Util.getIntValue(workflowComInfo.getFormId(""+workflowid));
			String maintablename = "formtable_main_"+((-1)*formid);
			writeLog("requestid = " + requestid);
			writeLog("workflowid = " + workflowid);
			writeLog("formid = " + formid);
			RecordSet rs = new RecordSet();
			RecordSet rs_md_det = new RecordSet();
			RecordSet rs_wf_main = new RecordSet();
			RecordSet rs_wf_det = new RecordSet();
			String sql = "";

			int smsid_ = Util.getIntValue(this.smsid, 0);
			writeLog("this.smsid = " + this.smsid);
			if(smsid_ < 0) {
				return retStr;
			}
			rs.execute("select * from uf_smsmb where id="+smsid_);
			if(rs.next()) {
				String smscon = Util.null2String(rs.getString("smscon"));

				boolean sjhIsDetail = false;//手机号是否来自明细
				int detnum = 0;//来自明细几组
				rs_md_det.execute("select * from uf_smsmb_dt1 where mainid="+smsid_);
				while(rs_md_det.next()) {
					int ftype_ = Util.getIntValue(rs_md_det.getString("ftype"));
					int detnum_ = Util.getIntValue(rs_md_det.getString("detnum"));
					String fname_ = Util.null2String(rs_md_det.getString("fname"));
					if(ftype_ == 0) {//手机号
						if(detnum_ > 0) {
							sjhIsDetail = true;
							detnum = detnum_;
							break;
						}
					}
				}
				rs_md_det.beforFirst();

				rs_wf_main.execute("select * from "+maintablename+" where requestid="+requestid);
				if(rs_wf_main.next()) {
					int mainid = Util.getIntValue(rs_wf_main.getString("id"));

					String sjh = "";
					while(rs_md_det.next()) {
						int ftype_ = Util.getIntValue(rs_md_det.getString("ftype"));
						int detnum_ = Util.getIntValue(rs_md_det.getString("detnum"));
						String fname_ = Util.null2String(rs_md_det.getString("fname"));
						if(ftype_==1 && detnum_<=0) {//一般
							String v_ = Util.null2String( rs_wf_main.getString(fname_) );
							smscon = smscon.replaceAll("\\&"+fname_+"\\$", v_);
						}
						if(sjhIsDetail == false) {
							if(ftype_ == 0) {
								sjh = Util.null2String( rs_wf_main.getString(fname_) ).trim();
							}
						}
					}
					if(sjhIsDetail == false) {
						if(!"".equals(sjh)) {
							send(sjh, smscon);
						}
					}else {
						rs_wf_det.execute("select * from "+maintablename+"_dt"+detnum+" where mainid="+mainid);
						while(rs_wf_det.next()) {
							sjh = "";
							String smscon_ = smscon;
							while(rs_md_det.next()) {
								int ftype_ = Util.getIntValue(rs_md_det.getString("ftype"));
								String fname_ = Util.null2String(rs_md_det.getString("fname"));
								if(ftype_ == 1) {//一般
									String v_ = Util.null2String( rs_wf_det.getString(fname_) );
									smscon_ = smscon_.replaceAll("\\&"+fname_+"\\$", v_);
								}else {
									sjh = Util.null2String( rs_wf_det.getString(fname_) ).trim();
								}
							}
							if(!"".equals(sjh)) {
								send(sjh, smscon_);
							}
						}
					}
				}
				
			}
		}catch(Exception ex){
			writeLog(ex);
		}
		return retStr;
	}

	public void send(String sjh, String sms) {
		try {
			SMSSaveAndSend smsSaveAndSend = new SMSSaveAndSend();
			smsSaveAndSend.reset();
			smsSaveAndSend.setMessage(sms);
			smsSaveAndSend.setRechrmnumber("");
			smsSaveAndSend.setReccrmnumber("");
			smsSaveAndSend.setCustomernumber(sjh);

			smsSaveAndSend.setRechrmids("");
			smsSaveAndSend.setReccrmids("");
			smsSaveAndSend.setSendnumber("");
			//smsSaveAndSend.setRequestid(0);
			//smsSaveAndSend.setUserid(user.getUID());
			//smsSaveAndSend.setUsertype(user.getLogintype());
			boolean smst = smsSaveAndSend.send();
		}catch(Exception ex) {
			writeLog(ex);
		}
	}

	public String getSmsid() {
		return smsid;
	}
	public void setSmsid(String smsid) {
		this.smsid = smsid;
	}

}
