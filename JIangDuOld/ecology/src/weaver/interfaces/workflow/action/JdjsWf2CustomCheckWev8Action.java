package weaver.interfaces.workflow.action;

import java.math.*;
import java.text.*;
import java.util.UUID;

import weaver.conn.RecordSet;
import weaver.crm.CrmShareBase;
import weaver.crm.Maint.CustomerInfoComInfo;
import weaver.general.*;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.workflow.WorkflowComInfo;

/**
 * 江都建设，流程转客户，客户名重复检验
 * <AUTHOR>
 *
 */
public class JdjsWf2CustomCheckWev8Action extends BaseBean implements Action{

	public String errormsg = "";
	@Override
	public String execute(RequestInfo request) {
		// TODO Auto-generated method stub
		String retStr = Action.SUCCESS;
		try{
			char flag = Util.getSeparator();
			WorkflowComInfo workflowComInfo = new WorkflowComInfo();
			int requestid = Util.getIntValue(request.getRequestid());
			int workflowid = Util.getIntValue(request.getWorkflowid());
			int formid = Util.getIntValue(workflowComInfo.getFormId(""+workflowid));
			String maintablename = "formtable_main_"+((-1)*formid);
			writeLog("requestid = " + requestid);
			writeLog("workflowid = " + workflowid);
			writeLog("formid = " + formid);
			RecordSet rs = new RecordSet();
			RecordSet rs0 = new RecordSet();
			RecordSet rs1 = new RecordSet();
			String sql = "";

			this.errormsg = Util.null2String(this.errormsg).trim();

			sql = "select dwmc from "+maintablename+" where requestid="+requestid;
			writeLog("sql 001 = " + sql);
			rs.execute(sql);
			if(rs.next()) {
				String dwmc = Util.null2String(rs.getString("dwmc"));

				rs1.execute("select id from CRM_CustomerInfo where name='"+dwmc.replaceAll("'", "''")+"'");
				if (rs1.next()) {
					RequestManager rm = request.getRequestManager();
					if(rm != null){
						//rm.setMessage("message 错误");
						rm.setMessagecontent(errormsg.replaceAll("\\$dwmc\\$", dwmc));
						retStr = Action.FAILURE_AND_CONTINUE;
					}
				}
				
			}
		}catch(Exception ex){
			writeLog(ex);
		}
		return retStr;
	}
	public String getErrormsg() {
		return errormsg;
	}
	public void setErrormsg(String errormsg) {
		this.errormsg = errormsg;
	}

}
