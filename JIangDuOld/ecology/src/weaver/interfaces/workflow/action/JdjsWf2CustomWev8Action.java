package weaver.interfaces.workflow.action;

import java.math.*;
import java.text.*;
import java.util.UUID;

import weaver.conn.RecordSet;
import weaver.crm.CrmShareBase;
import weaver.crm.Maint.CustomerInfoComInfo;
import weaver.general.*;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.workflow.WorkflowComInfo;

/**
 * 江都建设，流程转客户
 * <AUTHOR>
 *
 */
public class JdjsWf2CustomWev8Action extends BaseBean implements Action{

	@Override
	public String execute(RequestInfo request) {
		// TODO Auto-generated method stub
		String retStr = Action.SUCCESS;
		try{
			char flag = Util.getSeparator();
			WorkflowComInfo workflowComInfo = new WorkflowComInfo();
			int requestid = Util.getIntValue(request.getRequestid());
			int workflowid = Util.getIntValue(request.getWorkflowid());
			int formid = Util.getIntValue(workflowComInfo.getFormId(""+workflowid));
			String maintablename = "formtable_main_"+((-1)*formid);
			writeLog("requestid = " + requestid);
			writeLog("workflowid = " + workflowid);
			writeLog("formid = " + formid);
			RecordSet rs = new RecordSet();
			RecordSet rs0 = new RecordSet();
			RecordSet rs1 = new RecordSet();
			String sql = "";

			sql = "select * from "+maintablename+" where requestid="+requestid;
			writeLog("sql 001 = " + sql);
			rs.execute(sql);
			if(rs.next()) {
				String sj = Util.null2String(rs.getString("sj")).trim();
				String uuid = UUID.randomUUID().toString();

				String fieldSql = "name, address1, xz, zyyw, fax, shxydm, zh, khh, fjsc, bzsm, " + 
							"status, type, manager, uuidzpf, createdate, deleted, language";
				String valueSql = "f.dwmc, f.dz, s.selectname, f.zyyw, f.cz, f.shxydm, f.zh, f.khh, f.fjsc, f.bzsm, " + 
							"4, 2, f.sqr, '"+uuid+"', '"+TimeUtil.getCurrentDateString()+"', 0, 7 ";
				sql = "insert into CRM_CustomerInfo("+fieldSql+") select "+valueSql+" from "+maintablename+" f left join workflow_selectitem s on s.fieldid=7483 and s.selectvalue=f.xz where f.requestid="+requestid;
				writeLog("sql 010 = " + sql);
				rs0.execute(sql);

				rs1.execute("select max(id) as mid from CRM_CustomerInfo where uuidzpf='"+uuid+"'");
				if (rs1.next()) {
					int customerid = Util.getIntValue(rs1.getString("mid"), 0);

					fieldSql = "fullname, firstname, email, phoneoffice, mobilephone, customerid";
					valueSql = "f.lxr, f.lxr, f.yx, f.dh, f.sj, "+customerid+" ";
					sql = "insert into CRM_CustomerContacter("+fieldSql+") select "+valueSql+" from "+maintablename+" f where f.requestid="+requestid;
					writeLog("sql 020 = " + sql);
					rs0.execute(sql);

					sql = "update CRM_CustomerInfo set PortalLoginid='u"+customerid+"', PortalPassword='"+sj+"', PortalStatus=2 where id="+customerid;
					writeLog("sql 020 = " + sql);
					rs0.execute(sql);
					

					CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
					customerInfoComInfo.addCustomerInfoCache(""+customerid);

					CrmShareBase crmShareBase = new CrmShareBase();
					crmShareBase.setDefaultShare(""+customerid);
				}
			}
		}catch(Exception ex){
			writeLog(ex);
		}
		return retStr;
	}

}
