package weaver.interfaces.workflow.action;

import java.math.*;
import java.text.*;

import weaver.conn.RecordSet;
import weaver.general.*;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.workflow.WorkflowComInfo;

/**
 * 江都建设，获取客户的联系人手机号，根据id，取最小的
 * <AUTHOR>
 *
 */
public class JdjsCustomMobileWev8Action extends BaseBean implements Action{

	public String khfn = "";
	public String sjhfn = "";
	public String detnum = "";

	@Override
	public String execute(RequestInfo request) {
		// TODO Auto-generated method stub
		String retStr = Action.SUCCESS;
		try{
			char flag = Util.getSeparator();
			WorkflowComInfo workflowComInfo = new WorkflowComInfo();
			int requestid = Util.getIntValue(request.getRequestid());
			int workflowid = Util.getIntValue(request.getWorkflowid());
			int formid = Util.getIntValue(workflowComInfo.getFormId(""+workflowid));
			String maintablename = "formtable_main_"+((-1)*formid);
			writeLog("requestid = " + requestid);
			writeLog("workflowid = " + workflowid);
			writeLog("formid = " + formid);
			RecordSet rs = new RecordSet();
			RecordSet rs0 = new RecordSet();
			RecordSet rs1 = new RecordSet();
			String sql = "";

			this.khfn = Util.null2String(this.khfn).trim();
			this.sjhfn = Util.null2String(this.sjhfn).trim();
			int detnum_ = Util.getIntValue(this.detnum, 0);
			writeLog("khfn = " + this.khfn);
			writeLog("sjhfn = " + this.sjhfn);
			writeLog("detnum = " + this.detnum);
			if("".equals(this.khfn) || "".equals(this.sjhfn)) {
				return retStr;
			}

			sql = "select * from "+maintablename+" where requestid="+requestid;
			writeLog("sql 001 = " + sql);
			rs.execute(sql);
			if(rs.next()) {
				int mainid = Util.getIntValue(rs.getString("id"), 0);
				String tablename = "";
				if(detnum_ <= 0) {
					rs.beforFirst();
					tablename = maintablename;
				}else {
					tablename = maintablename+"_dt"+detnum_;
					rs.execute("select * from "+tablename+" where mainid="+mainid);
				}
				while(rs.next()) {
					int id = Util.getIntValue(rs.getString("id"), 0);
					int kh = Util.getIntValue(rs.getString(this.khfn), 0);

					rs0.execute("select mobilephone from CRM_CustomerContacter where customerid="+kh+" order by id asc");
					if(rs0.next()) {
						String sjh = Util.null2String(rs0.getString("mobilephone")).trim();
						rs0.execute("update "+tablename+" set "+this.sjhfn+"='"+sjh+"' where id="+id);
					}
				}
			}
		}catch(Exception ex){
			writeLog(ex);
		}
		return retStr;
	}

	public String getKhfn() {
		return khfn;
	}

	public void setKhfn(String khfn) {
		this.khfn = khfn;
	}

	public String getSjhfn() {
		return sjhfn;
	}

	public void setSjhfn(String sjhfn) {
		this.sjhfn = sjhfn;
	}

	public String getDetnum() {
		return detnum;
	}

	public void setDetnum(String detnum) {
		this.detnum = detnum;
	}

}
