package cwcc.job.crm;

import cwcc.crm.CrmSeasUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;
import weaver.toolbox.db.recordset.ExecuteUtil;
import weaver.toolbox.db.recordset.QueryUtil;

import java.util.Map;

/**
 * 功能说明 客户公海转换
 * 出现状况客户进入部门公海
 * 部门公海转换到公司公海
 *
 * <AUTHOR>
 * @crete Nov 30, 2022 14:27
 */
public class CrmSeasConvertJob extends BaseCronJob {

    BaseBean log = new BaseBean();

    @Override
    public void execute() {
        log.writeLog("CrmSeasConvertJob start");

        // 获取公海客户集合，如果已经存在于公海的客户，不再进行转换
        Map<String, String> seasCustomerMap = QueryUtil.doQueryCacheMapAll("crm_seasCustomer", "customerid", "seasid");
        log.writeLog("seasCustomerMap:" + seasCustomerMap);
        // region 基础客户 start
        // 超过N个月未转化成意向客户
        // 基础客户未转化为意向客户的天数
        String jckhwzhwyxkhy = CrmSeasUtil.getDeptSeasDays("jckhwzhwyxkhy");
        // 获取N个月未转化成意向客户的客户
        String qry5 = "select a.id,dbo.getTopDepId(b.departmentid) as topdeptid from crm_CustomerInfo a left join HrmResource b on a.manager = b.id" +
                " where a.bcjckhsj < getdate() - " + jckhwzhwyxkhy + " and a.status = 5 and b.departmentid > 0";
        log.writeLog("qry5 = " + qry5);
        RecordSet rs5 = new RecordSet();
        rs5.execute(qry5);
        while (rs5.next()) {
            String customerId = Util.null2String(rs5.getString("id"));
            String topDeptId = Util.null2String(rs5.getString("topdeptid"));
            if (!seasCustomerMap.containsKey(customerId)) {
                log.writeLog("qry5 客户id：" + customerId + "不在map中");
                // 获取部门公海的id
                String deptSeasId = Util.null2String(CrmSeasUtil.getDeptSeasId(topDeptId));
                if (!customerId.isEmpty() && !deptSeasId.isEmpty()) {
                    log.writeLog("qry5 加入公海，customerId：" + customerId + ",deptSeasId:" + deptSeasId);
                    // 添加到部门公海
                    CrmSeasUtil.addCustomer2Seas(customerId, deptSeasId);
                }

            }
        }
        // endregion 基础客户 end

        // region 意向客户 start
        // 超过N个月没有联系记录，并且超过N个月没有成交记录
        // 意向客户未联系天数
        String yxkhwlxy = CrmSeasUtil.getDeptSeasDays("yxkhwlxy");
        // 意向客户未成交天数
        String yxkhwcjy = CrmSeasUtil.getDeptSeasDays("yxkhwcjy");
        // 意向不联系且未成交的客户所属顶级部门
        String qry2 = "select a.id,dbo.getTopDepId(b.departmentid) as topdeptid from crm_CustomerInfo a left join HrmResource b on a.manager = b.id " +
                " where (a.zjcjsj < getdate() - " + yxkhwcjy + " or a.lastcontacteddate < getdate() - " + yxkhwlxy + ") and a.status = 2 and b.departmentid > 0";
        log.writeLog("qry2 = " + qry2);
        RecordSet rs2 = new RecordSet();
        rs2.execute(qry2);
        while (rs2.next()) {
            String customerId = Util.null2String(rs2.getString("id"));
            String topDeptId = Util.null2String(rs2.getString("topdeptid"));
            if (!seasCustomerMap.containsKey(customerId)) {
                log.writeLog("qry2 客户id：" + customerId + "不在map中");
                // 获取部门公海的id
                String deptSeasId = Util.null2String(CrmSeasUtil.getDeptSeasId(topDeptId));
                //判断有公海id和客户id
                if (!customerId.isEmpty() && !deptSeasId.isEmpty()) {
                    log.writeLog("qry2 加入公海，customerId：" + customerId + ",deptSeasId:" + deptSeasId);
                    // 添加到部门公海
                    CrmSeasUtil.addCustomer2Seas(customerId, deptSeasId);
                }

            }
        }
        // endregion 意向客户 end

        // region 成交客户 start
        // 若N个月内没有新项目
        // 成交客户无新项目天数
        String cjkhwxxmy = CrmSeasUtil.getDeptSeasDays("cjkhwxxmy");
        // 成交无新项目的客户所属顶级部门
        String qry3 = "select a.id,dbo.getTopDepId(b.departmentid) as topdeptid from crm_CustomerInfo a left join HrmResource b on a.manager = b.id " +
                " where a.zjcjsj < getdate() - " + cjkhwxxmy + " and a.status = 3 and b.departmentid > 0";
        log.writeLog("qry3 = " + qry3);
        RecordSet rs3 = new RecordSet();
        rs3.execute(qry3);
        while (rs3.next()) {
            String customerId = Util.null2String(rs3.getString("id"));
            String topDeptId = Util.null2String(rs3.getString("topdeptid"));
            if (!seasCustomerMap.containsKey(customerId)) {
                log.writeLog("qry3 客户id：" + customerId + "不在map中");
                // 获取部门公海的id
                String deptSeasId = Util.null2String(CrmSeasUtil.getDeptSeasId(topDeptId));
                //判断有公海id和客户id
                if (!customerId.isEmpty() && !deptSeasId.isEmpty()) {
                    log.writeLog("qry3 加入公海，customerId：" + customerId + ",deptSeasId:" + deptSeasId);
                    // 添加到部门公海
                    CrmSeasUtil.addCustomer2Seas(customerId, deptSeasId);
                }

            }
        }
        // endregion 成交客户 end

        // region 部门公海的客户只能存在N天（可配置），超过N天，则回归公司公海 start
        // 获取部门转公司的天数
        String deptSeasDays = CrmSeasUtil.getDeptSeasDays("bmzgsy");
        log.writeLog("deptSeasDays:" + deptSeasDays);
        // 获取公司公海的id
        String companySeasId = Util.null2String(CrmSeasUtil.getCompanySeasId());
        log.writeLog("公司companySeasId:" + companySeasId);
        // 获取部门公海的客户
        String qryDeptSeasCustomersSql = "select customerids = stuff((select ','+cast(customerid as varchar) from crm_seasCustomer " +
                " where seasid not in (" + companySeasId + ") and enterdate < getdate() - " + deptSeasDays + " for xml path('')),1,1,'')";
        log.writeLog("qryDeptSeasCustomersSql:" + qryDeptSeasCustomersSql);
        String customerids = QueryUtil.doQueryFieldValue(qryDeptSeasCustomersSql, "customerids");
        log.writeLog("公司customerids:" + customerids);
        // 所有客户转到公司公海
        if (!companySeasId.isEmpty() && !customerids.isEmpty()) {
            String sqlDept2Company = "update crm_seasCustomer set seasid = " + companySeasId + " where customerid in (" + customerids + ")";
            log.writeLog("sqlDept2Company sql:" + sqlDept2Company);
            ExecuteUtil.executeSql(sqlDept2Company);
            // endregion 部门公海的客户只能存在N天（可配置），超过N天，则回归公司公海 end
        }

        log.writeLog("CrmSeasConvertJob end");
    }

}

