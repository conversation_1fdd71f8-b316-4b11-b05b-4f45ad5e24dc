package cwcc.workflow.crm;


import cwcc.crm.CrmSeasUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.toolbox.db.recordset.QueryUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 功能说明 离职申请人的客户全部归还到部门公海
 *
 * <AUTHOR>
 * @create 2022-11-29 11:00
 */
public class UserCustomers2CrmDeptSeasAction extends BaseBean implements Action {

    @Override
    public String execute(RequestInfo request) {
        try {
            String requestid = request.getRequestid();
            String workflowid = request.getWorkflowid();
            writeLog("UserCustomers2CrmDeptSeasAction requestid:" + requestid + " workflowid:" + workflowid);

            // 取主表数据
            Map mainMap = new HashMap();
            Property[] properties = request.getMainTableInfo().getProperty();
            for (int i = 0; i < properties.length; i++) {
                String name = properties[i].getName();
                String value = Util.null2String(properties[i].getValue());
                mainMap.put(name, value);
            }

            // 申请人
            String sqr = Util.null2String(mainMap.get("sqr"));

            // 获取用户所有客户
            String qryAllCustomersSql = "select ids = stuff((select ','+cast(id as varchar) from CRM_CustomerInfo where deleted = 0 and manager = " + sqr + " for xml path('')),1,1,'')";
            String allCustomers = QueryUtil.doQueryFieldValue(qryAllCustomersSql, "ids");
            writeLog("UserCustomers2CrmDeptSeasAction allCustomers:" + allCustomers);

            if (StringUtils.isNoneBlank(allCustomers)) {
                // 获取用户所在的部门公海,获取用户所在部门的顶级部门，再去查询公海
                String qryTopDeptId = "select dbo.getTopDepId(departmentid) as topdepartmentid from HrmResource where id = ?";
                String topDeptId = QueryUtil.doQueryFieldValueWithArgs(qryTopDeptId, "topdepartmentid", sqr);
                writeLog("UserCustomers2CrmDeptSeasAction topDeptId:" + topDeptId);
                String qryDeptSeasSql = "select id from CRM_SeasInfo where id not in (select zbzghid from uf_ghgz) and concat(',',departmentids,',') like '%," + topDeptId + ",%'";
                String deptSeasId = QueryUtil.doQueryFieldValue(qryDeptSeasSql, "id");
                writeLog("UserCustomers2CrmDeptSeasAction deptSeasId:" + deptSeasId);

                // 所有客户添加到部门公海
                String res = CrmSeasUtil.addCustomer2Seas(allCustomers, deptSeasId);
                //JSONObject jsonObject = new JSONObject(res);
                //String status = jsonObject.getStr("status");

                //if(!"1".equals(status)){
                //    request.getRequestManager().setMessagecontent("客户加入部门公海失败");
                //    return Action.FAILURE_AND_CONTINUE;
                //}
            }
        } catch (Exception ex) {
            writeLog(ex.getMessage());
            request.getRequestManager().setMessagecontent("流程提交时发生异常，请联系管理员！" + ex.getMessage());
            return Action.FAILURE_AND_CONTINUE;
        }
        return Action.SUCCESS;
    }

    //public static void main(String[] args) {
    //    RestfulHelper restfulHelper = new RestfulHelper("123698", "http://58.247.16.74:10060/");
    //    String url = "/api/crm/seas/doJoinSeas?ids=53&seasIds=2&operation=joinSeas";
    //    String res = restfulHelper.doGet(url, "");
    //    System.out.println(res);
    //}
}
