package cwcc.crm;

import weaver.general.BaseBean;
import weaver.toolbox.db.recordset.QueryUtil;
import weaver.toolbox.http.ec.RestfulHelper;

/**
 * 功能说明
 *
 * <AUTHOR>
 * @create 2022-11-30 14:45
 */
public class CrmSeasUtil {

    private static BaseBean bean = new BaseBean();

    /**
     * 获取各类型公海转换的天数
     *
     * @return
     */
    public static String getDeptSeasDays(String type) {
        // 获取各类型公海转换的时间(月)
        String qryDeptSeasMonthsSql = "select " + type + " from uf_ghgz";
        String deptSeasMonths = QueryUtil.doQueryFieldValue(qryDeptSeasMonthsSql, type);
        return String.valueOf(Integer.parseInt(deptSeasMonths) * 30);
    }

    public static String getCompanySeasId() {
        // 获取公司公海的id
        String qryCompanySeasIdSql = "select zbzghid from uf_ghgz";
        return QueryUtil.doQueryFieldValue(qryCompanySeasIdSql, "zbzghid");
    }

    public static String addCustomer2Seas(String customerids, String seasid) {
        // 添加客户到公海
        String appId = bean.getPropValue("crmseas", "appId");
        String oaAddress = bean.getPropValue("crmseas", "oaAddress");
        bean.writeLog("UserCustomers2CrmDeptSeasAction appId:" + appId + " oaAddress:" + oaAddress);

        RestfulHelper restfulHelper = new RestfulHelper(appId, oaAddress);
        String url = "/api/crm/seas/doJoinSeas?ids=" + customerids + "&seasIds=" + seasid + "&operation=joinSeas";
        bean.writeLog("UserCustomers2CrmDeptSeasAction url:" + url);

        String res = restfulHelper.doGet(url, "");
        bean.writeLog("UserCustomers2CrmDeptSeasAction res:" + res);
        return res;
    }

    public static String getDeptSeasId(String topdeptid) {
        // 获取客户所属的部门公海id
        String qryDeptSeasIdSql = "select id from CRM_SeasInfo where id not in (select zbzghid from uf_ghgz) and concat(',',departmentids,',') like '%," + topdeptid + ",%'";
        return QueryUtil.doQueryFieldValue(qryDeptSeasIdSql, "id");
    }
}
