<%@ page import="weaver.general.Util" %>
<%@ page import="weaver.hrm.User" %>
<%@ page import="weaver.hrm.HrmUserVarify" %>
<%@ page import="weaver.toolbox.json.JSONObject" %>
<%@ page import="weaver.toolbox.json.JSONUtil" %>
<%@ page import="java.util.HashMap" %>
<%@ page import="java.util.Map" %>
<%@ page import="weaver.conn.RecordSet" %>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page"/>
<jsp:useBean id="log" class="weaver.general.BaseBean" scope="page" />

<%!
    public Map<String,Integer> getCustomerStatusCount(String customerIds,String userId){
        Map<String,Integer> map = new HashMap<>();

        // 新增客户的类型数量统计
        RecordSet rs = new RecordSet();
        String qryNewCustomerStatusCountSql = "select count(1) as count, status from crm_CustomerInfo where id in (" + customerIds + ") group by status";
        rs.execute(qryNewCustomerStatusCountSql);
        while(rs.next()){
            String status = rs.getString("status");
            int count     = rs.getInt("count");
            map.put(status,count);
        }

        // 现有客户的类型数量统计
        String qryExistCustomerStatusCountSql = "select count(1) as count, status from crm_CustomerInfo where manager = " + userId + " group by status";
        rs.execute(qryExistCustomerStatusCountSql);
        while(rs.next()){
            String status = rs.getString("status");
            int count     = rs.getInt("count");
            if(map.containsKey(status)){
                map.put(status,map.get(status) + count);
            }else{
                map.put(status,count);
            }
        }

        return map;
    }
%>

<%
    response.setContentType("application/json;charset=UTF-8");

    User user = HrmUserVarify.getUser(request, response);
    if (user == null) {
        return;
    }

    String seasCustomerIds = Util.null2String(request.getParameter("seasCustomerIds"));

    String msg = "";

    // 获取各类型客户上限
    String qryCustomerLimitSql = "select b.jckh,b.cjkh,b.yxkh from cus_fielddata a left join uf_howmuchhave b on a.field1 = b.type " +
                                " where a.scope = 'HrmCustomFieldByInfoType' and a.scopeid = -1 and a.id = " + user.getUID();
    rs.execute(qryCustomerLimitSql);
    if(rs.next()){
        // 基础客户
        int jckhLimit = rs.getInt("jckh");
        // 成交客户
        int cjkhLimit = rs.getInt("cjkh");
        // 意向客户
        int yxkhLimit = rs.getInt("yxkh");

        log.writeLog("jckhLimit:" + jckhLimit + ",cjkhLimit:" + cjkhLimit + ",yxkhLimit:" + yxkhLimit);

        // 获取新增各类型客户数量
        Map<String,Integer> map = getCustomerStatusCount(seasCustomerIds,String.valueOf(user.getUID()));
        log.writeLog("map:" + map);

        // 比较哪些类型超过上限，则返回信息
        if(map.containsKey("5") && map.get("5") > jckhLimit){
            msg += "基础客户数量超过上限！";
        }
        if(map.containsKey("3") && map.get("3") > cjkhLimit){
            msg += "成交客户数量超过上限！";
        }
        if(map.containsKey("2") && map.get("2") > yxkhLimit){
            msg += "意向客户数量超过上限！";
        }
    }

    Map result = new HashMap();
    result.put("msg", msg);
    JSONObject jo = JSONUtil.parseObj(result);
    out.println(jo);
%>