<%@ page import="weaver.general.Util" %>
<%@ page import="weaver.hrm.User" %>
<%@ page import="weaver.hrm.HrmUserVarify" %>
<%@ page import="weaver.toolbox.json.JSONObject" %>
<%@ page import="weaver.toolbox.json.JSONUtil" %>
<%@ page import="java.util.HashMap" %>
<%@ page import="java.util.Map" %>
<%@ page import="weaver.conn.RecordSet" %>
<%@ page import="org.apache.commons.lang.StringUtils" %>
<%@ page import="java.util.ArrayList" %>
<%@ page import="static weaver.toolbox.json.JSONObject.*" %>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page"/>
<jsp:useBean id="log" class="weaver.general.BaseBean" scope="page" />

<%!
    // 获取客户状态统计信息
    public HashMap<String, Map<String, String>> getCustomerStatusCount(String customerIds, String userId) {
        RecordSet rs = new RecordSet();
        HashMap<String, Map<String, String>> customerStatusMap = new HashMap<>();
        // 查询新增客户数据
//        String newCustomerSql = "SELECT * FROM crm_CustomerInfo WHERE id IN (" +customerIds + ")";
//        rs.execute(newCustomerSql);
//        while (rs.next()) {
//            String bmlx = rs.getString("bmlx");
//            String id = rs.getString("id");
//            customerStatusMap.computeIfAbsent(bmlx, k -> new HashMap<>()).put(id, "5");
//        }
        // 查询现有客户数据
        String existingCustomerSql = "SELECT * FROM crm_CustomerInfo WHERE manager = ? and  deleted = 0 and (seasFlag is null or seasFlag = 3)";
        rs.executeQuery(existingCustomerSql, userId);
        while (rs.next()) {
                String bmlx = rs.getString("bmlx");
                String id = rs.getString("id");
                String status = rs.getString("status");
                customerStatusMap.computeIfAbsent(bmlx, k -> new HashMap<>()).put(id, status);
        }
        return customerStatusMap;
    }
%>

<%
    response.setContentType("application/json;charset=UTF-8");
    User user = HrmUserVarify.getUser(request, response);
    if (user == null) {
        return;
    }

    String seasCustomerIds = Util.null2String(request.getParameter("seasCustomerIds"));
    String msg = "";
    HashMap<String, Map<String, String>> map = getCustomerStatusCount(seasCustomerIds, String.valueOf(user.getUID()));
    log.writeLog("返回结果："+ JSONUtil.toJsonStr(map));
    int seasCustomer = 0;
    if(StringUtils.isNotBlank(seasCustomerIds)){
        String[] seasCustomerArr =  seasCustomerIds.split(",");
        seasCustomer = seasCustomerArr.length;
    }
    log.writeLog("seasCustomer ："+ seasCustomer);
    int approvalCustomers = 0;
    String newCustomerSql = "SELECT " +
            "    a.id, c1.customerid, t1.city, t1.oldmanager, a.approveUser, a.approveDate, " +
            "    h1.departmentid AS oldmanagerDept, h2.departmentid AS approveDept, s.id AS seasid, t1.manager " +
            "FROM " +
            "    CRM_CustomerInfo t1 " +
            "    LEFT JOIN CRM_SeasCustomer c1 ON t1.id = c1.customerid " +
            "    RIGHT JOIN CRM_SeasCustomerApprove a ON c1.id = a.seasCustomerid " +
            "    RIGHT JOIN CRM_SeasInfo s ON c1.seasid = s.id " +
            "    LEFT JOIN HrmResource h1 ON t1.oldmanager = h1.id " +
            "    LEFT JOIN HrmResource h2 ON a.approveUser = h2.id " +
            "WHERE " +
            "    seasFlag = 2 " +
            "    AND a.approveUser = "+user.getUID();
    rs.execute(newCustomerSql);
    while (rs.next()) {
        approvalCustomers = approvalCustomers+1;
    }
    log.writeLog("approvalCustomers ："+ approvalCustomers);
    for (Map.Entry<String, Map<String, String>> entry : map.entrySet()) {
        String key = entry.getKey();
        Map<String, String> customerStatuses = entry.getValue();

        // 查询客户限制信息
        String qryCustomerLimitSql = "SELECT b.jckh, b.cjkh, b.yxkh FROM cus_fielddata a LEFT JOIN uf_howmuchhave b ON a.field1 = b.type " +
                "WHERE a.scope = 'HrmCustomFieldByInfoType' AND a.scopeid = -1 AND a.id = ? AND b.bmlx = ?";

        rs.executeQuery(qryCustomerLimitSql, user.getUID(), key);

        if (rs.next()) {
            int jckhLimit = rs.getInt("jckh");
            int cjkhLimit = rs.getInt("cjkh");
            int yxkhLimit = rs.getInt("yxkh");
            log.writeLog("jckhLimit: " + jckhLimit + ", cjkhLimit: " + cjkhLimit + ", yxkhLimit: " + yxkhLimit);

            // 统计每种状态类型的客户数量
            HashMap<String, Integer> statusCountMap = new HashMap<>();
            for (String status : customerStatuses.values()) {
                statusCountMap.put(status, statusCountMap.getOrDefault(status, 0) + 1);
            }
            log.writeLog("statusCountMap ："+ JSONUtil.toJsonStr(statusCountMap));
            int jckh = statusCountMap.getOrDefault("5", 0);

            jckh = jckh + approvalCustomers +seasCustomer;
            log.writeLog("jckh ："+ jckh);
            // 检查是否超过限制并生成消息
            if (jckh > jckhLimit) {
                msg += "基础客户数量超过上限！";
            }
        }
        log.writeLog("msg ："+ msg);
        if (StringUtils.isNotBlank(msg)) {
            Map<String, String> result = new HashMap<>();
            result.put("msg", msg);
            JSONObject jsonResponse = JSONUtil.parseObj(result);
            out.println(jsonResponse);
            break;
        }
    }
%>
