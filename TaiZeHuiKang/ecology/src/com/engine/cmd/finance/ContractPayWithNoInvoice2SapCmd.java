package com.engine.cmd.finance;

import com.action.constant.ApiMethodConstant;
import com.action.constant.ApiResultConstant;
import com.action.finance.JournalEntryCommon;
import com.action.util.TransferUtil;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.http.constrant.SoapResultCst;
import com.engine.parent.http.dto.SoapResult;
import com.engine.parent.http.util.ApiLogUtil;
import com.engine.parent.query.util.QueryResultUtil;
import com.wbi.util.Util;
import weaver.conn.RecordSet;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/11-17:46
 * @description : 合同付款-无发票
 */
public class ContractPayWithNoInvoice2SapCmd extends AbstractCommonCommand<Map<String, Object>> {

    /**
     * 构造器
     *
     * @param params
     */
    public ContractPayWithNoInvoice2SapCmd(Map<String, Object> params) {
        this.params = params;
        this.apiAddress = "";
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    //共有变量，注意线程安全问题
    //主接口地址
    private volatile String apiAddress;

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        //请求id
        String requestId = Util.null2String(params.get("requestId"));
        //先检查该接口是否已经走过，走过的话直接跳过
        if (ApiLogUtil.checkApiHasRun(requestId, ApiMethodConstant.JournalEntry)) {
            result.put("status", ApiResultConstant.SUCCESS);
            return result;
        }
        //查询参数
        Map<String, Object> mapParams = queryDataForApiParams(requestId);
        //接口相关错误信息
        String erroMsg = "";
        if (mapParams.get("mainData") == null) {
            erroMsg = "未查询到参数数据，请检查流程数据！";
        }
        if ("".equals(apiAddress)) {
            erroMsg = "未获取到接口主地址，请检查分部数据！";
        }
        if (erroMsg.isEmpty()) {
            //发送接口
            mapParams.put("requestId", requestId);
            mapParams.put("userId", params.get("userId"));
            //需要传现金流节点
            mapParams.put("needPrimaryFormItems", "1");
            SoapResult sr = JournalEntryCommon.sendData(mapParams, apiAddress);
            if (sr.getStatus() == SoapResultCst.SOAP_RESULT_FAIL) {
                erroMsg = sr.getResultMsg();
            }
        }
        if (!erroMsg.isEmpty()) {
            result.put("status", ApiResultConstant.NORMAL_ERRO);
            result.put("erroMsg", erroMsg);
        } else {
            result.put("status", ApiResultConstant.SUCCESS);
        }
        return result;
    }


    @SuppressWarnings("unchecked")
    private Map<String, Object> queryDataForApiParams(String requestId) {
        Map<String, Object> result = new HashMap<>();
        RecordSet rs = new RecordSet();
        String sql;
        //主表数据
        sql = "SELECT " +
                //管理分部，用于判断调用不同端口
                "m.fycdfb AS FB, " +
                //摘要
                "m.bt AS Memo, " +
                //参考2
                "m.htbh AS Reference2, " +
                //过账日期
                " CONVERT ( VARCHAR ( 12 ), CAST(m.sjfkrq as date), 112 ) AS ReferenceDate, " +
                //到期日
                " CONVERT ( VARCHAR ( 12 ), CAST(m.sjfkrq as date), 112 ) AS DueDate, " +
                //单据日期
                " CONVERT ( VARCHAR ( 12 ), CAST(m.sjfkrq as date), 112 ) AS TaxDate " +
                "FROM " +
                //其他员工报销主表
                " formtable_main_44 m " +
                "WHERE " +
                " m.requestId = ? ";
        rs.executeQuery(sql, requestId);
        if (rs.next()) {
            apiAddress = TransferUtil.getAddressByDepartmentCode(Util.null2String(rs.getString("FB")));
            result.put("mainData", QueryResultUtil.getJSONList(rs.getArray(), rs.getData()).get(0));
        } else {
            result.put("mainData", null);
        }
        sql = "SELECT " +
                //科目代码，如果是往来填写业务伙伴代码
                "TBA.ShortName, " +
                //科目代码
                "TBA.AccountCode, " +
                //贷方金额
                "TBA.Credit, " +
                //借方金额
                "TBA.Debit, " +
                //研发项目
                "xm.sapxmdm AS ProjectCode, " +
                //现金流项目
                "xj.sapmrz AS CashFlowLineItemID, " +
                //成本中心 1：部门
                "hd.sapbmdm AS CostingCode  " +
                "FROM " +
                "( " +
                "SELECT " +
                "m.gysdm AS ShortName, " +
                "CAST ( m.yfzkkm AS nvarchar ) AS AccountCode, " +
                "d1.fkje1 AS Credit, " +
                "d1.ccfk AS Debit, " +
                "d1.xm AS ProjectCode, " +
                "'' AS CashFlowLineItemID, " +
                "d1.fycdbm AS CostingCode  " +
                "FROM " +
                " formtable_main_44_dt1 d1 " +
                " LEFT JOIN formtable_main_44 m ON ( m.id = d1.mainid ) " +
                "LEFT JOIN FnaBudgetSubject_0 fs ON ( fs.id = d1.yskmjmy )  " +
                "WHERE " +
                "m.requestId = ? UNION ALL " +
                "SELECT " +
                "m.gysdm AS ShortName, " +
                "CAST ( m.yfzkkm AS nvarchar ) AS AccountCode, " +
                "d2.fkje1 AS Credit, " +
                "d2.ccfk AS Debit, " +
                "d2.xm AS ProjectCode, " +
                "'' AS CashFlowLineItemID, " +
                "d2.fycdbm AS CostingCode  " +
                "FROM " +
                " formtable_main_44_dt2 d2 " +
                " LEFT JOIN formtable_main_44 m ON ( m.id = d2.mainid ) " +
                "LEFT JOIN FnaBudgetSubject_0 fs ON ( fs.id = d2.yskmjmy )  " +
                "WHERE " +
                "m.requestId = ? UNION ALL " +
                "SELECT " +
                "m.yxkm AS ShortName, " +
                "m.yxkm AS AccountCode, " +
                "m.fkhj AS Credit, " +
                "m.fkhj1 AS Debit, " +
                "m.xm AS ProjectCode, " +
                "m.xjlxm AS CashFlowLineItemID, " +
                "m.bmjky AS CostingCode  " +
                "FROM " +
                "formtable_main_44 m  " +
                "WHERE " +
                "m.requestId = ?  " +
                ") TBA " +
                "LEFT JOIN hrmdepartmentdefined hd ON ( hd.deptid = TBA.CostingCode ) " +
                "LEFT JOIN uf_xmgltz xm ON ( xm.id = TBA.ProjectCode ) " +
                "LEFT JOIN uf_XJLXM xj ON ( xj.id = TBA.CashFlowLineItemID )";
        rs.executeQuery(sql, requestId, requestId, requestId);
        if (rs.next()) {
            result.put("itemData", QueryResultUtil.getJSONList(rs.getArray(), rs.getData()));
        } else {
            result.put("itemData", null);
        }
        return result;
    }
}
