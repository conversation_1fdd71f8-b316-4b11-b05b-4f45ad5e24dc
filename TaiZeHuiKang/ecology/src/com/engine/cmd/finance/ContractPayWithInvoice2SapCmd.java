package com.engine.cmd.finance;

import com.action.constant.ApiAddressConstant;
import com.action.constant.ApiMethodConstant;
import com.action.constant.ApiResultConstant;
import com.action.util.AddParamsUtil;
import com.action.util.TransferUtil;
import com.constant.ApiLogConstant;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.http.constrant.SoapResultCst;
import com.engine.parent.http.dto.SoapRequestDto;
import com.engine.parent.http.dto.SoapResult;
import com.engine.parent.http.util.ApiLogUtil;
import com.engine.parent.http.util.SoapRequestUtil;
import com.engine.parent.query.util.QueryResultUtil;
import com.wbi.util.Util;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import weaver.conn.RecordSet;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/11-17:46
 * @description : 合同付款-有发票
 */
public class ContractPayWithInvoice2SapCmd extends AbstractCommonCommand<Map<String, Object>> {

    /**
     * 构造器
     *
     * @param params
     */
    public ContractPayWithInvoice2SapCmd(Map<String, Object> params) {
        this.params = params;
        this.apiAddress = "";
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    //共有变量，注意线程安全问题
    //主接口地址
    private volatile String apiAddress;

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        //请求id
        String requestId = Util.null2String(params.get("requestId"));
        //默认成功
        result.put("status", ApiResultConstant.SUCCESS);
        //先检查该接口是否已经走过，走过的话直接跳过
        if (ApiLogUtil.checkApiHasRun(requestId, ApiMethodConstant.OutgoingPayments)) {
            return result;
        }
        //查询参数
        Map<String, Object> mapParams = queryDataForApiParams(requestId);
        //接口相关错误信息
        String erroMsg = "";
        if (mapParams.get("mainData") == null) {
            erroMsg = "未查询到参数数据，请检查流程数据！";
        }
        if ("".equals(apiAddress)) {
            erroMsg = "未获取到接口主地址，请检查分部数据！";
        }
        if (erroMsg.isEmpty()) {
            //发送接口
            erroMsg = sendData(mapParams);
        }
        if (!erroMsg.isEmpty()) {
            result.put("status", ApiResultConstant.NORMAL_ERRO);
            result.put("erroMsg", erroMsg);
        }
        return result;
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> queryDataForApiParams(String requestId) {
        Map<String, Object> result = new HashMap<>();
        RecordSet rs = new RecordSet();
        String sql;
        //主表数据
        sql = " SELECT " +
                " m.fycdfb AS FB, " +
                " CONVERT ( VARCHAR ( 12 ), CAST(m.sjfkrq as date), 112 ) AS TransferDate, " +
                " CONVERT ( VARCHAR ( 12 ), CAST(m.sjfkrq as date), 112 ) AS DueDate, " +
                " CONVERT ( VARCHAR ( 12 ), CAST(m.sjfkrq as date), 112 ) AS TaxDate, " +
                " m.fkhj AS TransferSum, " +
                " m.fkhj AS AmountLC, " +
                " xj.sapmrz AS CashFlowLineItemID, " +
                " m.yxkm AS TransferAccount, " +
                " m.gysdm AS CardCode, " +
                " xg.sapxmdm AS ProjectCode, " +
                " 'S' AS DocType, " +
                " 'pmtBankTransfer' AS PaymentMeans, " +
                " 'it_PurchaseInvoice' AS invoiceType,  " +
                //2022-02-21 新增接口字段,pingcode TZH-9
                " m.bt AS JournalRemarks " +
                " FROM " +
                " formtable_main_44 m " +
                " LEFT JOIN uf_xmgltz xg ON ( xg.id = m.xm ) " +
                " LEFT JOIN uf_XJLXM xj ON ( m.xjlxm = xj.id )  " +
                " WHERE m.requestid = ? ";
        rs.executeQuery(sql, requestId);

        if (rs.next()) {
            apiAddress = TransferUtil.getAddressByDepartmentCode(Util.null2String(rs.getString("FB")));
            result.put("mainData", QueryResultUtil.getJSONList(rs.getArray(), rs.getData()).get(0));
        } else {
            result.put("mainData", null);
        }
        sql = "SELECT " +
                " dt1.ccfk AS SumApplied, " +
                " dt1.sapyffphm AS DocEntry,  " +
                " 'it_PurchaseInvoice' AS InvoiceType " +
                " FROM " +
                " formtable_main_44_dt1 dt1 " +
                " LEFT JOIN formtable_main_44 m ON ( dt1.mainid = m.id )  " +
                " WHERE " +
                " m.requestid = ? UNION ALL " +
                " SELECT " +
                " dt2.ccfk AS SumApplied, " +
                " dt2.sapyffphm AS DocEntry,  " +
                " 'it_PurchaseInvoice' AS InvoiceType " +
                " FROM " +
                " formtable_main_44_dt2 dt2 " +
                " LEFT JOIN formtable_main_44 m ON ( dt2.mainid = m.id )  " +
                " WHERE " +
                " m.requestid = ? ";
        rs.executeQuery(sql, requestId, requestId);
        if (rs.next()) {
            result.put("itemData", QueryResultUtil.getJSONList(rs.getArray(), rs.getData()));
        } else {
            result.put("itemData", null);
        }
        return result;
    }


    @SuppressWarnings("unchecked")
    private String sendData(Map<String, Object> mapParams) {
        Map<String, Object> mainData = (Map<String, Object>) mapParams.get("mainData");
        List<Map<String, Object>> itemData = (List<Map<String, Object>>) mapParams.get("itemData");
        String requestId = Util.null2String(mapParams.get("requestId"));
        int userId = Util.getIntValue(Util.null2String(mapParams.get("userId")));
        //文档对象
        Document doc;
        Element rootElemtent, node1, node2, node3, row, eachParam;
        //这是在创建一个根节点
        rootElemtent = DocumentHelper.createElement("OutgoingPayments");
        //把根节点变成一个Document 对象方便添加子节点
        doc = DocumentHelper.createDocument(rootElemtent);
        //三个子节点
        node1 = rootElemtent.addElement("Payments");
        node2 = rootElemtent.addElement("Payments_Invoices");
        node3 = rootElemtent.addElement("PrimaryFormItems");

        String rowParams;
        //添加主节点Payments参数
        row = node1.addElement("row");
        rowParams = "CardCode,DocType,TransferAccount,TransferSum,TransferDate,TaxDate,ProjectCode,JournalRemarks";
        AddParamsUtil.addRowData(row, rowParams, mainData);

        int i = 0;
        //添加子节点Payments_Invoices参数
        for (Map<String, Object> map : itemData) {
            row = node2.addElement("row");
            //行号
            eachParam = row.addElement("LineNum");
            eachParam.setText(String.valueOf(i));

            rowParams = "DocEntry,SumApplied,InvoiceType";
            AddParamsUtil.addRowData(row, rowParams, map);
            i++;
        }

        //添加子节点PrimaryFormItems参数
        row = node3.addElement("row");
        rowParams = "CashFlowLineItemID,PaymentMeans,AmountLC";
        AddParamsUtil.addRowData(row, rowParams, mainData);

        //发送接口
        SoapRequestDto dto = new SoapRequestDto();
        dto.setApiAddress(apiAddress);
        dto.setMethodName(ApiMethodConstant.OutgoingPayments);
        dto.setNameSpace(ApiAddressConstant.DEFAULT_NAMESPACE_URI);
        dto.setParamNode("Request");
        dto.setSendDataStr(doc.asXML());
        dto.setResultNode("OutgoingPaymentsResult");
        dto.setRequestId(requestId);
        dto.setUserId(userId);
        dto.setModuleId(ApiLogConstant.LOG_MOUDLE_ID);
        SoapResult sr = SoapRequestUtil.sendData(dto);
        if (sr.getStatus() == SoapResultCst.SOAP_RESULT_FAIL) {
            return sr.getResultMsg();
        }
        return "";
    }
}
