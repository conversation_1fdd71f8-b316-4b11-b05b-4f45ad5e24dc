package com.engine.cmd.contract;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.wbi.util.Util;
import weaver.conn.RecordSet;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/11-13:07
 * @description : 检查要终止合同类型
 */
public class CheckTargetContractTypeCmd extends AbstractCommonCommand<Map<String, Object>> {

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * 构造方法
     *
     * @param params
     */
    public CheckTargetContractTypeCmd(Map<String, Object> params) {
        this.params = params;

    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        //判断合同类型
        result.put("flag", queryData(Util.null2String(params.get("requestid"))));
        return result;
    }

    private boolean queryData(String requestId) {
        RecordSet rs = new RecordSet();
        String sql = " SELECT " +
                " b.sapcgddhm  " +
                " FROM " +
                " uf_httz a " +
                " INNER JOIN formtable_main_9 b ON ( a.id= b.glhtbh )  " +
                " AND a.htlx IN ( 2, 3, 4, 6, 7, 8, 9 )  " +
                " AND a.htzje is not null  AND a.htzje != 0  " +
                " AND b.requestId= ? ";
        rs.executeQuery(sql, requestId);
        return rs.next();
    }
}
