package com.engine.cmd.contract;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.wbi.util.Util;
import weaver.conn.RecordSet;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/11-13:07
 * @description : 检查合同类型
 */
public class CheckContractTypeCmd extends AbstractCommonCommand<Map<String, Object>> {

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * 构造方法
     *
     * @param params
     */
    public CheckContractTypeCmd(Map<String, Object> params) {
        this.params = params;

    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        //判断合同类型
        result.put("flag", queryData(Util.null2String(params.get("requestid"))));
        return result;
    }

    private boolean queryData(String requestId) {
        RecordSet rs = new RecordSet();
        String sql = "select id from formtable_main_9 " +
                " where htlx in (2,3,4,6,7,8,9) and requestId = ? " +
                " AND htzje is not null AND htzje != 0  ";
        rs.executeQuery(sql, requestId);
        return rs.next();
    }
}
