package com.engine.cmd.contract;

import com.action.constant.ApiAddressConstant;
import com.action.constant.ApiMethodConstant;
import com.action.constant.ApiResultConstant;
import com.action.util.AddParamsUtil;
import com.action.util.TransferUtil;
import com.constant.ApiLogConstant;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.http.constrant.SoapResultCst;
import com.engine.parent.http.dto.SoapRequestDto;
import com.engine.parent.http.dto.SoapResult;
import com.engine.parent.http.util.ApiLogUtil;
import com.engine.parent.http.util.SoapRequestUtil;
import com.engine.parent.query.util.QueryResultUtil;
import com.wbi.util.Util;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.general.BaseBean;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/11-13:07
 * @description : 采购订单接口
 */
public class PurchaseOrderCmd extends AbstractCommonCommand<Map<String, Object>> {

    //主接口地址
    private String apiAddress;
    //接口相关错误信息
    private String erroMsg;
    //物料类型
    private String materialTyle;

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * 构造方法
     *
     * @param params
     * @param user
     */
    public PurchaseOrderCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        this.apiAddress = "";
        this.erroMsg = "";
        this.materialTyle = "";
    }

    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        BaseBean bb = new BaseBean();
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> mapParams;
        List<Map<String, Object>> vendorList;
        //默认成功
        result.put("status", ApiResultConstant.SUCCESS);
        //请求id
        String requestId = Util.null2String(params.get("requestid"));

        //先检查该接口是否已经走过，走过的话直接跳过该接口
        bb.writeLog("检查Purchase_Order是否运行过，requestid：" + requestId);
        if (ApiLogUtil.checkApiHasRun(requestId, ApiMethodConstant.Purchase_Order)) {
            bb.writeLog("检查Purchase_Order是否运行过，requestid：" + requestId + "运行过");
            result.put("status", ApiResultConstant.SUCCESS);
            return result;
        }
        bb.writeLog("检查Purchase_Order是否运行过，requestid：" + requestId + "没有运行过");
        //校验供应商条件(有且只有一条为收款方的数据)，如果不符合直接跳过该接口
        mapParams = queryVendorData(requestId);
        if (mapParams.get("vendorData") != null) {
            vendorList = (List<Map<String, Object>>) mapParams.get("vendorData");
            if (vendorList.size() != 1) {
                result.put("status", ApiResultConstant.SUCCESS);
                return result;
            }
        } else {
            result.put("status", ApiResultConstant.SUCCESS);
            return result;
        }
        Map<String, Object> vendorMap = vendorList.get(0);
        //查询表数据
        mapParams = this.queryDataForApiParams(requestId);
        if ("".equals(apiAddress)) {
            //流程提交失败信息内容
            erroMsg = "未获取到接口主地址，请检查分部数据！";
        }
        if (erroMsg.isEmpty()) {
            sendData(mapParams, vendorMap);
        }
        if (!erroMsg.isEmpty()) {
            result.put("status", ApiResultConstant.NORMAL_ERRO);
            result.put("erroMsg", erroMsg);
        }
        return result;
    }

    /**
     * 发送数据
     *
     * @param mapParams
     */
    @SuppressWarnings("unchecked")
    private void sendData(Map<String, Object> mapParams, Map<String, Object> vendorMap) {
        //文档对象
        Document doc;
        String rowParams;
        Map<String, Object> mainData;
        mainData = (Map<String, Object>) mapParams.get("mainData");
        Element rootElemtent, node1, node2, row, eachParam;
        //这是在创建一个根节点
        rootElemtent = DocumentHelper.createElement("Purchase_Order");
        //把根节点变成一个Document 对象方便添加子节点
        doc = DocumentHelper.createDocument(rootElemtent);
        //子节点
        node1 = rootElemtent.addElement("Documents");
        node2 = rootElemtent.addElement("Document_Lines");
        //添加节点参数

        //转换参数
        mainData.put("Currency", TransferUtil.transferCurrency(Util.null2String(mainData.get("Currency"))));

        //node1表头
        row = node1.addElement("row");

        rowParams = "DocDate,TaxDate,DocDueDate";
        AddParamsUtil.addRowData(row, rowParams, mainData);

        if (!Util.null2String(vendorMap.get("CardCode")).isEmpty()) {
            eachParam = row.addElement("CardCode");
            eachParam.setText(Util.null2String(vendorMap.get("CardCode")));
        }
        if (!Util.null2String(vendorMap.get("CardName")).isEmpty()) {
            eachParam = row.addElement("CardName");
            eachParam.setText(Util.null2String(vendorMap.get("CardName")));
        }
        rowParams = "NumAtCard";
        AddParamsUtil.addRowData(row, rowParams, mainData);

        eachParam = row.addElement("DocType");
        String currency = Util.null2String(mainData.get("Currency"));
        //物资
        if ("0".equals(materialTyle)) {
            if (mapParams.get("itemData_material") == null) {
                erroMsg = "未获取到物资类明细数据，请检查数据！";
                return;
            }
            eachParam.setText("dDocument_Items");
            assembleParamsByMaterial(mapParams, node2, currency);
        }
        //资产/服务
        if ("1".equals(materialTyle) || "2".equals(materialTyle)) {
            if (mapParams.get("itemData_service") == null) {
                erroMsg = "未获取到资产/服务类明细数据，请检查数据！";
                return;
            }
            eachParam.setText("dDocument_Service");
            assembleParamsByService(mapParams, node2, currency);
        }

        //发送接口
        SoapRequestDto dto = new SoapRequestDto();
        dto.setApiAddress(apiAddress);
        dto.setMethodName(ApiMethodConstant.Purchase_Order);
        dto.setNameSpace(ApiAddressConstant.DEFAULT_NAMESPACE_URI);
        dto.setParamNode("Request");
        dto.setSendDataStr(doc.asXML());
        dto.setResultNode("Purchase_OrderResult");
        dto.setRequestId(Util.null2String(params.get("requestid")));
        dto.setUserId(user.getUID());
        dto.setModuleId(ApiLogConstant.LOG_MOUDLE_ID);
        SoapResult sr = SoapRequestUtil.sendData(dto);
        if (sr.getStatus() == SoapResultCst.SOAP_RESULT_SUCCESS) {
            //成功调用-更新返回字段
            updateSAPCode2Workflow(sr.getResultMsg());
        } else {
            erroMsg = sr.getResultMsg();
        }
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> queryDataForApiParams(String requestId) {
        Map<String, Object> result = new HashMap<>();
        RecordSet rs = new RecordSet();
        //查询主表数据
        String sql = "SELECT" +
                //管理分部，用于判断调用不同端口
                " m.wfqyzt AS FB, " +
                //采购合同号
                " m.htbh AS NumAtCard, " +
                //物料类型
                " m.bdwlx, " +
                //要求到货日期
                " CONVERT ( VARCHAR ( 12 ), CAST(m.htksr as date), 112 )  AS DocDueDate, " +
                //过账日期
                " CONVERT ( VARCHAR ( 12 ), CAST(m.htksr as date), 112 )  AS DocDate, " +
                //单据日期
                " CONVERT ( VARCHAR ( 12 ), CAST(m.htksr as date), 112 )  AS TaxDate, " +
                //币种
                " m.bz AS Currency " +
                " FROM " +
                //合同审批流程新建订单（purchase_order）主表
                " formtable_main_9 m " +
                " WHERE " +
                " m.requestId = ? ";

        rs.executeQuery(sql, requestId);
        if (rs.next()) {
            apiAddress = TransferUtil.getAddressByDepartmentCode(Util.null2String(rs.getString("FB")));
            materialTyle = Util.null2String(rs.getString("bdwlx"));
            result.put("mainData", QueryResultUtil.getJSONList(rs.getArray(), rs.getData()).get(0));
        }

        //服务类型明细
        sql = " SELECT " +
                //应收应付事务代码
                " d4.swdm AS U_OtherPostItem, " +
                //服务描述
                " d4.ms AS ItemDescription, " +
                //研发项目代码
                " xg.sapxmdm AS ProjectCode, " +
                //税收组
                " st.sm AS VatGroup, " +
                //含税单价
                " d4.hszj AS PriceAfterVAT, " +
                //税后总计
                " d4.hszj AS GrossTotal, " +
                //成本中心（部门）
                " hd.sapbmdm AS CostingCode, " +
                //费用科目代码
                " d4.sapkmdm AS AccountCode " +
                " FROM " +
                //子表（服务）
                " formtable_main_9_dt4 d4 " +
                //主表关联
                " LEFT JOIN formtable_main_9 m ON ( d4.mainid = m.id ) " +
                //项目关联
                " LEFT JOIN uf_xmgltz xg ON ( xg.id = d4.xm )  " +
                //税码关联
                " LEFT JOIN uf_smtz st ON ( st.id = d4.sm )  " +
                //部门关联
                " LEFT JOIN hrmdepartmentdefined hd ON ( hd.deptid = d4.fybm ) " +
                " WHERE " +
                " m.requestId = ? ";
        rs.executeQuery(sql, requestId);
        if (rs.next()) {
            result.put("itemData_service", QueryResultUtil.getJSONList(rs.getArray(), rs.getData()));
        }

        //物料类型明细
        sql = " SELECT " +
                //物料编码
                " d5.wldm AS ItemCode, " +
                //研发项目代码
                " xg.sapxmdm AS ProjectCode, " +
                //数量
                " d5.sl AS Quantity, " +
                //税前价格
                " d5.djbhs AS Price, " +
                //税后价格
                " d5.hsdj AS PriceAfterVAT, " +
                //税收组代码
                " st.sm AS VatGroup,  " +
                //成本中心（部门）
                " hd.sapbmdm AS CostingCode " +
                " FROM " +
                " formtable_main_9_dt5 d5 " +
                //主表关联
                " LEFT JOIN formtable_main_9 m ON ( d5.mainid = m.id )  " +
                //项目关联
                " LEFT JOIN uf_xmgltz xg ON ( xg.id = d5.xm )  " +
                //税码关联
                " LEFT JOIN uf_smtz st ON ( st.id = d5.sm )  " +
                //部门关联
                " LEFT JOIN hrmdepartmentdefined hd ON ( hd.deptid = d5.bm ) " +
                " WHERE " +
                " m.requestId = ? ";
        rs.executeQuery(sql, requestId);
        if (rs.next()) {
            result.put("itemData_material", QueryResultUtil.getJSONList(rs.getArray(), rs.getData()));
        }
        return result;
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> queryVendorData(String requestId) {
        Map<String, Object> result = new HashMap<>();
        RecordSet rs = new RecordSet();
        //查询明细数据
        String sql = " SELECT  " +
                //供应商代码
                "  u.gysdm AS CardCode,  " +
                //供应商全称
                "  u.gysqc  AS CardName  " +
                "  FROM  " +
                "  formtable_main_9_dt2 d  " +
                "  LEFT JOIN formtable_main_9 m ON ( d.mainid = m.id )  " +
                "  LEFT JOIN uf_gystz u ON ( d.gysmc = u.id )   " +
                "  WHERE  " +
                "  m.requestId = ? " +
                "  AND d.sfwskf = 0  ";
        rs.executeQuery(sql, requestId);
        if (rs.next()) {
            result.put("vendorData", QueryResultUtil.getJSONList(rs.getArray(), rs.getData()));
        }
        return result;
    }

    @SuppressWarnings("unchecked")
    private void assembleParamsByMaterial(Map<String, Object> mapParams, Element itemNode, String currency) {
        List<Map<String, Object>> itemData;

        itemData = (List<Map<String, Object>>) mapParams.get("itemData_material");
        String rowParams;
        Element row, eachParam;
        //node2 行数据
        int i = 0;
        for (Map<String, Object> map : itemData) {
            row = itemNode.addElement("row");

            eachParam = row.addElement("LineNum");
            eachParam.setText(String.valueOf(i));

            rowParams = "ItemCode,Quantity,Price,PriceAfterVAT,ProjectCode,CostingCode";
            AddParamsUtil.addRowData(row, rowParams, map);

            eachParam = row.addElement("Currency");
            eachParam.setText(currency);

            rowParams = "VatGroup";
            AddParamsUtil.addRowData(row, rowParams, map);
            i++;
        }

    }

    @SuppressWarnings("unchecked")
    private void assembleParamsByService(Map<String, Object> mapParams, Element itemNode, String currency) {
        List<Map<String, Object>> itemData;
        itemData = (List<Map<String, Object>>) mapParams.get("itemData_service");
        String rowParams;
        Element row, eachParam;
        //node2 行数据
        int i = 0;
        for (Map<String, Object> map : itemData) {
            row = itemNode.addElement("row");

            eachParam = row.addElement("LineNum");
            eachParam.setText(String.valueOf(i));

            rowParams = "ItemDescription";
            AddParamsUtil.addRowData(row, rowParams, map);

            eachParam = row.addElement("Currency");
            eachParam.setText(currency);

            rowParams = "ProjectCode,VatGroup,LineTotal,GrossTotal,U_OtherPostItem,PriceAfterVAT,CostingCode,AccountCode";
            AddParamsUtil.addRowData(row, rowParams, map);
            i++;
        }
    }

    private void updateSAPCode2Workflow(String sapCode) {
        RecordSetTrans rst = new RecordSetTrans();
        //自动提交设为false
        rst.setAutoCommit(false);
        String sql = "update formtable_main_9  set sapcgddhm = ? where requestId = ? ";
        try {
            rst.executeUpdate(sql, sapCode, Util.null2String(params.get("requestid")));
            //手动提交事务
            rst.commit();
        } catch (Exception e) {
            //异常手动回滚
            rst.rollback();
            e.printStackTrace();
        }

    }
}
