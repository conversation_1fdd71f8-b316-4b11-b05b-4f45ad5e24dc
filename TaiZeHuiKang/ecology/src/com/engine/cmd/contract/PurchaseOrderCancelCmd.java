package com.engine.cmd.contract;

import com.action.constant.ApiAddressConstant;
import com.action.constant.ApiMethodConstant;
import com.action.constant.ApiResultConstant;
import com.action.util.TransferUtil;
import com.constant.ApiLogConstant;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.http.constrant.SoapResultCst;
import com.engine.parent.http.dto.SoapRequestDto;
import com.engine.parent.http.dto.SoapResult;
import com.engine.parent.http.util.ApiLogUtil;
import com.engine.parent.http.util.SoapRequestUtil;
import com.wbi.util.Util;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import weaver.conn.RecordSet;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/11-13:07
 * @description : 采购订单取消接口
 */
public class PurchaseOrderCancelCmd extends AbstractCommonCommand<Map<String, Object>> {

    //共有变量，注意线程安全问题
    //主接口地址
    private volatile String apiAddress;
    private volatile String sapCode;
    //接口相关错误信息
    private volatile String erroMsg;

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * 构造方法
     *
     * @param params
     * @param user
     */
    public PurchaseOrderCancelCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        this.apiAddress = "";
        this.erroMsg = "";
        this.sapCode = "";
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        //默认成功
        result.put("status", ApiResultConstant.SUCCESS);
        //请求id
        String requestId = Util.null2String(params.get("requestid"));

        //先检查该接口是否已经走过，走过的话直接跳过
        if (ApiLogUtil.checkApiHasRun(requestId, ApiMethodConstant.Purchase_Order_Cancel)) {
            result.put("status", ApiResultConstant.SUCCESS);
            return result;
        }

        //查询数据，设置耳机口地址
        this.queryDataForApiParams(requestId);
        if (sapCode.isEmpty()) {
            erroMsg = "未查询到SAP采购订单号,请检查数据！";
        }
        if (apiAddress.isEmpty()) {
            //流程提交失败信息内容
            erroMsg = "未获取到接口主地址，请检查分部数据！";
        }
        if (erroMsg.isEmpty()) {
            sendData();
        }
        if (!erroMsg.isEmpty()) {
            if (erroMsg.contains(SoapResultCst.SAP_RESULT_PO_BISFAIL_MSG)) {
                result.put("status", ApiResultConstant.BUSINESS_ERRO);
            } else {
                result.put("status", ApiResultConstant.NORMAL_ERRO);
            }
            result.put("erroMsg", erroMsg);
        }
        return result;
    }

    /**
     * 发送数据
     */
    private void sendData() {
        //文档对象
        Document doc;
        Element rootElemtent, node1, row, eachParam;
        //这是在创建一个根节点
        rootElemtent = DocumentHelper.createElement("Purchase_Order");
        //把根节点变成一个Document 对象方便添加子节点
        doc = DocumentHelper.createDocument(rootElemtent);
        //子节点
        node1 = rootElemtent.addElement("Documents");

        //添加节点参数
        //节点行数据
        row = node1.addElement("row");
        eachParam = row.addElement("DocNum");
        eachParam.setText(sapCode);

        //发送接口
        SoapRequestDto dto = new SoapRequestDto();
        dto.setApiAddress(apiAddress);
        dto.setMethodName(ApiMethodConstant.Purchase_Order_Cancel);
        dto.setNameSpace(ApiAddressConstant.DEFAULT_NAMESPACE_URI);
        dto.setParamNode("Request");
        dto.setSendDataStr(doc.asXML());
        dto.setResultNode("Purchase_Order_CancelResult");
        dto.setRequestId(Util.null2String(params.get("requestid")));
        dto.setUserId(user.getUID());
        dto.setModuleId(ApiLogConstant.LOG_MOUDLE_ID);
        SoapResult sr = SoapRequestUtil.sendData(dto);
        if (sr.getStatus() == SoapResultCst.SOAP_RESULT_FAIL) {
            erroMsg = sr.getResultMsg();
        }
    }

    private void queryDataForApiParams(String requestId) {
        RecordSet rs = new RecordSet();
        //查询明细数据
        String sql = " SELECT  " +
                //我方签约公司
                "  m.wfqyzt AS FB,  " +
                "  m.glhtsapdh AS SAPCODE  " +
                "  FROM  " +
                "  formtable_main_9 m  " +
                "  WHERE  " +
                "  m.requestId = ? ";
        rs.executeQuery(sql, requestId);
        if (rs.next()) {
            apiAddress = TransferUtil.getAddressByDepartmentCode(Util.null2String(rs.getString("FB")));
            sapCode = Util.null2String(rs.getString("SAPCODE"));
        }
    }
}
