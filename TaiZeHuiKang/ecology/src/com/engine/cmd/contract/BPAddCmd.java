package com.engine.cmd.contract;

import com.action.constant.ApiAddressConstant;
import com.action.constant.ApiMethodConstant;
import com.action.constant.ApiResultConstant;
import com.action.util.AddParamsUtil;
import com.action.util.TransferUtil;
import com.constant.ApiLogConstant;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.http.constrant.SoapResultCst;
import com.engine.parent.http.dto.SoapRequestDto;
import com.engine.parent.http.dto.SoapResult;
import com.engine.parent.http.util.ApiLogUtil;
import com.engine.parent.http.util.SoapRequestUtil;
import com.engine.parent.query.util.QueryResultUtil;
import com.wbi.util.Util;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import weaver.conn.RecordSet;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/11-13:16
 * @description : BP_Add接口
 */
public class BPAddCmd extends AbstractCommonCommand<Map<String, Object>> {


    //共有变量，注意线程安全问题
    //主接口地址
    private volatile String apiAddress;
    //接口相关错误信息
    private volatile String erroMsg;

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    /**
     * 构造方法
     *
     * @param params
     * @param user
     */
    public BPAddCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        this.apiAddress = "";
        this.erroMsg = "";
    }

    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> itemData = null;
        //请求id
        String requestId = Util.null2String(params.get("requestid"));
        //先检查该接口是否已经走过，走过的话直接跳过
        if (ApiLogUtil.checkApiHasRun(requestId, ApiMethodConstant.BP_Add)) {
            result.put("status", ApiResultConstant.SUCCESS);
            return result;
        }
        //查询参数
        Map<String, Object> mapParams = this.queryDataForApiParams(requestId);
        if (mapParams.get("itemData") != null) {
            itemData = (List<Map<String, Object>>) mapParams.get("itemData");
        }
        if (itemData == null) {
            //这里是主表数据一条，如果不存在说明查询出错
            erroMsg = "未查询到参数数据，请检查流程数据！";
        }
        if ("".equals(apiAddress)) {
            //流程提交失败信息内容
            erroMsg = "未获取到接口主地址，请检查分部数据！";
        }
        if (itemData != null && erroMsg.isEmpty()) {
            for (Map<String, Object> map : itemData) {
                sendData(map);
            }
        }
        //该接口不管成功失败都返回成功
        result.put("status", ApiResultConstant.SUCCESS);
        return result;
    }

    /**
     * 发送数据
     *
     * @param mainData
     */
    private void sendData(Map<String, Object> mainData) {
        //文档对象
        Document doc;
        Element rootElemtent, node1, node2, node3, row, eachParam;
        //这是在创建一个根节点
        rootElemtent = DocumentHelper.createElement("BusinessPartner");
        //把根节点变成一个Document 对象方便添加子节点
        doc = DocumentHelper.createDocument(rootElemtent);
        //三个子节点
        node1 = rootElemtent.addElement("BusinessPartners");
        node2 = rootElemtent.addElement("BPAddresses");
        node3 = rootElemtent.addElement("ContactEmployees");

        String rowParams;
        //转换数据
        mainData.put("GroupCode", TransferUtil.transferGroupCode(Util.null2String(mainData.get("GroupCode"))));
        mainData.put("Currency", TransferUtil.transferCurrency(Util.null2String(mainData.get("Currency"))));
        mainData.put("DebitorAccount", TransferUtil.transferDebitorAccount(Util.null2String(mainData.get("DebitorAccount"))));

        int i = 0;
        //添加节点参数
        //BusinessPartners节点行数据
        row = node1.addElement("row");
        rowParams = "CardCode,CardName,CardType,cSupplier,GroupCode," +
                "FederalTaxID,Currency,GTSBankAccountNo,DebitorAccount";
        AddParamsUtil.addRowData(row, rowParams, mainData);

        //BPAddresses节点行数据
        row = node2.addElement("row");
        rowParams = "AddressType";
        AddParamsUtil.addRowData(row, rowParams, mainData);

        eachParam = row.addElement("RowNum");
        eachParam.setText(String.valueOf(i));

        //ContactEmployees节点行数据
        row = node3.addElement("row");
        rowParams = "CardCode,Name,Address,Phone1,Phone2,E_Mail";
        AddParamsUtil.addRowData(row, rowParams, mainData);

        //发送接口
        SoapRequestDto dto = new SoapRequestDto();
        dto.setApiAddress(apiAddress);
        dto.setMethodName(ApiMethodConstant.BP_Add);
        dto.setNameSpace(ApiAddressConstant.DEFAULT_NAMESPACE_URI);
        dto.setParamNode("Request");
        dto.setSendDataStr(doc.asXML());
        dto.setResultNode("BP_AddResult");
        dto.setRequestId(Util.null2String(params.get("requestid")));
        dto.setUserId(user.getUID());
        dto.setModuleId(ApiLogConstant.LOG_MOUDLE_ID);
        SoapResult sr = SoapRequestUtil.sendData(dto);
        if (sr.getStatus() == SoapResultCst.SOAP_RESULT_FAIL) {
            erroMsg = sr.getResultMsg();
        }
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> queryDataForApiParams(String requestId) {
        Map<String, Object> result = new HashMap<>();
        RecordSet rs = new RecordSet();
        //查询明细数据
        String sb = " SELECT  " +
                //我方签约公司
                "  m.wfqyzt AS FB,  " +
                //供应商代码
                "  u.gysdm AS CardCode,  " +
                //供应商全称
                "  u.gysqc  AS CardName,  " +
                //供应商类型
                "  u.gyslx AS GroupCode,  " +
                //主要交易货币
                "  u.zyjyhb AS Currency,  " +
                //统一信用社会代码
                "  u.tyshxydm AS FederalTaxID,  " +
                //银行信息
                "  CONCAT ( u.khyx, ' ', u.yxzh ) AS GTSBankAccountNo,  " +
                //应付账款科目
                "  u.mryfzkkm AS DebitorAccount,  " +
                //业务伙伴类型
                "  'cSupplier' AS CardType,  " +
                //业务联系人
                "  u.ywlxr AS Name,  " +
                //联系电话
                "  u.lxdh AS Phone1,  " +
                //固定电话
                "  u.gddh AS Phone2, " +
                //电子邮箱
                "  u.dzyx AS E_Mail,  " +
                //收件地址
                "  u.lxdz AS Address " +
                "  FROM  " +
                "  formtable_main_9_dt2 d  " +
                "  LEFT JOIN formtable_main_9 m ON ( d.mainid = m.id )  " +
                "  LEFT JOIN uf_gystz u ON ( d.gysmc = u.id )   " +
                "  WHERE  " +
                "  m.requestId = ? " +
                "  AND d.sfwskf = 0  ";
        rs.executeQuery(sb, requestId);
        if (rs.next()) {
            apiAddress = TransferUtil.getAddressByDepartmentCode(Util.null2String(rs.getString("FB")));
            result.put("itemData", QueryResultUtil.getJSONList(rs.getArray(), rs.getData()));
        }
        return result;
    }
}
