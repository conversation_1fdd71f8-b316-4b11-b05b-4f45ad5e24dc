package com.engine.service.finance.impl;

import com.engine.cmd.finance.ContractPayWithInvoice2SapCmd;
import com.engine.cmd.finance.ContractPayWithNoInvoice2SapCmd;
import com.engine.core.impl.Service;
import com.engine.service.finance.ContractPayService;

import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/11-17:45
 * @description : TODO
 */
public class ContractPayServiceImpl extends Service implements ContractPayService {
    @Override
    public Map<String, Object> contractPayWithNoInvoice2Sap(Map<String, Object> params) {
        return commandExecutor.execute(new ContractPayWithNoInvoice2SapCmd(params));
    }

    @Override
    public Map<String, Object> contractPayWithInvoice2Sap(Map<String, Object> params) {
        return commandExecutor.execute(new ContractPayWithInvoice2SapCmd(params));
    }
}
