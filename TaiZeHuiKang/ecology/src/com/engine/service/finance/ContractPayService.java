package com.engine.service.finance;

import weaver.hrm.User;

import java.util.Map;

/**
 * <AUTHOR> <PERSON>
 * @CreateTime : 2021/6/11-17:34
 * @description : TODO
 */
public interface ContractPayService {

    /**
     * 合同付款-无票-传SAP
     *
     * @param params
     * @return
     */
    Map<String, Object> contractPayWithNoInvoice2Sap(Map<String, Object> params);

    /**
     * 合同付款-有票-传SAP
     *
     * @param params
     * @return
     */
    Map<String, Object> contractPayWithInvoice2Sap(Map<String, Object> params);
}
