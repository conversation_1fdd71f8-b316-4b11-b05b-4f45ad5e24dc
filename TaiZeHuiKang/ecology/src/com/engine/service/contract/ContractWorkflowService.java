package com.engine.service.contract;

import weaver.hrm.User;

import java.util.Map;

/**
 * <AUTHOR> <PERSON>
 * @CreateTime : 2021/6/11-13:04
 * @description : TODO
 */
public interface ContractWorkflowService {

    /**
     * 终止主合同
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> cancelMainContract(Map<String, Object> params, User user);

    /**
     * 终止主合同-实际终止
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> closeMainContract(Map<String, Object> params, User user);

    /**
     * 不终止主合同
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> continueMainContract(Map<String, Object> params, User user);


    /**
     * 根据requestid 查询合同类型是否在7个合同类型中
     *
     * @param params
     * @return
     */
    boolean checkContractType(Map<String, Object> params);

}
