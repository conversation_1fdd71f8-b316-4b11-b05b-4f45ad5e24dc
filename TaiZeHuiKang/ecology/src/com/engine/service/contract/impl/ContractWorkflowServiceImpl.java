package com.engine.service.contract.impl;


import com.action.constant.ApiResultConstant;
import com.api.formmode.page.util.Util;
import com.engine.cmd.contract.*;
import com.engine.core.impl.Service;
import com.engine.service.contract.ContractWorkflowService;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/1-11:30
 * @description :
 */
public class ContractWorkflowServiceImpl extends Service implements ContractWorkflowService {
    /**
     * 终止主合同
     *
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> cancelMainContract(Map<String, Object> params, User user) {
        Map<String, Object> result;
        //STEP1 : 校验要终止的合同的合同类型，符合
        result = commandExecutor.execute(new CheckTargetContractTypeCmd(params));
        boolean flag = (boolean) result.get("flag");
        //如果符合合同类型才走Cancel接口
        if(!flag){
            //不符合跳过Cancel接口往下走
            return purchaseOrderAndAddVendor(params);
        }else{
            //STEP2 : 调用采购订单取消接口 Purchase_Order_Cancel
            result = commandExecutor.execute(new PurchaseOrderCancelCmd(params, user));
            //STEP2-成功
            if (ApiResultConstant.SUCCESS.equals(Util.null2String(result.get("status")))) {
                //STEP1-1 执行新增采购订单和新增供应商
                return purchaseOrderAndAddVendor(params);
            }
            //STEP2 - 失败
            //返回前台（如果是业务错误，前台需弹窗提示是否要终止主合同）
            return result;
        }
    }

    /**
     * 终止主合同-实际执行
     *
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> closeMainContract(Map<String, Object> params, User user) {
        Map<String, Object> result;
        //STEP1 : 调用采购订单终止接口 Purchase_Order_Close
        result = commandExecutor.execute(new PurchaseOrderCloseCmd(params, user));
        //STEP1-成功
        if (ApiResultConstant.SUCCESS.equals(Util.null2String(result.get("status")))) {
            //STEP1-1 执行新增采购订单和新增供应商
            return purchaseOrderAndAddVendor(params);
        }
        //STEP1-失败,返回错误信息
        return result;
    }

    /**
     * 不终止主合同
     *
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> continueMainContract(Map<String, Object> params, User user) {
        return purchaseOrderAndAddVendor(params);
    }


    @Override
    public boolean checkContractType(Map<String, Object> params) {
        Map<String, Object> result;
        result = commandExecutor.execute(new CheckContractTypeCmd(params));
        return (boolean) result.get("flag");
    }


    private Map<String, Object> purchaseOrderAndAddVendor(Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();
        //STEP1: 判断合同类型是否符合7大合同类型
        boolean flag = this.checkContractType(params);
        //STEP1 true：
        if (flag) {
            //STEP1 调添加供应商用BP_Add该接口不管成功失败，都不影响流程
            commandExecutor.execute(new BPAddCmd(params, user));
            //STEP1-1 : 调用采购订单接口（新增订单） Purchase_Order 返回信息（根据Purchase_Order的status判断是否成功）
            return commandExecutor.execute(new PurchaseOrderCmd(params, user));
        }
        //STEP1 false：调添加供应商用BP_Add该接口不管成功失败，都不影响流程,返回成功status
        commandExecutor.execute(new BPAddCmd(params, user));
        result.put("status", ApiResultConstant.SUCCESS);
        return result;
    }

}
