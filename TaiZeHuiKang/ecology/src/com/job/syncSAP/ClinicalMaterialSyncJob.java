package com.job.syncSAP;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.module.util.InsertModuleUtil;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.query.util.QueryResultUtil;
import com.wbi.util.Util;
import weaver.conn.RecordSetDataSource;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.List;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/17-14:54
 * @description : 临床物料出库信息（中间表）
 */
public class ClinicalMaterialSyncJob extends BaseCronJob {
    private BaseBean _bb;

    private void initBaseBean() {
        _bb = new BaseBean();
    }

    @Override
    public void execute() {
        initBaseBean();
        _bb.writeLog("ClinicalMaterialSyncJob --- START");
        RecordSetDataSource extRecordSet;
        //读取其他数据源
        //读取三个地区的数据源
        String externalDataSources = "SAP_QD,SAP_SH,SAP_BJ";
        String sql;
        String tableName = "uf_saplcwlcktz";
        String[] insertFields = "fb,wldm,wlmc,pp,ggxh,sl,bs,zt,sapfhdh".split(",");
        String docEntry, lineNum;
        List<Object> listValue;
        JSONObject jo;
        //这里默认系统管理员id
        int Create = SDUtil.getSystemMangerByLoginId();
        int module = 31;
        try {
            for (String dataSource : externalDataSources.split(",")) {
                extRecordSet = new RecordSetDataSource(dataSource);
                extRecordSet.executeSql(getSql());
                JSONArray jsonArray = QueryResultUtil.getJSONArrayList(extRecordSet);
                _bb.writeLog("查询出的数据jsonArray" + jsonArray);
                for (int i = 0; i < jsonArray.size(); i++) {
                    jo = jsonArray.getJSONObject(i);
                    listValue = SyncSAPCommon.getValues(insertFields, jo, dataSource);
                    _bb.writeLog("每次插入数据的listValue：" + listValue);
                    InsertModuleUtil.ModuleInsert(tableName, insertFields, listValue, Create, module, null);
                    docEntry = Util.null2String(jo.getString("DocEntry"));
                    lineNum = Util.null2String(jo.getString("LineNum"));
                    //插入建模成功之后再更新中间表数据为已读取
                    updateExtTableRead(docEntry, lineNum, dataSource);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            _bb.writeLog("Exception:" + e.getMessage());
        }
        _bb.writeLog("ClinicalMaterialSyncJob --- END");
    }

    private String getSql() {
        return "SELECT " +
                " DocEntry, " +
                " LineNum, " +
                " 物料代码 AS wldm, " +
                " 物料名称 AS wlmc, " +
                " 数量 AS sl, " +
                " 品牌 AS pp, " +
                " 规格型号 AS ggxh, " +
                " 0 AS bs, " +
                " 发货单编号 AS sapfhdh, " +
                " '0' AS zt  " +
                "FROM " +
                " T_SAP_IGE1  " +
                "WHERE " +
                " 状态 IS NULL  " +
                " OR 状态 = '' ";
    }

    private void updateExtTableRead(String docEntry, String lineNum, String dataSource) {
        RecordSetDataSource rs = new RecordSetDataSource(dataSource);
        String sql = "update T_SAP_IGE1 set 状态 = 1 where DocEntry = '" + docEntry + "' " +
                " and LineNum = '" + lineNum + "'";
        rs.executeSql(sql);
    }
}
