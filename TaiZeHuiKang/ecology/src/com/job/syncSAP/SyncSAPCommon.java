package com.job.syncSAP;

import com.action.util.TransferUtil;
import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @CreateTime : 2021/6/17-17:32
 * @description : Sync<PERSON>PCommon通用类
 */
public class SyncSAPCommon {
    public static List<Object> getValues(String[] insertFields, JSONObject jo, String dataSource) {
        List<Object> listValue = new ArrayList<>();
        for (String field : insertFields) {
            //字段为分部，根据数据源进行转换
            if ("fb".equals(field)) {
                listValue.add(TransferUtil.transferDataSource2SubDep(dataSource));
            } else {
                listValue.add(jo.get(field));
            }
        }
        return listValue;
    }

}
