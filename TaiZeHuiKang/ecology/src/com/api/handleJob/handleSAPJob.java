package com.api.handleJob;

import com.job.syncSAP.ClinicalMaterialSyncJob;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/18-9:29
 * @description : 手动触发SAP同步的job
 */
@Path("/handleJob/sap/v1")
public class handleSAPJob {
    /**
     * 临床物料出库信息（中间表）
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/doClinicalMaterialSyncJob")
    @Produces(MediaType.TEXT_PLAIN)
    public void doClinicalMaterialSyncJob(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        String className = "";
        ClinicalMaterialSyncJob job = new ClinicalMaterialSyncJob();
        job.execute();
    }
}
