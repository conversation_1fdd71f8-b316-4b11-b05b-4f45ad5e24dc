package com.api.contract;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/11-10:55
 * @description : TODO
 */

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.pojo.ApiResult;
import com.engine.service.contract.ContractWorkflowService;
import com.engine.service.contract.impl.ContractWorkflowServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/11-10:56
 * @description : 审批流程接口
 */
@Path("/workflow/contract/approve/v1")
public class ContractWorkflowApi {

    /**
     * 注入service
     *
     * @param user
     * @return
     */
    private ContractWorkflowService getService(User user) {
        return ServiceUtil.getService(ContractWorkflowServiceImpl.class, user);
    }

    /**
     * 终止主合同
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/cancelMainContract")
    @Produces(MediaType.TEXT_PLAIN)
    public String cancelMainContract(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>();
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).cancelMainContract(params, user);
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 终止主合同-实际终止
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/closeMainContract")
    @Produces(MediaType.TEXT_PLAIN)
    public String closeMainContract(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>();
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).closeMainContract(params, user);
        }
        return JSONObject.toJSONString(result);
    }

    /**
     * 不终止主合同
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/continueMainContract")
    @Produces(MediaType.TEXT_PLAIN)
    public String continueMainContract(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>();
        User user = HrmUserVarify.getUser(request, response);
        if (user != null) {
            Map<String, Object> params = ParamUtil.request2Map(request);
            result = getService(user).continueMainContract(params, user);
        }
        return JSONObject.toJSONString(result);
    }


}
