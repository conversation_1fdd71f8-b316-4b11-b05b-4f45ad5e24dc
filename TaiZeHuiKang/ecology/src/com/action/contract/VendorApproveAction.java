package com.action.contract;

import com.action.constant.ApiAddressConstant;
import com.action.constant.ApiMethodConstant;
import com.action.util.AddParamsUtil;
import com.action.util.TransferUtil;
import com.constant.ApiLogConstant;
import com.engine.parent.http.constrant.SoapResultCst;
import com.engine.parent.http.dto.SoapRequestDto;
import com.engine.parent.http.dto.SoapResult;
import com.engine.parent.http.util.ApiLogUtil;
import com.engine.parent.http.util.SoapRequestUtil;
import com.engine.parent.query.util.QueryResultUtil;
import com.wbi.util.Util;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.request.WorkflowRequestMessage;

import java.util.HashMap;
import java.util.Map;

public class VendorApproveAction extends BaseBean implements Action {

    //共有变量，注意线程安全问题
    //主接口地址
    private volatile String apiAddress = "";
    //接口相关错误信息
    private volatile String erroMsg = "";

    @Override
    public String execute(RequestInfo requestInfo) {
        //请求id
        String requestId = requestInfo.getRequestid();
        RequestManager rm = requestInfo.getRequestManager();
        String checkcolumns;
        apiAddress = "";
        erroMsg = "";
        //先检查该接口是否已经走过，走过的话直接跳过
        if (ApiLogUtil.checkApiHasRun(requestId, ApiMethodConstant.BP_Update)) {
            return Action.SUCCESS;
        }

        //查询参数
        Map<String, Object> mapParams = queryDataForApiParams(requestId);
        if (mapParams.get("mainData") == null) {
            //这里是主表数据一条，如果不存在说明查询出错
            erroMsg = "未查询到参数数据，请检查流程数据！";
        }
        if ("".equals(apiAddress)) {
            //流程提交失败信息内容
            erroMsg = "未获取到接口主地址，请检查分部数据！";
        }
        //判断是否都是空值，所有都是空值的话，不走接口，说明没有变更项
        checkcolumns = "CardName,Currency,FederalTaxID,GTSBankAccountNo," +
                "DebitorAccount,Name,Phone1,Phone2,E_Mail,Address";
        if (checkHasChangedValue(mapParams, checkcolumns.split(","))) {
            //发送接口
            mapParams.put("requestId", requestId);
            mapParams.put("userId", requestInfo.getRequestManager().getCreater());
            sendData(mapParams);
            if (!erroMsg.isEmpty()) {
                //流程提交失败信息编号
                rm.setMessageid(WorkflowRequestMessage.WF_REQUEST_ERROR_CODE_07);
                //流程提交失败信息内容
                rm.setMessagecontent(erroMsg);
                return Action.FAILURE_AND_CONTINUE;
            }
        }
        return Action.SUCCESS;
    }

    /**
     * 发送数据
     *
     * @param mapParams
     */
    @SuppressWarnings("unchecked")
    private void sendData(Map<String, Object> mapParams) {
        Map<String, Object> mainData = (Map<String, Object>) mapParams.get("mainData");
        int userId = Util.getIntValue(Util.null2String(mapParams.get("userId")));
        String checkcolumns;
        //文档对象
        Document doc;
        Element rootElemtent, node1, node3, row, eachParam;
        //这是在创建一个根节点
        rootElemtent = DocumentHelper.createElement("BusinessPartner");
        //把根节点变成一个Document 对象方便添加子节点
        doc = DocumentHelper.createDocument(rootElemtent);
        String rowParams;
        //转换数据
        mainData.put("GroupCode", TransferUtil.transferGroupCode(Util.null2String(mainData.get("GroupCode"))));
        mainData.put("Currency", TransferUtil.transferCurrency(Util.null2String(mainData.get("Currency"))));
        mainData.put("DebitorAccount", TransferUtil.transferDebitorAccount(Util.null2String(mainData.get("DebitorAccount"))));

        //添加节点参数
        //2个子节点
        //判断各个子节点是否有数据
        checkcolumns = "CardName,Currency,FederalTaxID,GTSBankAccountNo,DebitorAccount";
        if (checkHasChangedValue(mapParams, checkcolumns.split(","))) {
            node1 = rootElemtent.addElement("BusinessPartners");
            //BusinessPartners节点行数据
            row = node1.addElement("row");
            rowParams = "CardCode,CardName,FederalTaxID,Currency,GTSBankAccountNo,DebitorAccount";
            AddParamsUtil.addRowData(row, rowParams, mainData);
            eachParam = row.addElement("PriceListNum");
            eachParam.setText("1");
        }

        checkcolumns = "Name,Phone1,Phone2,E_Mail,Address";
        if (checkHasChangedValue(mapParams, checkcolumns.split(","))) {
            node3 = rootElemtent.addElement("ContactEmployees");
            //ContactEmployees节点行数据
            row = node3.addElement("row");
            rowParams = "CardCode";
            AddParamsUtil.addRowData(row, rowParams, mainData);

            //业务联系人字段，如果有变化，则传变化的值，如果没有变化则传原始值
            if (Util.null2String(mainData.get("Name")).isEmpty()) {
                eachParam = row.addElement("Name");
                eachParam.setText(Util.null2String(mainData.get("ywlxr")));
            } else {
                eachParam = row.addElement("Name");
                eachParam.setText(Util.null2String(mainData.get("Name")));
            }
            rowParams = "Address,Phone1,Phone2,E_Mail";
            AddParamsUtil.addRowData(row, rowParams, mainData);
        }


        //发送接口
        SoapRequestDto dto = new SoapRequestDto();
        dto.setApiAddress(apiAddress);
        dto.setMethodName(ApiMethodConstant.BP_Update);
        dto.setNameSpace(ApiAddressConstant.DEFAULT_NAMESPACE_URI);
        dto.setParamNode("Request");
        dto.setSendDataStr(doc.asXML());
        dto.setResultNode("BP_UpdateResult");
        dto.setRequestId(Util.null2String(mapParams.get("requestId")));
        dto.setUserId(userId);
        dto.setModuleId(ApiLogConstant.LOG_MOUDLE_ID);
        SoapResult sr = SoapRequestUtil.sendData(dto);
        if (sr.getStatus() == SoapResultCst.SOAP_RESULT_FAIL) {
            erroMsg = sr.getResultMsg();
        }
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> queryDataForApiParams(String requestId) {
        Map<String, Object> result = new HashMap<>();
        RecordSet rs = new RecordSet();
        String sql = " SELECT " +
                //管理分部，用于判断调用不同端口
                "m.glfb AS FB, " +
                //供应商代码
                "m.gysdm AS CardCode, " +
                //供应商全称
                "m.gysqcjky AS CardName, " +
                //主要交易货币
                "m.zyjyhbjky AS Currency, " +
                //统一信用社会代码
                "m.tyshxydmjky AS FederalTaxID, " +
                //银行信息
                "CONCAT ( m.khyxjky, ' ', m.yxzhjky ) AS GTSBankAccountNo, " +
                //应付账款科目
                "m.yfzkkmjky AS DebitorAccount, " +
                //业务联系人
                "m.ywlxrjky AS Name, " +
                //业务联系人（原始）
                "m.ywlxr AS ywlxr, " +
                //联系电话
                "m.lxdhjky AS Phone1, " +
                //固定电话
                "m.gddhjky AS Phone2, " +
                //电子邮箱
                "m.dzyxjky AS E_Mail, " +
                //收件地址
                "m.sjdzjky AS Address, " +
                //地址类型
                "'bo_BillTo' AS AddressType  " +
                " from " +
                //THERA-供应商信息变更审批表
                "formtable_main_77 m  " +
                "WHERE " +
                "m.requestId = ? ";
        rs.executeQuery(sql, requestId);
        if (rs.next()) {
            apiAddress = TransferUtil.getAddressByDepartmentCode(Util.null2String(rs.getString("FB")));
            result.put("mainData", QueryResultUtil.getJSONList(rs.getArray(), rs.getData()).get(0));
        } else {
            result.put("mainData", null);
        }
        return result;
    }

    @SuppressWarnings("unchecked")
    private boolean checkHasChangedValue(Map<String, Object> mapParams, String[] checkValues) {
        Map<String, Object> mainData = (Map<String, Object>) mapParams.get("mainData");
        for (String str : checkValues) {
            if (!Util.null2String(mainData.get(str)).trim().isEmpty()) {
                return true;
            }
        }
        return false;
    }
}
