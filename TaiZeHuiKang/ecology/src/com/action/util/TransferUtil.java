package com.action.util;

import com.action.constant.ApiAddressConstant;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/4-23:15
 * @description : 转换工具类
 */
public class TransferUtil {
    /**
     * 根据部门code获取接口主地址
     *
     * @param departmentCode
     * @return
     */
    public static String getAddressByDepartmentCode(String departmentCode) {
        String res = "";
        switch (departmentCode) {
            //青岛公司
            case "3":
                res = ApiAddressConstant.URL_QINGDAO;
                break;
            //上海
            case "2":
                res = ApiAddressConstant.URL_SH;
                break;
            //北京
            case "4":
                res = ApiAddressConstant.URL_BJ;
                break;
            default:
                res = "";
        }
        return res;
    }

    /**
     * 转换业务伙伴组GroupCode
     *
     * @param str
     * @return
     */
    public static String transferGroupCode(String str) {
        String res = "";
        switch (str) {
            case "0":
                res = "101";
                break;
            case "1":
                res = "102";
                break;
            case "2":
                res = "103";
                break;
            case "3":
                res = "104";
                break;
            case "4":
                res = "105";
                break;
            case "5":
                res = "106";
                break;
            default:
                res = "";
        }
        return res;
    }

    /**
     * 转换币种Currency
     *
     * @param str
     * @return
     */
    public static String transferCurrency(String str) {
        String res = "";
        switch (str) {
            case "0":
                res = "AUD";
                break;
            case "1":
                res = "CAD";
                break;
            case "2":
                res = "EUR";
                break;
            case "3":
                res = "GBP";
                break;
            case "4":
                res = "HKD";
                break;
            case "5":
                res = "JPY";
                break;
            case "6":
                res = "RMB";
                break;
            case "7":
                res = "SGD";
                break;
            case "8":
                res = "USD";
                break;
            case "9":
                res = "CNY";
                break;
            default:
                res = "";
        }
        return res;
    }

    public static String transferDebitorAccount(String str) {
        String res = "";
        switch (str) {
            case "0":
                res = "220201";
                break;
            case "1":
                res = "220202";
                break;
            case "2":
                res = "220203";
                break;
            case "3":
                res = "220204";
                break;
            case "4":
                res = "220205";
                break;
            default:
                res = str;
        }
        return res;
    }

    /**
     * 根据数据源转换部门id
     *
     * @param str
     * @return
     */
    public static String transferDataSource2SubDep(String str) {
        String res = "";
        switch (str) {
            case "SAP_QD":
                res = "3";
                break;
            case "SAP_SH":
                res = "2";
                break;
            case "SAP_BJ":
                res = "4";
                break;
            default:
                res = "";
        }
        return res;
    }

}
