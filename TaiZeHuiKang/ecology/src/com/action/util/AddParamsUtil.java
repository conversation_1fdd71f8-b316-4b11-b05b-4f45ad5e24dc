package com.action.util;

import com.wbi.util.Util;
import org.dom4j.Element;

import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/10-15:10
 * @description : 添加参数工具类
 */
public class AddParamsUtil {
    /**
     * 给row添加参数数据
     *
     * @param row
     * @param targetParams
     * @param source
     */
    public static void addRowData(Element row, String targetParams, Map<String, Object> source) {
        String[] targetParamsArray = targetParams.split(",");
        Element eachObj;
        for (String str : targetParamsArray) {
            if (Util.null2String(source.get(str)) != null && !Util.null2String(source.get(str)).isEmpty()) {
                eachObj = row.addElement(str);
                eachObj.setText(Util.null2String(source.get(str)));
            }
        }
    }
}
