package com.action.finance;

import com.action.constant.ApiResultConstant;
import com.engine.common.util.ServiceUtil;
import com.engine.service.finance.ContractPayService;
import com.engine.service.finance.impl.ContractPayServiceImpl;
import com.wbi.util.Util;
import weaver.general.BaseBean;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.request.WorkflowRequestMessage;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/10-17:46
 * @description : 合同付款（无票）action
 */
public class ContractPayAction extends BaseBean implements Action {

    /**
     * 注入service
     *
     * @param user
     * @return
     */
    private ContractPayService getService(User user) {
        return ServiceUtil.getService(ContractPayServiceImpl.class, user);
    }

    @Override
    public String execute(RequestInfo requestInfo) {
        Map<String, Object> result = new HashMap<>();
        //请求id
        String requestId = requestInfo.getRequestid();

        RequestManager rm = requestInfo.getRequestManager();
        User user = rm.getUser();
        Map<String, Object> params = new HashMap<>();
        params.put("requestId", requestId);
        params.put("userId", requestInfo.getRequestManager().getCreater());
        String apiStatus, erroMsg;
        if (user == null) {
            //流程提交失败信息编号
            rm.setMessageid(WorkflowRequestMessage.WF_REQUEST_ERROR_CODE_07);
            //流程提交失败信息内容
            rm.setMessagecontent("未获取到用户信息，请刷新页面并重试！");
            return Action.FAILURE_AND_CONTINUE;
        }
        //获取主表信息
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        String invoiceStatus = "";
        String propertyName, propertyValue;
        //获取发票状态
        for (Property property : propertys) {
            propertyName = property.getName();
            propertyValue = property.getValue();
            if ("fkzx".equals(propertyName)) {
                invoiceStatus = propertyValue;
                break;
            }
        }
        //无票付款
        if ("0".equals(invoiceStatus)) {
            result = getService(user).contractPayWithNoInvoice2Sap(params);
        }
        //有票付款
        if ("1".equals(invoiceStatus)) {
            result = getService(user).contractPayWithInvoice2Sap(params);
        }
        apiStatus = Util.null2String(result.get("status"));
        erroMsg = Util.null2String(result.get("erroMsg"));
        if (!ApiResultConstant.SUCCESS.equals(apiStatus)) {
            //流程提交失败信息编号
            rm.setMessageid(WorkflowRequestMessage.WF_REQUEST_ERROR_CODE_07);
            //流程提交失败信息内容
            rm.setMessagecontent(erroMsg);
            return Action.FAILURE_AND_CONTINUE;
        }
        return Action.SUCCESS;
    }
}