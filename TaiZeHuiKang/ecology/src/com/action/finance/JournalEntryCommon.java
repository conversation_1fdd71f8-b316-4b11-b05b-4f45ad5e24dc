package com.action.finance;

import com.action.constant.ApiAddressConstant;
import com.action.constant.ApiMethodConstant;
import com.action.util.AddParamsUtil;
import com.action.util.TransferUtil;
import com.constant.ApiLogConstant;
import com.engine.parent.http.dto.SoapRequestDto;
import com.engine.parent.http.dto.SoapResult;
import com.engine.parent.http.util.SoapRequestUtil;
import com.wbi.util.Util;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/10-15:20
 * @description : JournalEntry接口
 */
public class JournalEntryCommon {
    /**
     * 发送接口数据
     *
     * @param mapParams
     */
    @SuppressWarnings("unchecked")
    public static SoapResult sendData(Map<String, Object> mapParams, String apiAddress) {
        Map<String, Object> mainData = (Map<String, Object>) mapParams.get("mainData");
        List<Map<String, Object>> itemData = (List<Map<String, Object>>) mapParams.get("itemData");
        String requestId = Util.null2String(mapParams.get("requestId"));
        int userId = Util.getIntValue(Util.null2String(mapParams.get("userId")));
        String needPrimaryFormItems = Util.null2String(mapParams.get("needPrimaryFormItems"));
        //文档对象
        Document doc;
        Element rootElemtent, node1, node2, node3, row, eachParam;
        //这是在创建一个根节点
        rootElemtent = DocumentHelper.createElement("JournalEntryObject");
        //把根节点变成一个Document 对象方便添加子节点
        doc = DocumentHelper.createDocument(rootElemtent);
        //三个子节点
        node1 = rootElemtent.addElement("JournalEntries");
        node2 = rootElemtent.addElement("JournalEntries_Lines");
        node3 = rootElemtent.addElement("PrimaryFormItems");

        String rowParams;
        //转换数据
        if (itemData != null && !itemData.isEmpty()) {
            for (Map<String, Object> map : itemData) {
                map.put("AccountCode", TransferUtil.transferDebitorAccount(Util.null2String(map.get("AccountCode"))));
            }
        }
        //添加主节点JournalEntries参数
        row = node1.addElement("row");
        rowParams = "ReferenceDate,Memo,Reference2,TaxDate,DueDate";
        AddParamsUtil.addRowData(row, rowParams, mainData);
        if (itemData != null && !itemData.isEmpty()) {
            int i = 0;
            //添加子节点参数
            for (Map<String, Object> map : itemData) {
                //JournalEntries_Lines
                row = node2.addElement("row");
                //行号
                eachParam = row.addElement("Line_ID");
                eachParam.setText(String.valueOf(i));
                rowParams = "AccountCode,ShortName,Debit,Credit,CostingCode,ProjectCode";
                AddParamsUtil.addRowData(row, rowParams, map);
                if ("1".equals(needPrimaryFormItems)) {
                    if (!Util.null2String(map.get("CashFlowLineItemID")).isEmpty()) {
                        //PrimaryFormItems
                        row = node3.addElement("row");
                        //行号
                        eachParam = row.addElement("JDTLineId");
                        eachParam.setText(String.valueOf(i));
                        rowParams = "CashFlowLineItemID,PaymentMeans";
                        AddParamsUtil.addRowData(row, rowParams, map);
                    }
                }
                i++;
            }
        }
        //发送接口
        SoapRequestDto dto = new SoapRequestDto();
        dto.setApiAddress(apiAddress);
        dto.setMethodName(ApiMethodConstant.JournalEntry);
        dto.setNameSpace(ApiAddressConstant.DEFAULT_NAMESPACE_URI);
        dto.setParamNode("Request");
        dto.setSendDataStr(doc.asXML());
        dto.setResultNode("JournalEntryResult");
        dto.setRequestId(requestId);
        dto.setUserId(userId);
        dto.setModuleId(ApiLogConstant.LOG_MOUDLE_ID);
        return SoapRequestUtil.sendData(dto);

    }

}
