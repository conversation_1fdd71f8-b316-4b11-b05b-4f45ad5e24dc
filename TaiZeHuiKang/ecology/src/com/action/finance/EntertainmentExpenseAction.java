package com.action.finance;

import com.action.constant.ApiMethodConstant;
import com.action.util.TransferUtil;
import com.engine.parent.http.constrant.SoapResultCst;
import com.engine.parent.http.dto.SoapResult;
import com.engine.parent.http.util.ApiLogUtil;
import com.engine.parent.query.util.QueryResultUtil;
import com.wbi.util.Util;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.request.WorkflowRequestMessage;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @CreateTime : 2021/6/10-17:29
 * @description : 招待费报销action
 */
public class EntertainmentExpenseAction extends BaseBean implements Action {

    //共有变量，注意线程安全问题
    //主接口地址
    private volatile String apiAddress = "";

    @Override
    public String execute(RequestInfo requestInfo) {
        apiAddress = "";
        //请求id
        String requestId = requestInfo.getRequestid();
        //先检查该接口是否已经走过，走过的话直接跳过
        if (ApiLogUtil.checkApiHasRun(requestId, ApiMethodConstant.JournalEntry)) {
            return Action.SUCCESS;
        }
        //查询参数
        Map<String, Object> mapParams = queryDataForApiParams(requestId);
        //接口相关错误信息
        String erroMsg = "";
        if (mapParams.get("mainData") == null) {
            erroMsg = "未查询到参数数据，请检查流程数据！";
        }
        if (apiAddress.isEmpty()) {
            erroMsg = "未获取到接口主地址，请检查分部数据！";
        }
        if (erroMsg.isEmpty()) {
            //发送接口
            mapParams.put("requestId", requestId);
            mapParams.put("userId", requestInfo.getRequestManager().getCreater());
            SoapResult sr = JournalEntryCommon.sendData(mapParams, apiAddress);
            if (sr.getStatus() == SoapResultCst.SOAP_RESULT_FAIL) {
                erroMsg = sr.getResultMsg();
            }
        }
        if (!erroMsg.isEmpty()) {
            RequestManager rm = requestInfo.getRequestManager();
            //流程提交失败信息编号
            rm.setMessageid(WorkflowRequestMessage.WF_REQUEST_ERROR_CODE_07);
            //流程提交失败信息内容
            rm.setMessagecontent(erroMsg);
            return Action.FAILURE_AND_CONTINUE;
        }
        return Action.SUCCESS;
    }


    @SuppressWarnings("unchecked")
    private Map<String, Object> queryDataForApiParams(String requestId) {
        Map<String, Object> result = new HashMap<>();
        RecordSet rs = new RecordSet();
        String sql;
        //主表数据
        sql = "SELECT " +
                //管理分部，用于判断调用不同端口
                "m.fycdfb AS FB, " +
                //摘要
                "m.bt AS Memo," +
                //参考2
                "m.htbh AS Reference2," +
                //过账日期
                "CONVERT ( VARCHAR ( 12 ), CAST(m.sjfksj as date), 112 )  AS ReferenceDate," +
                //到期日
                "CONVERT ( VARCHAR ( 12 ), CAST(m.sjfksj as date), 112 ) AS DueDate," +
                //单据日期
                "CONVERT ( VARCHAR ( 12 ), CAST(m.sjfksj as date), 112 ) AS TaxDate " +
                "FROM" +
                //招待费报销主表
                " formtable_main_34 m" +
                " WHERE " +
                " m.requestId = ? ";
        rs.executeQuery(sql, requestId);
        if (rs.next()) {
            apiAddress = TransferUtil.getAddressByDepartmentCode(Util.null2String(rs.getString("FB")));
            result.put("mainData", QueryResultUtil.getJSONList(rs.getArray(), rs.getData()).get(0));
        } else {
            result.put("mainData", null);
        }
        sql = "SELECT " +
                //科目代码，如果是往来填写业务伙伴代码
                "TBA.ShortName, " +
                //科目代码
                "TBA.AccountCode, " +
                //贷方金额
                "TBA.Credit, " +
                //借方金额
                "TBA.Debit, " +
                //研发项目
                "xm.sapxmdm AS ProjectCode, " +
                //现金流项目
                "xj.sapmrz AS CashFlowLineItemID, " +
                //成本中心 1：部门
                "hd.sapbmdm AS CostingCode  " +
                "FROM " +
                " ( " +
                " SELECT " +
                "  fs.accountcode AS ShortName, " +
                "  fs.accountcode AS AccountCode, " +
                "  d1.sjfkje1 AS Credit, " +
                "  d1.bhsje AS Debit, " +
                "  m.xm AS ProjectCode, " +
                "  m.xjlxm AS CashFlowLineItemID, " +
                "  d1.fycdbm AS CostingCode  " +
                " FROM " +
                "  formtable_main_34_dt1 d1 " +
                "  LEFT JOIN formtable_main_34 m ON ( m.id = d1.mainid ) " +
                "  LEFT JOIN FnaBudgetSubject_0 fs ON ( fs.id = m.yskmjmy )  " +
                " WHERE " +
                "  m.requestId = ? UNION ALL " +
                " SELECT " +
                "  d1.yjsjkm AS ShortName, " +
                "  d1.yjsjkm AS AccountCode, " +
                "  d1.sjfkje1 AS Credit, " +
                "  d1.se AS Debit, " +
                "  '' AS ProjectCode, " +
                "  '' AS CashFlowLineItemID, " +
                "  '' AS CostingCode  " +
                " FROM " +
                "  formtable_main_34_dt1 d1 " +
                "  LEFT JOIN formtable_main_34 m ON ( m.id = d1.mainid )  " +
                " WHERE " +
                "  m.requestId = ? UNION ALL " +
                " SELECT " +
                "  fs.accountcode AS ShortName, " +
                "  fs.accountcode AS AccountCode, " +
                "  d2.sjfkje1 AS Credit, " +
                "  d2.bhsje AS Debit, " +
                "  m.xm AS ProjectCode, " +
                "  m.xjlxm AS CashFlowLineItemID, " +
                "  d2.fycdbm AS CostingCode  " +
                " FROM " +
                "  formtable_main_34_dt2 d2 " +
                "  LEFT JOIN formtable_main_34 m ON ( m.id = d2.mainid ) " +
                "  LEFT JOIN FnaBudgetSubject_0 fs ON ( fs.id = m.yskmjmy )  " +
                " WHERE " +
                "  m.requestId = ? UNION ALL " +
                " SELECT " +
                "  d2.yjsjkm AS ShortName, " +
                "  d2.yjsjkm AS AccountCode, " +
                "  d2.sjfkje1 AS Credit, " +
                "  d2.se AS Debit, " +
                "  '' AS ProjectCode, " +
                "  '' AS CashFlowLineItemID, " +
                "  '' AS CostingCode  " +
                " FROM " +
                "  formtable_main_34_dt2 d2 " +
                "  LEFT JOIN formtable_main_34 m ON ( m.id = d2.mainid )  " +
                " WHERE " +
                "  m.requestId = ? UNION ALL " +
                " SELECT " +
                "  m.ygbh AS ShortName, " +
                "  CAST ( m.yfzkkm AS nvarchar ) AS AccountCode, " +
                "  m.bdhjje AS Credit, " +
                "  m.bdhjje1 AS Debit, " +
                "  m.xm AS ProjectCode, " +
                "  m.xjlxm AS CashFlowLineItemID, " +
                "  m.ystbbm AS CostingCode  " +
                " FROM " +
                "  formtable_main_34 m  " +
                " WHERE " +
                "  m.requestId = ?  " +
                " ) TBA " +
                " LEFT JOIN hrmdepartmentdefined hd ON ( hd.deptid = TBA.CostingCode ) " +
                " LEFT JOIN uf_xmgltz xm ON ( xm.id = TBA.ProjectCode ) " +
                " LEFT JOIN uf_XJLXM xj ON ( xj.id = TBA.CashFlowLineItemID )";
        rs.executeQuery(sql, requestId, requestId, requestId, requestId, requestId);
        if (rs.next()) {
            result.put("itemData", QueryResultUtil.getJSONList(rs.getArray(), rs.getData()));
        } else {
            result.put("itemData", null);
        }
        return result;
    }
}
