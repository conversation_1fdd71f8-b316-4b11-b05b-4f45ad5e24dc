<%@ page import="weaver.workflow.qiyuesuo.bean.QYSResponse" %>
<%@ page import="weaver.workflow.qiyuesuo.QYSInterface" %>
<%@ page import="weaver.workflow.qiyuesuo.QYSServerInterface" %>
<%@ page import="weaver.workflow.qiyuesuo.util.QYSUtil" %>
<%@ page import="weaver.workflow.qiyuesuo.util.QYSPageUtil" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" %> <%@ include file="/systeminfo/init_wev8.jsp" %>

<%
    String contextPath = QYSPageUtil.getContextPath();
    QYSInterface qysInterface = new QYSInterface(user);
    String targetUrl = Util.null2String(request.getParameter("targetUrl"));
    String source = Util.null2String(request.getParameter("source"));
    String authentication = Util.null2String(request.getParameter("authentication"));
    Boolean isAuthentication = authentication.equals("") ? true : authentication.equals("true");
    String pageType = Util.null2String(request.getParameter("type"));
    String serverType = "";
    String integratedLoginId = "";
    List<Map> servers = QYSServerInterface.listServerByType("", user.getLanguage());
    String url = "";
    boolean existDefaultService = false;
    String accessKey = "";
    String accessSecret = "";
    String serverUrl = "";
    for(Map server: servers) {
        if(server == null) {
            continue;
        }
        String isDefaultService = Util.null2String(server.get("isDefaultService"));//是否是默认服务
        if(isDefaultService.equals("1")) {
            existDefaultService = true;
            break;
        }
    }
    for(Map server: servers) {
        if(server == null) {
            continue;
        }
        boolean isDefaultService = "1".equals(server.get("isDefaultService")); // 是否是默认服务
        boolean autoNetWork = "1".equals(Util.null2String(server.get("autoNetwork"))) || "2".equals(Util.null2String(server.get("autoNetwork")));//是否开启内外网自动登录
        String goPage = Util.null2String(server.get("goPage"));
        String intranetGoPage = Util.null2String(server.get("intranetGoPage"));
        String networkSegmentIds = Util.null2String(server.get("networkSegmentIds"));
        String oaIntranetGoPage = Util.null2String(server.get("oaIntranetGoPage"));
        serverUrl = Util.null2String(server.get("serverUrl"));
        accessKey = Util.null2String(server.get("accessKey"));
        accessSecret = Util.null2String(server.get("accessSecret"));
        if(existDefaultService) {
            if(isDefaultService) {
                serverType = Util.null2String(server.get("serverType"));
                if(autoNetWork) {
                    if("1".equals(Util.null2String(server.get("autoNetwork")))){            //网段策略
                        //判断内外网
                        if (!networkSegmentIds.trim().isEmpty() && !QYSUtil.checkIpSeg(Util.null2String(user.getLoginip()), networkSegmentIds)) {//内网
                            url = QYSUtil.removeSign(intranetGoPage, serverType);
                        }else {
                            url = QYSUtil.removeSign(goPage, serverType);
                        }
                    } else if("2".equals(Util.null2String(server.get("autoNetwork")))) {     //固定网址策略
                        String href = Util.null2String(request.getRequestURL().toString());
                        if(!QYSUtil.checkIpFixed(Util.null2String(href), oaIntranetGoPage)){
                            url = QYSUtil.removeSign(intranetGoPage, serverType);
                        } else {
                            url = QYSUtil.removeSign(goPage, serverType);
                        }
                    }

                }else {
                    url = QYSUtil.removeSign(goPage, serverType);
                }
                break;
            }
        }else {
            serverType = Util.null2String(server.get("serverType"));
            integratedLoginId = Util.null2String(server.get("integratedLoginId"));
            if(autoNetWork) {
                if("1".equals(Util.null2String(server.get("autoNetwork")))) {            //网段策略
                    //判断内外网
                    if (!networkSegmentIds.trim().isEmpty() && !QYSUtil.checkIpSeg(Util.null2String(user.getLoginip()), networkSegmentIds)) {//内网
//                    url = serverType.equals("1") ? QYSUtil.removeSign(intranetGoPage, serverType) : intranetGoPage.replace("/contract/sign/", "").replace("/contract/sign", "").replace("/contract/", "").replace("/contract", "");
                        url = QYSUtil.removeSign(intranetGoPage, serverType);
                    }else {
//                    url = serverType.equals("1") ? QYSUtil.removeSign(goPage, serverType) : goPage.replace("/contract/sign/", "").replace("/contract/sign", "").replace("/contract/", "").replace("/contract", "");
                        url = QYSUtil.removeSign(goPage, serverType);
                    }
                } else if("2".equals(Util.null2String(server.get("autoNetwork")))) {     //固定网址策略
                    String href = Util.null2String(request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort());
                    if(!QYSUtil.checkIpFixed(Util.null2String(href), oaIntranetGoPage)){
                        url = QYSUtil.removeSign(intranetGoPage, serverType);
                    } else {
                        url = QYSUtil.removeSign(goPage, serverType);
                    }
                }
            }else {
//                url = serverType.equals("1") ? QYSUtil.removeSign(goPage, serverType) : goPage.replace("/contract/sign/", "").replace("/contract/sign", "").replace("/contract/", "").replace("/contract", "");
                url = QYSUtil.removeSign(goPage, serverType);
            }
            break;
        }
    }
    if(url.endsWith("/")) {
        url = url.substring(0, url.length() - 1);
    }

    if(!targetUrl.startsWith("http") && !targetUrl.equals("")){//targetUrl有值，并且没写ip或者域名的情况下，根据内外网自动加地址
        targetUrl = targetUrl.startsWith("/") ? targetUrl.substring(1) : targetUrl;
        targetUrl = url + "/" + targetUrl;
    }else if(targetUrl.equals("")){
//        if (!pageType.isEmpty()) {
//            targetUrl = targetUrl.startsWith("/") ? targetUrl.substring(1) : targetUrl;
//        }
        //判断服务是什么版本
        QYSResponse versionResponse = new QYSResponse(new QYSServerInterface(user).checkVersion(serverType, serverUrl, accessKey, accessSecret));
        String serverVersion = Util.null2String(versionResponse.getString("serverVersion"));
        int version = 0;
        if(!serverVersion.isEmpty()) {
            serverVersion = serverVersion.replaceAll("\\.", "");
            if(!serverVersion.isEmpty() && serverVersion.length() > 3) {
                serverVersion = serverVersion.substring(0, 3);
            }
            try {
                version = serverVersion.isEmpty() ? 0 : Integer.parseInt(serverVersion);
            } catch (Exception e) {
                System.out.println(e);
            }
        }
        String urlPath = "";
        if (pageType.equals("1")) {//个人中心
            if (serverType.equals("1")) {//私有云
                targetUrl = url + "/" + "usercenter/info?hide_menu=true" + "&systemCode=" + QYSUtil.getMultiSystemCode();
            } else {
                targetUrl = contextPath + "/interface/Entrance.jsp?id=" + integratedLoginId + "&gopage=" + URLEncoder.encode(url+ "?hide_menu=true");
            }
        } else if (pageType.equals("2")) {//待签文件
            targetUrl = serverType.equals("1") ? url + "/" + "contractlist?status=REQUIRED&hide_menu=true" + "&systemCode=" + QYSUtil.getMultiSystemCode() : contextPath + "/interface/Entrance.jsp?id=" + integratedLoginId + "&gopage=" + URLEncoder.encode(url + "/" + "contract/list?status=REQUIRED&hide_menu=true");
        } else if (pageType.equals("3")) {//已签文件
            targetUrl = serverType.equals("1") ? url + "/" + "contractlist?status=COMPLETE&hide_menu=true" + "&systemCode=" + QYSUtil.getMultiSystemCode() : contextPath + "/interface/Entrance.jsp?id=" + integratedLoginId + "&gopage=" + URLEncoder.encode(url + "/" + "contract/list?status=COMPLETE&hide_menu=true");
        } else if (pageType.equals("4")) {//印章管理
            urlPath = version >= 425 ? "/chop/electronic?hide_menu=true" : "/seal-electronic?hide_menu=true";
            targetUrl = serverType.equals("1") ? url + urlPath + "&systemCode=" + QYSUtil.getMultiSystemCode() : contextPath + "/interface/Entrance.jsp?id=" + integratedLoginId + "&gopage=" +  URLEncoder.encode(url + "/company/seal?hide_menu=true");
        } else if (pageType.equals("5")) {//模板管理
            urlPath = version >= 420 ? "/template-organize?hide_menu=true" : "/template-manage?hide_menu=true";
            targetUrl = serverType.equals("1") ? url + urlPath + "&systemCode=" + QYSUtil.getMultiSystemCode() : contextPath + "/interface/Entrance.jsp?id=" + integratedLoginId + "&gopage=" + URLEncoder.encode(url + "/contract/template?hide_menu=true");
        } else if (pageType.equals("6")) {//业务分类管理
            urlPath = version >= 420 ? "/workflow?hide_menu=true" : "/businesstype?hide_menu=true";
            targetUrl = serverType.equals("1") ? url + urlPath + "&systemCode=" + QYSUtil.getMultiSystemCode() : contextPath + "/interface/Entrance.jsp?id=" + integratedLoginId + "&gopage=" + URLEncoder.encode(url + "/contract/businesstype?hide_menu=true");
        } else if (pageType.equals("7")) {//组织与成员
            targetUrl = serverType.equals("1") ? url + "/employee?hide_menu=true" + "&systemCode=" + QYSUtil.getMultiSystemCode() : contextPath + "/interface/Entrance.jsp?id=" + integratedLoginId + "&gopage=" + URLEncoder.encode(url+ "?hide_menu=true");
        } else if(pageType.equals("8")){//文件签章
            //判断e8还是e9跳不通的页面

        } else{
            targetUrl = serverType.equals("1") ? url + "&systemCode=" + QYSUtil.getMultiSystemCode(): contextPath + "/interface/Entrance.jsp?id=" + integratedLoginId + "&gopage=" + url;
        }
    }
    if(serverType.equals("1")) {
        QYSResponse qysResponse = new QYSResponse();
        if (source.isEmpty()) {
            qysResponse = new QYSResponse(new QYSUtil().getNewQYSToken(targetUrl, user.getUID(), isAuthentication));
        } else {
            qysResponse = new QYSResponse(new QYSUtil().getNewQYSToken(targetUrl, user.getUID(), isAuthentication, source));
        }
        if(qysResponse.getCode() == 0) {
            targetUrl = Util.null2String(qysResponse.getString("url"));
        }
    }

%>

<html>
<body>
    <div style="height: 100%; width: 100%; background-color: #f4f4f4" >
        <iframe style="border: none; width:100%; height:100%" src='<%=targetUrl%>'></iframe>
    </div>
</body>
</html>