package com.engine.waigaoqiao2.tpw.cmd;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.sd2.message.util.MessageUtil;
import com.engine.waigaoqiao2.tpw.bean.AccessResult;
import com.engine.waigaoqiao2.tpw.bean.CallbackData;
import com.engine.waigaoqiao2.tpw.bean.CallbackValidateResult;
import com.engine.waigaoqiao2.tpw.util.QiyuesuoCallbackUtils;
import com.wbi.util.Util;
import net.qiyuesuo.v3sdk.SdkClient;
import net.qiyuesuo.v3sdk.model.common.ContractViewurlViewUserRequest;
import net.qiyuesuo.v3sdk.model.contract.request.ContractViewurlRequest;
import net.qiyuesuo.v3sdk.model.contract.response.ContractViewurlResponse;
import net.qiyuesuo.v3sdk.utils.SdkResponse;
import net.qiyuesuo.v3sdk.model.common.DetailContract;
import net.qiyuesuo.v3sdk.model.contract.request.ContractDetailRequest;

import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.hrm.User;

import java.util.*;

public class FileSignCallbackCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final BaseBean bb;
    private final RecordSet rs = new RecordSet();

    public FileSignCallbackCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();

    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        bb.writeLog("params:" + params);
        try {
            if (params != null && !params.isEmpty()) {
                String aesKey = "";
                String token = "";
                String baseServiceUrl = "";
                String accessToken = "";
                String accessSecret = "";
                String messageSourceId = "";
                String signature = (String) params.get("signature"); // 签名字符串
                String timestamp = (String) params.get("timestamp");// 时间戳，精确到毫秒
                String nonce = (String) params.get("nonce");// 随机字符串
                String encrypted = (String) params.get("encrypted");// 加密后回调内容
                CallbackData callbackData = new CallbackData();
                callbackData.setSignature(signature);
                callbackData.setTimestamp(Long.parseLong(timestamp));
                callbackData.setNonce(nonce);
                callbackData.setEncrypted(encrypted);
                rs.executeQuery("select * from uf_wgqjqxx where qy = 0");
                if (rs.next()) {
                    aesKey = rs.getString("aesKey");
                    bb.writeLog("aesKey:" + aesKey);
                    token = rs.getString("token");
                    bb.writeLog("token:" + token);
                    baseServiceUrl = rs.getString("baseServiceUrl");
                    bb.writeLog("baseServiceUrl:" + baseServiceUrl);
                    accessToken = rs.getString("accessToken");
                    bb.writeLog("accessToken:" + accessToken);
                    accessSecret = rs.getString("accessSecret");
                    bb.writeLog("accessSecret:" + accessSecret);
                    messageSourceId = rs.getString("messageSourceId");
                    bb.writeLog("messageSourceId:" + messageSourceId);
                }
                if(StringUtils.isNotBlank(aesKey) && StringUtils.isNotBlank(token)&& StringUtils.isNotBlank(baseServiceUrl)&& StringUtils.isNotBlank(accessToken)&& StringUtils.isNotBlank(accessSecret)){
                    QiyuesuoCallbackUtils callbackUtils = new QiyuesuoCallbackUtils(aesKey, token);
                    CallbackValidateResult validateResult = callbackUtils.callbackValidateResult(callbackData);
                    if (!validateResult.isValid()) {
                        return AccessResult.newErrorMessage(10000, validateResult.getMessage());
                    }
                    bb.writeLog("validateResult======>" + JSONObject.toJSONString(validateResult));
                    bb.writeLog("getPlainText======>" + validateResult.getPlainText());
                    JSONObject jsonObject = JSONObject.parseObject(validateResult.getPlainText());
                    bb.writeLog("解密后的数据======>" + JSONObject.toJSONString(jsonObject));
                    String callbackType = jsonObject.getString("callbackType");
                    String contractId = jsonObject.getString("contractId");
                    String subject = "";
                    String link = "";
                    Set<String> userIdList = new HashSet<>();
                    if(StringUtils.isNotBlank(contractId)){
                        bb.writeLog("contractId=====>:" + contractId);
                        //调用sdk文件浏览页面（仅查看）
                        link = getFileBrowserPage(contractId,baseServiceUrl,accessToken,accessSecret);
                        //调用sdk获取查询文件详情信息事件
                        JSONObject fileInfo = getElectronicContractingDetails(contractId,baseServiceUrl,accessToken,accessSecret);
                        bb.writeLog("fileInfo======>" + JSONObject.toJSONString(fileInfo));
                        subject = fileInfo.getString("subject");
                        rs.executeQuery("select * from hrmresource where (accounttype <> 1 OR accounttype IS NULL) and workcode = ?", fileInfo.getString("creatorNumber"));
                        //该账号为主账号
                        if(rs.next()){
                            bb.writeLog("id======>",rs.getString("id"));
                            userIdList.add(rs.getString("id"));
                        }else {
                            bb.writeLog("消息推送失败，没有接收人");
                        }

                    }
                    bb.writeLog("userIdList:" + JSONObject.toJSONString(userIdList));
                    //文件签署完成
                    if ("CONTRACT_COMPLETE".equals(callbackType)) {
                        String title = "文件签署完成";
                        String context = "您发起的文件--" + subject + "已签署完成，请点击链接查看签署详情。";
                        MessageUtil.sendMsg(Integer.parseInt(messageSourceId),1,userIdList,title,context, link,link);
                    } else if ("CONTRACT_REJECTED".equals(callbackType)) {//文件退回-接收方拒绝签署
                        String title = "文件退回-接收方拒绝签署";
                        String context = "您发起的文件--" + subject + "已被退回-接收方拒绝签署，请点击链接查看签署详情。";
                        MessageUtil.sendMsg(Integer.parseInt(messageSourceId),1,userIdList,title,context, link,link);
                    }
                }
            }
        } catch (Exception e) {
            bb.writeLog(this.getClass().getName() + "---EXCEPTION" + "异常信息为：" + SDUtil.getExceptionDetail(e));
        }
        bb.writeLog(this.getClass().getName() + "---END");
        return AccessResult.newSuccessMessage("回调成功");
    }

    private String getFileBrowserPage(String contractId,String baseServiceUrl,String accessToken,String accessSecret) {
        // 填写实际的开放平台服务地址（不包含接口地址部分）
//        String baseServiceUrl = "";
        // 从 电子签管理后台-集成中心 获取accessToken和accessSecret
//        String accessToken = "";
//        String accessSecret = "";

        // 创建SdkClient, SdkClient创建成功之后可以重复使用，不必为每个接口或请求单独创建。
        bb.writeLog( "getFileBrowserPage---START");
        bb.writeLog( "baseServiceUrl====>"+baseServiceUrl);
        bb.writeLog( "accessToken====>"+accessToken);
        bb.writeLog( "accessSecret====>"+accessSecret);
        SdkClient sdkClient = new SdkClient(baseServiceUrl, accessToken, accessSecret);

//        ContractViewurlPageStyleRequest pageStyle = new ContractViewurlPageStyleRequest();

//        pageStyle.setThemeColor("#ff0000");

        ContractViewurlViewUserRequest viewUser = new ContractViewurlViewUserRequest();

//        viewUser.setUserId(3189510024457412851L);

        viewUser.setUserId(Long.parseLong("1"));

//        viewUser.setContact("***********");
//
//        viewUser.setMobile("***********");
//
//        viewUser.setEmail("<EMAIL>");
//
//        viewUser.setNumber("*************");
//
//        viewUser.setCardNo("258453199901013453");
//
//        viewUser.setOpenUserId("3189510024457412851");
//
//        viewUser.setAccountNo("3189510024457412852");
//
//        viewUser.setName("池小小");

        ContractViewurlRequest request = new ContractViewurlRequest();

        request.setContractId(Long.parseLong(contractId));

//        request.setBizId("CPF072451");
//
//        request.setPageType("DETAIL");
//
//        request.setDocumentId(3189510024457412851L);
//
//        request.setAllowTab("true");
//
//        request.setCanShowSignatureDetail("true");
//
//        request.setCanShowDocumentList("true");
//
//        request.setAllowViewEvidence("true");
//
//        request.setAllowDownload("false");
//
//        request.setAllowPrint("false");
//
//        request.setCanReturn("false");
//
//        request.setCanViewDetail("false");
//
//        request.setCanMoreOperation("true");
//
//        request.setAllowAttachment("true");
//
//        request.setAllowOffline("true");
//
//        request.setVisitNum(10L);
//
//        request.setExpireTime(300L);
//
//        request.setInvalidToPage("https://www.baidu.com");
//
//        request.setLanguage("ZH_CN");
//
//        request.setViewUser(viewUser);

//        request.setPageStyle(pageStyle);


        try  {
            SdkResponse<ContractViewurlResponse> response = sdkClient.service(request, ContractViewurlResponse.class);
            bb.writeLog( "response====>"+JSONObject.toJSONString(response));
            if (response.getCode() == 0) {
                ContractViewurlResponse result = (ContractViewurlResponse)response.getResult();
                // 使用返回的result数据
                if(result!=null){
                    bb.writeLog( "getFileBrowserPage--link---->"+result.getViewUrl());
                    return result.getLocalPageUrl();
                }
            } else {
                // 根据响应码和响应信息，处理异常情况
                bb.writeLog( "异常情况cede====>"+response.getCode());
                bb.writeLog( "异常情况message====>"+response.getMessage());
            }
        }catch (Exception e) {
            // 处理未知异常
            bb.writeLog("处理未知异常:"+SDUtil.getExceptionDetail(e));
        }
        bb.writeLog( "getFileBrowserPage---END");
        return "";
    }

    private JSONObject getElectronicContractingDetails(String contractId,String baseServiceUrl,String accessToken,String accessSecret){
        bb.writeLog( "getElectronicContractingDetails---START");
        bb.writeLog( "baseServiceUrl====>"+baseServiceUrl);
        bb.writeLog( "accessToken====>"+accessToken);
        bb.writeLog( "accessSecret====>"+accessSecret);
        JSONObject jsonObject = new JSONObject();
        // 创建SdkClient, SkClient创建成功之后可以重复使用，不必为每个接口或请求单独创建。
        SdkClient sdkClient = new SdkClient(baseServiceUrl, accessToken, accessSecret);

        ContractDetailRequest request = new ContractDetailRequest();

        request.setContractId(Long.parseLong(contractId));
        try  {

            SdkResponse<DetailContract> response = sdkClient.service(request, DetailContract.class);
            bb.writeLog( "response====>"+JSONObject.toJSONString(response));
            if (response.getCode() == 0) {
                DetailContract result = (DetailContract)response.getResult();
                // 使用返回的result数据
                if(result!=null){
                    bb.writeLog( "getElectronicContractingDetails--creatorNumber--->"+result.getCreatorNumber());
                    jsonObject.put("creatorNumber",result.getCreatorNumber());
                    jsonObject.put("subject",result.getSubject());
                    return jsonObject;
                }
            } else {
                // 根据响应码和响应信息，处理异常情况
                bb.writeLog( "异常情况cede====>"+response.getCode());
                bb.writeLog( "异常情况message====>"+response.getMessage());
            }
        } catch (Exception e) {
            // 处理未知异常
            bb.writeLog("处理未知异常:"+SDUtil.getExceptionDetail(e));
        }
        bb.writeLog( "getElectronicContractingDetails---END");
        return jsonObject;
    }

}
