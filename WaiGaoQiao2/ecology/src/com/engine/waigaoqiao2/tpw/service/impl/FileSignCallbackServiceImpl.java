package com.engine.waigaoqiao2.tpw.service.impl;

import com.engine.core.impl.Service;

import com.engine.waigaoqiao2.tpw.cmd.FileSignCallbackCmd;
import com.engine.waigaoqiao2.tpw.service.FileSignCallbackService;
import weaver.hrm.User;

import java.util.Collections;
import java.util.Map;

public class FileSignCallbackServiceImpl extends Service implements FileSignCallbackService {
    @Override
    public Map<String, Object> signCallback(Map<String, Object> params, User user) {
        return commandExecutor.execute(new FileSignCallbackCmd(params, user));
    }
}
