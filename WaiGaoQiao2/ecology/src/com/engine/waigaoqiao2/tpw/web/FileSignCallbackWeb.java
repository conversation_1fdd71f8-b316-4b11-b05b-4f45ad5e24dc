package com.engine.waigaoqiao2.tpw.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;

import com.engine.waigaoqiao2.tpw.service.FileSignCallbackService;
import com.engine.waigaoqiao2.tpw.service.impl.FileSignCallbackServiceImpl;
import weaver.general.BaseBean;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;

public class FileSignCallbackWeb extends BaseBean {
    private FileSignCallbackService getService(User user) {
        return ServiceUtil.getService(FileSignCallbackServiceImpl.class, user);
    }

    @POST
    @Path("/signCallback")
    @Produces({MediaType.APPLICATION_JSON})
    public String signCallback(@Context HttpServletRequest request, @Context HttpServletResponse response, Map<String, Object> body) {
        Map<String, Object> result;
        //获取当前用户
        User user = new User(1);
        result = getService(user).signCallback(body, user);
        return JSONObject.toJSONString(result);
    }
}