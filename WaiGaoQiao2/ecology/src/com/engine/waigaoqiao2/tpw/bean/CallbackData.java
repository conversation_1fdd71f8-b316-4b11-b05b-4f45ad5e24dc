package com.engine.waigaoqiao2.tpw.bean;

public class CallbackData {
	private static final long serialVersionUID = 1L;
	private String signature;     // 签名字符串
	private Long timestamp;     // 时间戳，精确到毫秒
	private String nonce;         // 随机字符串
	private String encrypted;     // 加密后回调内容
	
	public String getSignature() {
		return signature;
	}
	
	public void setSignature(String signature) {
		this.signature = signature;
	}
	
	public Long getTimestamp() {
		return timestamp;
	}
	
	public void setTimestamp(Long timestamp) {
		this.timestamp = timestamp;
	}
	
	public String getNonce() {
		return nonce;
	}
	
	public void setNonce(String nonce) {
		this.nonce = nonce;
	}
	
	public String getEncrypted() {
		return encrypted;
	}
	
	public void setEncrypted(String encrypted) {
		this.encrypted = encrypted;
	}
}