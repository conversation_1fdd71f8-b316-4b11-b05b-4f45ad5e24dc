package com.engine.waigaoqiao2.tpw.bean;

public class CallbackValidateResult {
	private boolean valid;  // 校验是否通过
	private Exception exception; // 解密出错异常
	private String message; // 校验信息
	private String plainText; // 回调解密后原文
	private CallbackData callbackData; // 原回调对象
	
	public CallbackValidateResult(boolean valid, String message, String plainText, CallbackData callbackData) {
		this.valid = valid;
		this.message = message;
		this.plainText = plainText;
		this.callbackData = callbackData;
	}
	
	public boolean isValid() {
		return valid;
	}
	
	public void setValid(boolean valid) {
		this.valid = valid;
	}
	
	public Exception getException() {
		return exception;
	}
	
	public void setException(Exception exception) {
		this.exception = exception;
	}
	
	public String getMessage() {
		return message;
	}
	
	public void setMessage(String message) {
		this.message = message;
	}
	
	public String getPlainText() {
		return plainText;
	}
	
	public void setPlainText(String plainText) {
		this.plainText = plainText;
	}
	
	public CallbackData getCallbackData() {
		return callbackData;
	}
	
	public void setCallbackData(CallbackData callbackData) {
		this.callbackData = callbackData;
	}
}
