package com.engine.waigaoqiao3.gyl.portal.cmd;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.query.util.QueryUtil;
import com.engine.parent.workflow.util.WfUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.waigaoqiao3.gyl.portal.cmd.bean.FlowTypeConfig;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @FileName FlowTodoCountCmd.java
 * @Description 流程(包括统一待办)、传阅待办数量查询接口
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/8/15
 */
public class FlowTodoCountCmd extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());


    @Override
    public BizLogContext getLogContext() {
        return null;
    }


    public FlowTodoCountCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    /**
     * @param commandContext
     * @return
     */
    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        String error = "";
        log.info("---START---");
        log.info("params:" + params);
        JSONObject data = new JSONObject();
        try {
            String userid = Util.null2String(params.get("userid"));
            if (userid.isEmpty()) {
                error = "缺失人员id参数";
            } else {
                //获取OA的待办数量
                int oaCount = WfUtil.getWorkflowTodoCount(userid);
                data.put("OA", oaCount);
                //获取各类统一待办的数量
                List<FlowTypeConfig> typeConfigs = getFlowTypeConfig();
                if (typeConfigs != null) {
                    Map<String, Integer> mapCount = getOfsCount(typeConfigs);
                    if (!mapCount.isEmpty()) {
                        data.putAll(mapCount);
                    }
                }
                //获取传阅未开封数量
                int chuanyueCount = getNotOpenChuanYueCount();
                data.put("NBCY", chuanyueCount);
            }
        } catch (Exception e) {
            error = "程序异常:" + e.getMessage();
            log.error("程序异常:", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        result.put("data", data);
        if (error.isEmpty()) {
            result.put("code", "SUCCESS");
        } else {
            result.put("code", "ERROR");
            result.put("msg", error);
        }
        log.info("---END---");
        return result;
    }

    private List<FlowTypeConfig> getFlowTypeConfig() {
        String sql = "select * from " + FlowTypeConfig.TABLE_NAME + " order by px ";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            return QueryUtil.getObjList(rs, FlowTypeConfig.class);
        } else {
            log.error("getFlowTypeConfig query error :" + rs.getExceptionMsg());
        }
        return null;
    }

    /**
     * 获取各类统一待办数量
     *
     * @param configs
     * @return
     */
    private Map<String, Integer> getOfsCount(List<FlowTypeConfig> configs) {
        String userid = Util.null2String(params.get("userid"));
        StringBuilder allSysCodes = new StringBuilder();
        String allSysCodesStr = "";
        Map<String, String[]> mapCode = new HashMap<>();
        Map<String, Integer> mapCount = new HashMap<>();
        for (FlowTypeConfig config : configs) {
            String sysCodes = Util.null2String(config.getBh());//统一待办系统标识
            String fzbh = Util.null2String(config.getFzbh()); //分组编号
            //排除掉没有系统标识的，OA和其他也排除掉
            if (!sysCodes.isEmpty() && !"OA".equals(fzbh) && !"OTHERS".equals(fzbh) && !fzbh.isEmpty()) {
                String[] array = sysCodes.split(",");
                mapCode.put(fzbh, array);
                mapCount.put(fzbh, 0);
                for (String s : array) {
                    allSysCodes.append("'").append(s).append("',");
                }
            }
        }
        if (!allSysCodes.toString().isEmpty()) {
            allSysCodesStr = allSysCodes.substring(0, allSysCodes.length() - 1);
        }
        if (!allSysCodesStr.isEmpty()) {
            String sql = "SELECT count(a.syscode) as cnt,a.syscode " +
                    " FROM " +
                    " ofs_todo_data a " +
                    " LEFT JOIN ofs_sysinfo b ON a.syscode = b.syscode " +
                    " LEFT JOIN HrmResource c ON a.creatorid = c.id " +
                    " WHERE 1=1 " +
                    " and  a.syscode in (" + allSysCodesStr + ")" +
                    " AND a.userid = " + userid +
                    " AND a.isremark = 0 " + //isremak 0是待办 2是已办
                    " group by a.syscode ";
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            if (rs.executeQuery(sql)) {
                while (rs.next()) {
                    String syscode = Util.null2String(rs.getString("syscode"));
                    int cnt = rs.getInt("cnt");
                    for (Map.Entry<String, String[]> entry : mapCode.entrySet()) {
                        if (entry.getKey().contains(syscode)) {
                            mapCount.put(entry.getKey(), mapCount.get(entry.getKey()) + cnt);
                        }
                    }
                }
            }
        }
        log.info("mapCount:" + mapCount);
        return mapCount;
    }

    /**
     * 获取内部传阅未开封数量
     *
     * @return
     */
    private int getNotOpenChuanYueCount() {
        String userid = Util.null2String(params.get("userid"));
        String sql = "SELECT " +
                " count( DISTINCT t1.id ) AS cnt  " +
                "FROM " +
                " uf_sws_nbcy t1 " +
                " INNER JOIN uf_sws_nbcy_dt3 d1 ON ( d1.mainid = t1.id )  " +
                "WHERE " +
                " 1 = 1  " +
                " AND ( " +
                "  t1.modedatastatus = 0  " +
                "  AND ( " +
                "   d1.cydx IN ( " +
                "   SELECT " +
                "    a2.id  " +
                "   FROM " +
                "    hrmresource a1, " +
                "    hrmresource a2  " +
                "   WHERE " +
                "    a1.id =   " + userid +
                "    AND a2.STATUS <= 3  " +
                "    AND a1.workcode = a2.workcode  " +
                "   ))  " +
                "  AND ( " +
                "   ',' || t1.cyxx || ',' NOT LIKE '%,7,%'  " +
                "  OR ( d1.zt = 2 OR d1.dqqrry = 1 ))  " +
                "  AND ( d1.hsbs IS NULL OR d1.hsbs = 0 )  " +
                "  AND ( d1.socbs IS NULL OR d1.socbs = 0 OR d1.socbs = 1 )  " +
                "  AND ( t1.scbs IS NULL OR t1.scbs = 0 )  " +
                "  AND ( t1.hsbs IS NULL OR t1.hsbs = 0 )  " +
                " AND ( t1.sfbzfcy IS NULL ))  " +
                " AND d1.mainid || '-' || d1.cydx IN ( " +
                " SELECT " +
                "  t3.mainid || '-' || min( t2.id ) cydx  " +
                " FROM " +
                "  hrmresource t1, " +
                "  hrmresource t2, " +
                "  uf_sws_nbcy_dt3 t3  " +
                " WHERE " +
                "  t1.id =   " + userid +
                "  AND t2.STATUS <= 3  " +
                "  AND t1.workcode = t2.workcode  " +
                "  AND t2.id = t3.cydx  " +
                "  AND ( t3.scbs IS NULL OR t3.scbs = 0 )  " +
                "  AND ( t3.hsbs IS NULL OR t3.hsbs = 0 )  " +
                "  AND ( t3.zt IS NULL OR t3.zt = 0 )  " +
                " GROUP BY " +
                "  t3.mainid  " +
                " )  " +
                " AND ( " +
                " d1.zt IS NULL  " +
                " OR d1.zt = 0) ";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            if (rs.next()) {
                return rs.getInt("cnt");
            }
        } else {
            log.error("getNotOpenChuanYueCount query error :" + rs.getExceptionMsg() + ";sql:" + sql);
        }
        return 0;
    }
}
