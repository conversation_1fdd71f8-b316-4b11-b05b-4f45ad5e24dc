package com.engine.waigaoqiao3.gyl.portal.web;


import com.engine.parent.common.util.ApiUtil;
import weaver.general.BaseBean;
import weaver.general.GCONST;
import weaver.general.Util;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;
import weaver.ofs.manager.utils.OfsTodoDataUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;

/**
 * @FileName FlowDataWeb.java
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/8/15
 */
public class FlowDataWeb {
    public FlowDataWeb() {
    }

    @POST
    @Path("/getOfsUrl")
    @Produces({MediaType.TEXT_PLAIN})
    public String getOfsUrl(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        BaseBean bb = new BaseBean();
        bb.writeLog("getOfsUrl---111");
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        String sysid = Util.null2String(params.get("sysid"));
        String requestid = Util.null2String(params.get("requestid"));
        bb.writeLog("sysid:" + sysid);
        bb.writeLog("requestid:" + requestid);
        OfsTodoDataUtils ofsTodoDataUtils = new OfsTodoDataUtils();
        String tourl = GCONST.getContextPath() + ofsTodoDataUtils.getShowUrl(sysid, requestid, Util.null2String(user.getUID()), "0");
        bb.writeLog("tourl:" + tourl);
        return tourl;
    }
}
