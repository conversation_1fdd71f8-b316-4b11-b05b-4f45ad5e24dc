package com.engine.waigaoqiao3.gyl.portal.web;


import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.parent.common.util.ApiUtil;
import com.engine.waigaoqiao3.gyl.portal.service.FlowCountService;
import com.engine.waigaoqiao3.gyl.portal.service.impl.FlowCountServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;

/**
 * @FileName FlowCountWeb.java
 * @Description 流程等数据数量统计
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/8/15
 */
public class FlowCountWeb {
    private FlowCountService getService(User user) {
        return ServiceUtil.getService(FlowCountServiceImpl.class, user);
    }

    /**
     * 流程(包括统一待办)、传阅待办数量查询接口
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/todo/count")
    @Produces(MediaType.TEXT_PLAIN)
    public String todoCount(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).todoCount(params, user)
        );
    }
}
