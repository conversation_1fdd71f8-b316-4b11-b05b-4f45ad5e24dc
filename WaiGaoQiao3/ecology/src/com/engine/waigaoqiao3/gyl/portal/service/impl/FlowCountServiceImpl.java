package com.engine.waigaoqiao3.gyl.portal.service.impl;

import com.engine.core.impl.Service;
import com.engine.waigaoqiao3.gyl.portal.cmd.FlowTodoCountCmd;
import com.engine.waigaoqiao3.gyl.portal.service.FlowCountService;
import weaver.hrm.User;

import java.util.Map;

public class FlowCountServiceImpl extends Service implements FlowCountService {
    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> todoCount(Map<String, Object> params, User user) {
        return commandExecutor.execute(new FlowTodoCountCmd(params, user));
    }
}
