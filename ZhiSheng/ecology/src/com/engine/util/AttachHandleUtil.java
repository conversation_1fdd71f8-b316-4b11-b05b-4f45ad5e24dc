package com.engine.util;

import com.aspose.words.Shape;
import com.aspose.words.*;
import weaver.general.BaseBean;
import weaver.general.GCONST;
import weaver.general.Util;
import weaver.toolbox.core.io.FileUtil;

import java.awt.*;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * 附件工具类
 */
public class AttachHandleUtil {

    //region word转pdf start
    public static void doc2Pdf(String docFile, String pdfFile, String waterStr) {
        try {
            FileOutputStream osll = new FileOutputStream(pdfFile);
            loadLicense();
            Document docss;
            try {
                docss = new Document(Files.newInputStream(Paths.get(docFile)));
                if (!"".equals(Util.null2String(waterStr))) {
                    insertWatermarkText(docss, waterStr);
                }
                // 全面支持DOC, DOCX, OOXML, RTF，HTML, OpenDocument, PDF, EPUB, XPS, SWF 相互转换
                docss.save(osll, SaveFormat.PDF);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } catch (FileNotFoundException e1) {
            e1.printStackTrace();
        }
    }

    private static void loadLicense() {
        // 返回读取指定资源的输入流
        License license = new License();
        InputStream is = null;
        BaseBean bb = new BaseBean();
        try {
            //String licenseFile = "/Users/<USER>/Documents/WEAVER/otherlib/aspose-words-14.9.0-jdk16-license.xml";
            String licenseFile = GCONST.getRootPath() + File.separator + "WEB-INF" + File.separator + "lib" + File.separator + "aspose-words-14.9.0-jdk16-license.xml";
            bb.writeLog("licenseFile:" + licenseFile);
            is = FileUtil.getInputStream(licenseFile);
            license.setLicense(is);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * @param doc
     * @param watermarkText
     * @throws Exception
     * @throws
     * @Title: insertWatermarkText
     * @Description: PDF生成水印
     * <AUTHOR>
     */
    private static void insertWatermarkText(Document doc, String watermarkText) throws Exception {

        Shape watermark = new Shape(doc, ShapeType.TEXT_PLAIN_TEXT);
        //水印内容
        watermark.getTextPath().setText(watermarkText);
        //水印字体
        watermark.getTextPath().setFontFamily("宋体");
        //水印宽度
        watermark.setWidth(500);
        //水印高度
        watermark.setHeight(100);
        //旋转水印
        watermark.setRotation(-40);
        //水印颜色
        watermark.getFill().setColor(Color.lightGray);
        watermark.setStrokeColor(Color.lightGray);

        watermark.setRelativeHorizontalPosition(RelativeHorizontalPosition.PAGE);
        watermark.setRelativeVerticalPosition(RelativeVerticalPosition.PAGE);
        watermark.setWrapType(WrapType.NONE);
        watermark.setVerticalAlignment(VerticalAlignment.CENTER);
        watermark.setHorizontalAlignment(HorizontalAlignment.CENTER);

        Paragraph watermarkPara = new Paragraph(doc);
        watermarkPara.appendChild(watermark);

        for (Section sect : doc.getSections()) {
            insertWatermarkIntoHeader(watermarkPara, sect, HeaderFooterType.HEADER_PRIMARY);
            insertWatermarkIntoHeader(watermarkPara, sect, HeaderFooterType.HEADER_FIRST);
            insertWatermarkIntoHeader(watermarkPara, sect, HeaderFooterType.HEADER_EVEN);
        }
        System.out.println("Watermark Set");
    }


    private static void insertWatermarkIntoHeader(Paragraph watermarkPara, Section sect, int headerType) throws Exception {
        HeaderFooter header = sect.getHeadersFooters().getByHeaderFooterType(headerType);
        if (header == null) {
            header = new HeaderFooter(sect.getDocument(), headerType);
            sect.getHeadersFooters().add(header);
        }
        header.appendChild(watermarkPara.deepClone(true));
    }
}
