package com.engine.minshuo2.tpw.module.common.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.sd2.db.util.DBUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * @FileName CustomerMatchingCmd
 * @Description 客户匹配
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/8/26
 */
public class CustomerMatchingCmd extends AbstractCommonCommand<Map<String, Object>> {
    // 创建一个对象作为锁
    private static final Object lock = new Object();
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    public CustomerMatchingCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        String error = "";
        log.info("CustomerMatchingCmd---START---");
        log.info("CustomerMatchingCmd params:" + params);
        try {
            String checkedIds = Util.null2String(params.get("checkedIds"));
            //执行
            error = executeMy(checkedIds);
        } catch (Exception e) {
            error = "异常：" + SDUtil.getExceptionDetail(e);
            log.error("异常", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        result.put("error", error);
        result.put("status", error.isEmpty());
        log.info("CustomerMatchingCmd ---END---");
        return result;


    }

    private String executeMy(String checkedIds) {
        String error = "";
        synchronized (lock) {
            try {
                ArrayList<Map<String, String>> maps = new ArrayList<>();
                RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
                String querySql1 = "select a.fb,a.dq,b.id from uf_qyywhf a inner join uf_zhhk b on a.sf = b.szsf and a.cs = b.szcs where b.zt = 0 ";
                log.info("CustomerMatchingCmd -- querySql1:" + querySql1);
                recordSet.executeQuery(querySql1);
                while (recordSet.next()) {
                    HashMap<String, String> map = new HashMap<>();
                    String id = recordSet.getString("id");
                    String szfb = recordSet.getString("fb");
                    String szdq = recordSet.getString("dq");
                    map.put("id", id);
                    map.put("szfb", szfb);
                    map.put("szdq", szdq);
                    maps.add(map);
                }
                if (!maps.isEmpty()) {
                    for (int i = 0; i < maps.size(); i++) {
                        Map<String, String> map = maps.get(i);
                        String id = Util.null2String(map.get("id"));
                        String szfb = Util.null2String(map.get("szfb"));
                        String szdq = Util.null2String(map.get("szdq"));
                        if (StringUtils.isNotBlank(szfb) && StringUtils.isNotBlank(szdq)) {
                            String updateSql1 = "update uf_zhhk set zt = 2,szfb = " + szfb + ",szdq = " + szdq + " where id = " + id;
                            log.info("CustomerMatchingCmd -- updateSql1:" + updateSql1);
                            recordSet.executeUpdate(updateSql1);
                        }
                    }
                }

                ArrayList<Map<String, String>> list = new ArrayList<>();
                String querySql2 = "select a.id as glkh ,b.id from crm_customerinfo a inner join uf_zhhk b on a.name = b.gs where b.zt = 0 or b.zt = 2";
                log.info("CustomerMatchingCmd -- querySql2:" + querySql2);
                recordSet.executeQuery(querySql2);
                while (recordSet.next()) {
                    HashMap<String, String> map = new HashMap<>();
                    String id = recordSet.getString("id");
                    String glkh = recordSet.getString("glkh");
                    map.put("id", id);
                    map.put("glkh", glkh);
                    list.add(map);
                }
                if (!list.isEmpty()) {
                    for (int i = 0; i < list.size(); i++) {
                        Map<String, String> map = list.get(i);
                        String id = Util.null2String(map.get("id"));
                        String glkh = Util.null2String(map.get("glkh"));
                        if (StringUtils.isNotBlank(glkh) && StringUtils.isNotBlank(id)) {
                            String updateSql2 = "update uf_zhhk set zt = 1,glkh = " + glkh + " where id = " + id;
                            recordSet.executeUpdate(updateSql2);
                        }
                    }
                }
            } catch (Exception e) {
                error = "executeMy 异常：" + SDUtil.getExceptionDetail(e);
                log.error("executeMy 异常：", e);
            }
        }
        return error;
    }
}
