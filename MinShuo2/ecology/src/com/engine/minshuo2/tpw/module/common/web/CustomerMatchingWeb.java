package com.engine.minshuo2.tpw.module.common.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.minshuo2.tpw.module.common.service.CustomerMatchingWebService;
import com.engine.minshuo2.tpw.module.common.service.impl.CustomerMatchingWebServiceImpl;
import com.engine.parent.common.util.ApiUtil;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;


public class CustomerMatchingWeb {

    private CustomerMatchingWebService getService(User user) {
        return ServiceUtil.getService(CustomerMatchingWebServiceImpl.class, user);
    }

    /**
     * 客户匹配
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/customerMatching")
    @Produces(MediaType.TEXT_PLAIN)
    public String customerMatching(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).customerMatching(params, user)
        );
    }


}
