package com.engine.minshuo2.tpw.module.common.service.impl;

import com.engine.core.impl.Service;
import com.engine.minshuo2.tpw.module.common.cmd.CustomerMatchingCmd;
import com.engine.minshuo2.tpw.module.common.service.CustomerMatchingWebService;
import weaver.hrm.User;

import java.util.Map;

/**
 * @FileName ReportFollowServiceImpl.java
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/6/13
 */
public class CustomerMatchingWebServiceImpl extends Service implements CustomerMatchingWebService {
    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> customerMatching(Map<String, Object> params, User user) {
        return commandExecutor.execute(new CustomerMatchingCmd(params, user));
    }

}
