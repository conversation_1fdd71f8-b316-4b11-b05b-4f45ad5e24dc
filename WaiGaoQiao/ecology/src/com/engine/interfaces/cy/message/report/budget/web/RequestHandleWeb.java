package com.engine.interfaces.cy.message.report.budget.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.workflow.publicApi.WorkflowRequestOperatePA;
import com.engine.workflow.publicApi.impl.WorkflowRequestOperatePAImpl;
import weaver.conn.RecordSet;
import weaver.general.Util;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

/**
 * @FileName SqlHandleWeb
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/3/20
 */
public class RequestHandleWeb {


    /**
     * 查询sql接口
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/handRequest")
    @Produces(MediaType.TEXT_PLAIN)
    public String handRequest(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        String ids = Util.null2String(request.getParameter("ids"));
        String table = Util.null2String(request.getParameter("table"));
        String col = Util.null2String(request.getParameter("col"));
        String value = Util.null2String(request.getParameter("value"));
        RecordSet recordSet = new RecordSet();
        String sql = "UPDATE " + table + " SET " + col + " = '" + value + "' where id in (" + ids + ")";
        JSONObject jsonObject = new JSONObject();
        boolean flag = recordSet.execute(sql);
        jsonObject.put("code", "SUCCESS");
        jsonObject.put("flag", flag);
        jsonObject.put("dec", recordSet.getExceptionMsg());
        return JSONObject.toJSONString(jsonObject);
    }

    private WorkflowRequestOperatePA getRequestOperatePA() {
        return ServiceUtil.getService(WorkflowRequestOperatePAImpl.class);
    }
}
