package com.engine.interfaces.cy.message;

import com.engine.parent.httputils.ApiResult;
import com.engine.parent.httputils.HttpUtil;
import weaver.general.BaseBean;
import weaver.sms.SmsService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class SendMesssage implements SmsService {


    @Override
    public boolean sendSMS(String s, String s1, String s2) {
        String url = new BaseBean().getPropValue("SendMessage","url");
        String sysId = new BaseBean().getPropValue("SendMessage","sysId");
        String password = new BaseBean().getPropValue("SendMessage","password");
        String businessId = new BaseBean().getPropValue("SendMessage","businessId");
        Map<String, Object> bodymap = new HashMap<>();
        ArrayList<String> phone = new ArrayList<>();
        phone.add(s1);
        bodymap.put("mobiles",phone);
        bodymap.put("msgContent",s2);
        bodymap.put("sysId",sysId);
        bodymap.put("password",password);
        bodymap.put("businessId",businessId);
        ApiResult result = HttpUtil.postData(url, bodymap, (jsonObject, apiResult) -> {
            new BaseBean().writeLog("SendMesssage----"+jsonObject);
            String code = jsonObject.getString("code");
            if("200".equals(code)){
                apiResult.setSuccessFlag(true);
            }
            return apiResult;
        });

        return result.isSuccessFlag();

    }
}
