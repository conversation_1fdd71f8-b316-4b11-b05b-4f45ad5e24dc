CREATE OR REPLACE VIEW view_remind_fjsc AS SELECT
                                   a.modedatastatus,
                                   a.id,
                                   b.id AS mxid,
                                   c.id AS mx2id,
                                   b.id || '-' || c.id AS uuid,
                                   a.cybt,
                                   a.fqr,
                                   a.sfb<PERSON><PERSON><PERSON>,
                                   a.hsbs,
                                   a.cyxx,
                                   a.scbs,
                                   b.fjscr,
                                   b.fjmc,
                                   b.rqyc,
                                   b.sjyc,
                                   c.cydx,
                                   c.dqqrry,
                                   c.zt,
                                   c.hsbs AS hsbsmx,
                                   c.scbs AS scbsmx
                               FROM
                                   uf_sws_nbcy a
                                       LEFT JOIN uf_sws_nbcy_dt1 b ON a.id = b.mainid
                                       LEFT JOIN uf_sws_nbcy_dt3 c ON a.id = c.mainid
                               WHERE
                                   b.fjmc IS NOT NULL
                                 AND b.rqyc IS NOT NULL
                                 AND b.sjyc IS NOT NULL
                                 AND a.modedatastatus != 1
	AND a.sfbzfcy IS NULL
	AND ( ',' || a.cyxx || ',' NOT LIKE '%,7,%' OR c.dqqrry = 1 OR c.zt IS NOT NULL )
	AND a.fqsj > '2020-06-14'
	-- 2024-07-05修改，为了提高消息提醒性能，视图数据限制日期为当天
	and TO_DATE(b.rqyc, 'yyyy-MM-dd') = TRUNC(SYSDATE);