CREATE OR REPLACE VIEW view_remindqr_nbcy AS
SELECT a.id,
       b.id AS mxid,
       a.cybt,
       a.cydxxxtx,
       a.fqr,
       b.cydx,
       b.qrnr,
       b.qrrq1,
       b.qrsj1
FROM uf_sws_nbcy a
         LEFT JOIN uf_sws_nbcy_dt3 b ON a.id = b.mainid
WHERE b.qrnr IS NOT NULL
  AND a.modedatastatus != 1
  AND a.sfbzfcy IS NULL
  AND b.id IS NOT NULL
  AND ',' || a.cyxx || ',' NOT LIKE '%,7,%'
  AND ',' || a.cyxx || ',' NOT LIKE '%,3,%'
  AND b.zt = 2
  AND qrrq1 IS NOT NULL
  AND qrsj1 IS NOT NULL
  AND qrnr != '已阅'
  -- 2024-07-05修改，为了提高消息提醒性能，视图数据限制日期为当天
  and TO_DATE(qrrq1, 'yyyy-MM-dd') = TRUNC(SYSDATE);