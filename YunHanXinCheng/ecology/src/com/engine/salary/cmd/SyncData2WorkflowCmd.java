package com.engine.salary.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.constant.CommonCst;
import com.engine.parent.common.util.SDUtil;
import com.wbi.util.Util;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.hrm.User;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @description : 同步考勤
 */
public class SyncData2WorkflowCmd extends AbstractCommonCommand<Map<String, Object>> {

    public SyncData2WorkflowCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        //请求id，绩效考评等级，请求id对应的formtable
        String requestId, jxkpdj, tableName;
        RecordSet rs;
        RecordSetTrans rst;
        boolean rsFlag;
        Map<String, Object> result = new HashMap<>(2);
        String selectedRowKeys = Util.null2String(params.get("rowKeys"));
        String updateFormField = Util.null2String(params.get("updateFormField"));
        BigDecimal scaleA = SDUtil.getBigDecimalValue(params.get("scaleA"));
        BigDecimal scaleB = SDUtil.getBigDecimalValue(params.get("scaleB"));
        if ("".equals(selectedRowKeys)) {
            result.put("status", -1);
            result.put("msg", "未获取到行数据，请检查勾选情况！");
            return result;
        }
        String[] rowArray = selectedRowKeys.split(CommonCst.COMMA_EN);
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT ");
        sb.append(" a.requestId, ");
        sb.append(" a.bm, ");
        sb.append(" d.tablename  ");
        sb.append(" FROM ");
        sb.append(" uf_jxkh a ");
        sb.append(" LEFT JOIN workflow_requestbase b ON ( a.requestid = b.requestid ) ");
        sb.append(" LEFT JOIN workflow_base c ON b.WORKFLOWID = c.id ");
        sb.append(" LEFT JOIN workflow_bill d ON c.FORMID = d.id  ");
        sb.append(" WHERE ");
        sb.append(" a.requestId IS NOT NULL ");
        sb.append(" and ( ");
        for (int i = 0; i < rowArray.length; i++) {
            sb.append(" ( id = '").append(rowArray[i]).append(" ')");
            if (i < rowArray.length - 1) {
                sb.append(" or ");
            }
        }
        sb.append(" ) ");
        rs = new RecordSet();
        rsFlag = rs.execute(sb.toString());
        if (!rsFlag) {
            result.put("status", -1);
            result.put("msg", rs.getMsg());
            return result;
        }
        // 数据校验
        // 查询A B 等级
        while (rs.next()) {
            
        }
        rst = new RecordSetTrans();
        rst.setAutoCommit(false);
        try {
            while (rs.next()) {

                sb = new StringBuilder();
                rs = new RecordSet();
                requestId = Util.null2String(rs.getString("requestId"));
                jxkpdj = Util.null2String(rs.getString("bm"));
                tableName = Util.null2String(rs.getString("tablename"));
                sb.append(" update ").append(tableName).append(" set ").append(updateFormField).append(" = ? ");
                sb.append(" where requestid = ? ");
                rsFlag = rst.executeUpdate(sb.toString(), jxkpdj, requestId);
                if (!rsFlag) {
                    //出错手动回滚
                    rst.rollback();
                    result.put("status", -1);
                    result.put("msg", "更新流程表单数据时出错：" + rst.getMsg());
                    return result;
                }
            }
            //手动提交事务
            rst.commit();
            result.put("status", 1);
        } catch (Exception e) {
            e.printStackTrace();
            //异常手动回滚
            rst.rollback();
            result.put("status", -1);
            result.put("msg", "更新流程表单数据时出错：" + e.getMessage());
            return result;
        }

        return result;
    }

}
