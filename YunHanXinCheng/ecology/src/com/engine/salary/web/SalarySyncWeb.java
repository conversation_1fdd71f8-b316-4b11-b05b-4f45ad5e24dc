package com.engine.salary.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.salary.service.SalarySyncService;
import com.engine.salary.service.impl.SalarySyncServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * 绩效考核同步
 *
 * <AUTHOR>
 */
public class SalarySyncWeb {
    private SalarySyncService getService(User user) {
        return ServiceUtil.getService(SalarySyncServiceImpl.class, user);
    }

    /**
     * 测试
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/syncData")
    @Produces(MediaType.TEXT_PLAIN)
    public String syncData(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).syncData2workflow(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("msg", "catch exception : " + ex.getMessage());
        }
        return JSONObject.toJSONString(result);
    }
}
