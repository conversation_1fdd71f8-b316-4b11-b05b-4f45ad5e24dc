package com.engine.salary.module;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

import java.util.HashMap;
import java.util.Map;

/**
 * 校验评级
 *
 * <AUTHOR>
 */
public class ModeExpandRate {
    private static final String RATE_A = "A";
    private static final String RATE_B = "B";

    /**
     * 校验评级
     *
     * @param param
     * @return
     */
    public static Map<String, String> checkRate(Map<String, Object> param, String action) {
        Map<String, String> result = new HashMap<>(2);
        BaseBean bb = new BaseBean();
        bb.writeLog("ModeExpandRate ----START");
        //默认返回成功
        result.put("flag", "true");
        try {
            User user = (User) param.get("user");
            //数据id
            int billid = -1;
            //模块id
            int modeid = -1;
            //绩效考核评级
            String currentGrade;

            RequestInfo requestInfo = (RequestInfo) param.get("RequestInfo");
            if (requestInfo != null) {
                billid = Util.getIntValue(requestInfo.getRequestid());
                modeid = Util.getIntValue(requestInfo.getWorkflowid());
                //获取主表信息
                Property[] propertys = requestInfo.getMainTableInfo().getProperty();
                Map<String, String> mainData = new HashMap<>(6);
                for (Property property : propertys) {
                    String str = property.getName();
                    String value = Util.null2String(property.getValue());
                    mainData.put(str, value);
                }
                if (billid > 0 && modeid > 0) {
                    //------请在下面编写业务逻辑代码------
                    String rateScaleA = Util.null2String(bb.getPropValue("ickey_mode", "rate_scale_a"));
                    String rateScaleAb = Util.null2String(bb.getPropValue("ickey_mode", "rate_scale_ab"));
                    String rateGrouplevel1 = Util.null2String(bb.getPropValue("ickey_mode", "rate_grouplevel1"));
                    String rateGrouplevel2 = Util.null2String(bb.getPropValue("ickey_mode", "rate_grouplevel2"));
                    String rateField = Util.null2String(bb.getPropValue("ickey_mode", "rate_field"));
                    String rateTableName = Util.null2String(bb.getPropValue("ickey_mode", "rate_tablename"));

                    currentGrade = transferGrade(mainData.get(rateField));
                    String level1Value = mainData.get(rateGrouplevel1);
                    String level2Value = mainData.get(rateGrouplevel2);
                    //如果是评级和当前相同，则不校验
                    if (!currentGrade.equals(action)) {
                        if (RATE_A.equals(action) || RATE_B.equals(action)) {
                            RecordSet rs = new RecordSet();
                            boolean rsFlag;
                            //评级A和B的要校验
                            String sql = "SELECT " +
                                    " TBA.*, " +
                                    " TBA.CNT_TOTALA - TBA.CNT_EXISTA AS CNT_LEFTA, " +
                                    " TBA.CNT_TOTALAB - TBA.CNT_EXISTA - TBA.CNT_EXISTB AS CNT_LEFTAB  " +
                                    "FROM " +
                                    " ( " +
                                    " SELECT " +
                                    rateGrouplevel1 + " as level1, " +
                                    rateGrouplevel2 + " as level2, " +
                                    "  CEILING( count( id ) * " + rateScaleA + " ) AS CNT_TOTALA, " +
                                    "  CEILING( count( id ) * " + rateScaleAb + " ) AS CNT_TOTALAB, " +
                                    "  count( CASE WHEN " + rateField + " = 0 THEN " + rateField + " ELSE NULL END ) AS CNT_EXISTA, " +
                                    "  count( CASE WHEN " + rateField + " = 1 THEN " + rateField + " ELSE NULL END ) AS CNT_EXISTB  " +
                                    " FROM " + rateTableName +
                                    "  where " + rateGrouplevel1 + " = ?   " +
                                    "  and " + rateGrouplevel2 + " = ?  " +
                                    " GROUP BY " +
                                    rateGrouplevel1 + ", " +
                                    rateGrouplevel2 +
                                    " ) TBA ";
                            bb.writeLog("sql: " + sql);
                            rsFlag = rs.executeQuery(sql, level1Value, level2Value);
                            if (!rsFlag) {
                                result.put("errmsg", "查询评级规则数量出错：" + rs.getMsg());
                                result.put("flag", "false");
                            } else {
                                if (rs.next()) {
                                    int cntLefta = rs.getInt("CNT_LEFTA");
                                    int cntLeftab = rs.getInt("CNT_LEFTAB");
                                    int cntTotala = rs.getInt("CNT_TOTALA");
                                    int cntTotalab = rs.getInt("CNT_TOTALAB");
                                    int cntExista = rs.getInt("CNT_EXISTA");
                                    int cntExistb = rs.getInt("CNT_EXISTB");
                                    bb.writeLog("cntLefta:" + cntLefta);
                                    bb.writeLog("cntLeftab:" + cntLeftab);
                                    bb.writeLog("cntTotala:" + cntTotala);
                                    bb.writeLog("cntTotalab:" + cntTotalab);
                                    bb.writeLog("cntExista:" + cntExista);
                                    bb.writeLog("cntExistb:" + cntExistb);
                                    //评级A
                                    if (RATE_A.equals(action) && cntLefta <= 0) {
                                        result.put("errmsg", "按照当前规则，当前可评A的人数为：" + cntTotala + ",已有:" + cntExista + "人评选");
                                        result.put("flag", "false");
                                    } else if (RATE_B.equals(action)) {
                                        //如果是从A评级换到B评级，那么剩余AB应该加上本次A评级数量，即为1
                                        if (RATE_A.equals(currentGrade)) {
                                            cntLeftab = cntLeftab + 1;
                                        }
                                        if (cntLeftab <= 0) {
                                            //评级B
                                            result.put("errmsg", "按照当前规则，当前可评A或B的人数为：" + cntTotalab + "," +
                                                    "已有:" + cntExista + "人评选A，" + cntExistb + "人评选B");
                                            result.put("flag", "false");
                                        }

                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            result.put("errmsg", "出现异常：" + e.getMessage());
            result.put("flag", "false");
        }
        bb.writeLog("ModeExpandRate----END");
        return result;
    }

    /**
     * 根据登记选项转换ABCD
     *
     * @param grade
     * @return
     */
    private static String transferGrade(String grade) {
        if ("0".equals(grade)) {
            return "A";
        } else if ("1".equals(grade)) {
            return "B";
        } else if ("2".equals(grade)) {
            return "C";
        } else if ("3".equals(grade)) {
            return "D";
        } else {
            return "";
        }
    }

}