package com.engine.salary.module;

import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

import java.util.HashMap;
import java.util.Map;

/**
 * 绩效考核评分更新流程表单
 */
public class ModeExpandWorkflow {

    /**
     * 更新流程表单绩效
     *
     * @param param
     * @param action
     * @return
     */
    public static Map<String, String> updateWorkflowRate(Map<String, Object> param, String action) {
        Map<String, String> result = new HashMap<>(2);
        BaseBean bb = new BaseBean();
        bb.writeLog(" updateWorkflowRate---START");
        //默认返回成功
        result.put("flag", "true");
        try {
            User user = (User) param.get("user");
            //数据id
            int billid;
            //模块id
            int modeid;
            RequestInfo requestInfo = (RequestInfo) param.get("RequestInfo");
            if (requestInfo != null) {
                billid = Util.getIntValue(requestInfo.getRequestid());
                modeid = Util.getIntValue(requestInfo.getWorkflowid());
                //获取主表信息
                Property[] propertys = requestInfo.getMainTableInfo().getProperty();
                Map<String, String> mainData = new HashMap<>(6);
                for (Property property : propertys) {
                    String str = property.getName();
                    String value = Util.null2String(property.getValue());
                    mainData.put(str, value);
                }
                if (billid > 0 && modeid > 0) {
                    //------请在下面编写业务逻辑代码------
                    String requestid = mainData.get("gllc");
                    int gradeInt = Util.getIntValue(bb.getPropValue("ickey_salary_workflow", action));
                    RecordSet rs = new RecordSet();
                    //根据requestid查询对应的formtable表单名
                    String sql = " select " +
                            " a.requestid, " +
                            " c.tablename  " +
                            " from " +
                            " workflow_requestbase a " +
                            " left join workflow_base b on a.workflowid = b.id " +
                            " left join workflow_bill c on c.id = b.formid  " +
                            " where " +
                            " a.requestid = ? ";
                    bb.writeLog("requestid: " + requestid);
                    bb.writeLog("table sql: " + sql);
                    rs.executeQuery(sql, requestid);
                    if (rs.next()) {
                        String tablename = Util.null2String(rs.getString("tablename"));
                        String formtableFieldName = Util.null2String(bb.getPropValue("ickey_salary_workflow", tablename));
                        if (StringUtils.isNotBlank(formtableFieldName)) {
                            sql = "update " + tablename + " set " + formtableFieldName + "=" + gradeInt + " where requestid = ? ";
                            bb.writeLog("update sql: " + sql);
                            rs.executeUpdate(sql, requestid);
                        }
                    }

                }
            }
        } catch (Exception e) {
            result.put("errmsg", "出现异常：" + e.getMessage());
            result.put("flag", "false");
        }
        bb.writeLog("updateWorkflowRate----END");
        return result;
    }
}