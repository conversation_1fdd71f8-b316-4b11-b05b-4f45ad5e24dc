package com.engine.salary.service.impl;

import com.engine.core.impl.Service;
import com.engine.salary.cmd.SyncData2WorkflowCmd;
import com.engine.salary.service.SalarySyncService;
import weaver.hrm.User;

import java.util.Map;

/**
 * <AUTHOR> Mason
 * @description : 同步考勤
 */
public class SalarySyncServiceImpl extends Service implements SalarySyncService {
    @Override
    public Map<String, Object> syncData2workflow(Map<String, Object> params, User user) {
        return commandExecutor.execute(new SyncData2WorkflowCmd(params, user));
    }
}
