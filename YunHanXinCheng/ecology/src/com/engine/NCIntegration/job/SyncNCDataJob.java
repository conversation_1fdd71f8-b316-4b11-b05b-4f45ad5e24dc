package com.engine.NCIntegration.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.module.util.InsertModuleUtil;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.query.util.QueryResultUtil;
import com.wbi.util.Util;
import org.exolab.castor.types.Date;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetDataSource;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 同步NC视图数据
 * 全量同步(先删除后插入)/增量同步(只同步增量标记相同数据)
 */
public class SyncNCDataJob extends BaseCronJob {

    private String specificBulkingData;
    private BaseBean bb;

    private void _initBaseBean() {
        bb = new BaseBean();
    }

    @Override
    public void execute() {
        _initBaseBean();
        try {
            bb.writeLog("SyncNCDataJob----START");
            String ywbs, xrbm, xrsjy, lybm, lysjy, lysjzlbjzd, xrsjzlbjzd;
            String xrzdm, lyzdm;
            int mainId, xrModuleId;
            RecordSetDataSource extRS;
            RecordSet rs;
            RecordSet rsDetail;
            String fromSql;
            JSONArray fromData;
            JSONObject fromEachData;
            //默认系统管理员为创建人
            int Create = SDUtil.getSystemMangerByLoginId();
            String today = TimeUtil.getToday();
            if (specificBulkingData != null && !"".equals(specificBulkingData)) {
                try {
                    //测试转换日期，如果出错代表参数填写错误
                    Date.parseDate(specificBulkingData);
                    today = specificBulkingData;
                } catch (Exception de) {
                    de.printStackTrace();
                    bb.writeLog("参数日期格式错误：" + de.getMessage());
                }
            }

            //读字段对应配置表数据
            rs = new RecordSet();
            rs.executeQuery("select * from uf_filedscontrast ");
            while (rs.next()) {
                ywbs = Util.null2String(rs.getString("ywbs"));
                xrbm = Util.null2String(rs.getString("xrbm"));
                xrsjy = Util.null2String(rs.getString("xrsjy"));
                lybm = Util.null2String(rs.getString("lybm"));
                lysjy = Util.null2String(rs.getString("lysjy"));
                lysjzlbjzd = Util.null2String(rs.getString("lysjzlbjzd"));
                xrsjzlbjzd = Util.null2String(rs.getString("xrsjzlbjzd"));
                xrModuleId = rs.getInt("xrbdmkid");
                mainId = rs.getInt("id");
                //增量标记不为空，表示需要增量更新数据
                if (!"".equals(lysjzlbjzd)) {
                    //只查增量的数据
                    fromSql = "select * from " + lybm + " where " + lysjzlbjzd + "= '" + today + "'";
                } else {
                    //全量查询
                    fromSql = "select * from " + lybm;
                }

                //判断来源数据源是否是外部数据源
                if (!"".equals(lysjy) && !"oa".equalsIgnoreCase(lysjy) && !"local".equalsIgnoreCase(lysjy)) {
                    extRS = new RecordSetDataSource(lysjy);
                    extRS.execute(fromSql);
                    fromData = QueryResultUtil.getJSONArrayList(extRS);
                } else {
                    rs = new RecordSet();
                    rs.executeQuery(fromSql);
                    fromData = QueryResultUtil.getJSONArrayList(rs);
                }
                if (!fromData.isEmpty()) {
                    //现将现有的表数据删除
                    deleteData(xrbm, xrsjzlbjzd, today);
                    //根据mainId 查询对照表明细数据
                    rsDetail = new RecordSet();
                    rsDetail.executeQuery("select * from uf_filedscontrast_dt1 where mainid = ? ", mainId);
                    List<String> insertFields = new ArrayList<>();
                    List<Object> listValue;
                    Map<String, String> mapField = new HashMap<>();
                    while (rsDetail.next()) {
                        xrzdm = Util.null2String(rsDetail.getString("xrzdm"));
                        lyzdm = Util.null2String(rsDetail.getString("lyzdm"));
                        insertFields.add(xrzdm);
                        mapField.put(xrzdm, lyzdm);
                    }
                    bb.writeLog("fromData:");
                    bb.writeLog(fromData);

                    //遍历来源数据，将所有数据插入
                    for (int i = 0; i < fromData.size(); i++) {
                        //遍历所有字段名
                        listValue = new ArrayList<>();
                        for (String field : insertFields) {
                            fromEachData = fromData.getJSONObject(i);
                            //员工姓名，需要转换
                            if ("ygxm".equals(field)) {
                                int userId = getUserIdByCode(fromEachData.getString(mapField.get(field)));
                                listValue.add(userId);
                            } else {
                                Object fieldValue = fromEachData.get(mapField.get(field));
                                bb.writeLog("fieldValue:");
                                bb.writeLog(fieldValue);
                                listValue.add(fieldValue);
                            }
                        }
                        if ("".equals(xrsjy) || "local".equalsIgnoreCase(xrsjy) || "oa".equalsIgnoreCase(xrsjy)) {
                            InsertModuleUtil.ModuleInsert(xrbm, insertFields, listValue, Create, xrModuleId, null);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("Exception:" + e.getMessage());
        }
        bb.writeLog("SyncNCDataJob----END");
    }

    /**
     * 根据工号获取人员id
     *
     * @param hrmCode
     * @return
     */
    private int getUserIdByCode(String hrmCode) {
        RecordSet rs = new RecordSet();
        rs.executeQuery("select id from hrmresource where workcode = ?", hrmCode);
        if (rs.next()) {
            return rs.getInt("id");
        } else {
            return -1;
        }
    }

    /**
     * 删除数据
     *
     * @param tableName
     * @param xrsjzlbjzd
     * @param date
     * @return
     */
    private void deleteData(String tableName, String xrsjzlbjzd, String date) {
        RecordSet rs = new RecordSet();
        if (!"".equals(xrsjzlbjzd)) {
            rs.executeUpdate("delete from " + tableName + " where " + xrsjzlbjzd + " = ?", date);
        } else {
            rs.executeUpdate("delete from " + tableName);
        }
    }

    public String getSpecificBulkingData() {
        return specificBulkingData;
    }

    public void setSpecificBulkingData(String specificBulkingData) {
        this.specificBulkingData = specificBulkingData;
    }
}
