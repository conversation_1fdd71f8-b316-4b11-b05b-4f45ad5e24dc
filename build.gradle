plugins {
    id 'java'
}

//allprojects {
//    repositories {
//        mavenCentral()
//        maven { url 'https://maven.aliyun.com/repository/public' }  // 添加阿里云镜像，加快下载速度
//    }
//}

//所有子项目默认配置，可以直接使用
ext {
    jarPrefix = '_SD'
    moduleName = '我的项目名(子项目自行修改)'
    moduleVersion = '1.0'
}

// 所有子项目的的公共配置
subprojects {
    apply plugin: 'java'
    //子项目增加gradle.properties文件，配置skipDefaultDependencies=true，即可实现子项目自行配置依赖
    if (!project.hasProperty('skipDefaultDependencies')) {
        // 定义子项目的公共依赖
        dependencies {
            implementation fileTree(dir: '../../Weaver_Dependence/E9/ResinLib', include: ['*.jar'])
            implementation fileTree(dir: '../../Weaver_Dependence/E9/WeaverLib', include: ['*.jar'])
            implementation files('../../Weaver_Dependence/E9/classbean.jar')
            //这里必须显式的声明Lombok，不然Lombok注解无法识别
            annotationProcessor files('../../Weaver_Dependence/E9/WeaverLib/lombok.jar')
        }
    }

    // 默认的JAR 文件名称配置
    jar {
        archiveFileName = "${jarPrefix}_${moduleName}-${moduleVersion}.jar"
    }
    // 统一设置源代码目录
    sourceSets {
        main {
            java {
                srcDirs = ['ecology/src']
            }
        }
    }

    // 统一的编译选项
    tasks.withType(JavaCompile).configureEach {
        options.encoding = 'UTF-8'
    }

    // 任务：清理 Web 目录下的旧 JAR 文件
    tasks.register('sd_clearjar') {
        group = 'build'
        description = '清理部署目录中的旧 JAR 文件'

        doFirst {
            def libDir = file('ecology/WEB-INF/lib')
            if (libDir.exists()) {
                delete fileTree(libDir).matching {
                    include "${jarPrefix}*.jar"
                }
                println "已清理部署目录中的旧 JAR 文件"
            } else {
                println "部署目录不存在，跳过清理"
            }
        }
    }


    // 部署 JAR 文件到目标目录
    tasks.register('sd_copyjar', Copy) {
        group = 'build'
        description = '部署 JAR 到目标目录'

        from jar.outputs.files
        into 'ecology/WEB-INF/lib'

        doLast {
            println "已部署到 ecology/WEB-INF/lib/"
        }
    }

    // 新增一个只做清理的任务
    tasks.register('0清理') {
        group = 'build'
        description = '执行标准 clean 和 sd_clearjar 任务'
        dependsOn 'clean', 'sd_clearjar'

    }

    // 定义完整的构建任务
    tasks.register('1清理+编译+打包') {
        group = 'build'
        description = '完整构建流程：清理编译build文件 → 清理_SD的jar包 → 标准build编译 → 打包jar到WEB-INF/lib下'

        dependsOn 'clean', 'sd_clearjar', 'build', 'sd_copyjar'

        doLast {
            println "清理并构建 完整流程执行完毕"
        }
    }

    // 确保任务执行顺序
    tasks.named('sd_clearjar') {
        mustRunAfter 'clean'
    }
    tasks.named('build') {
        mustRunAfter 'sd_clearjar'
    }
    tasks.named('sd_copyjar') {
        mustRunAfter 'build'
    }
}
