package com.engine.huajia.tpw.action;

import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.sd2.db.util.DBUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import weaver.wechat.util.Utils;

import java.util.Map;

/**
 * @FileName TestAction2
 * @Description 销售订单销售订单变更流程退回:无效化本次流程中所选宴会厅且修改原订单的宴会厅状态
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/7/3
 */
@Setter
@Getter
public class BanquetHallChangeRejectAction extends BaseBean implements Action {
    private static final String YHTDQ_TABLE = "uf_yhtdqxx";
    //使用二开log类
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    private ActionInfo actionInfo;
    private String actionError;
    //宴会厅1字段
    private String yht1FieldName;
    //宴会厅2字段
    private String yht2FieldName;
    //宴会厅3字段
    private String yht3FieldName;

    @Override
    public String execute(RequestInfo requestInfo) {
        try {
            log.info(this.getClass().getName() + "--Start");
            actionInfo = ActionUtil.getInfo(requestInfo);
            actionError = "";
            _checkParams();
            if (StringUtils.isNotBlank(actionError)) {
                return ActionUtil.handleResult(actionError, requestInfo);
            }
            executeMy();
        } catch (Exception e) {
            actionError = "BanquetHallChangeRejectAction 程序异常!"+SDUtil.getExceptionDetail(e);
            log.info(actionError);
        }
        log.info(this.getClass().getName() + "--end");
        return ActionUtil.handleResult(actionError, requestInfo);
    }

    private void executeMy() {
        try {
            //获取流程主表信息
            Map<String, String> mainData = actionInfo.getMainData();
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            //婚礼举办日期
            String hljbrq = Utils.null2String(mainData.get("hljbrq"));
            //场次
            String cc = Utils.null2String(mainData.get("cc"));
            //宴会厅占用状态
            String yhtzyzt = Utils.null2String(mainData.get("yhtzyzt"));
            String[] ballroomKeys = {yht1FieldName, yht2FieldName, yht3FieldName};
            for (String key : ballroomKeys) {
                String ballroom = Utils.null2String(mainData.get(key));
                if (StringUtils.isNotBlank(ballroom)) {// 只处理非空的宴会厅
                    recordSet.executeUpdate("update " + YHTDQ_TABLE + " set zt = 1 where zt = " + yhtzyzt + " and hljbrq = '" + hljbrq + "' and cc = " + cc + " and yht = " + ballroom);
                }
            }
            String yddbh = Utils.null2String(mainData.get("yddbh"));
            if (StringUtils.isNotBlank(yddbh)) {
                recordSet.executeQuery("select * from uf_xsddtz where id = " + yddbh);
                if (recordSet.next()) {
                    String ballroom1 = recordSet.getString(yht1FieldName);
                    String ballroom2 = recordSet.getString(yht2FieldName);
                    String ballroom3 = recordSet.getString(yht3FieldName);
                    hljbrq = recordSet.getString("hljbrq");
                    cc = recordSet.getString("cc");
                    yhtzyzt = recordSet.getString("yhtzyzt");
                    recordSet.executeUpdate("update " + YHTDQ_TABLE + " set zt = " + yhtzyzt + " where  hljbrq = '" + hljbrq + "' and cc = " + cc + " and yht = " + ballroom1);
                    recordSet.executeUpdate("update " + YHTDQ_TABLE + " set zt = " + yhtzyzt + " where  hljbrq = '" + hljbrq + "' and cc = " + cc + " and yht = " + ballroom2);
                    recordSet.executeUpdate("update " + YHTDQ_TABLE + " set zt = " + yhtzyzt + " where  hljbrq = '" + hljbrq + "' and cc = " + cc + " and yht = " + ballroom3);
                }
            }
        } catch (Exception e) {
            actionError = "CheckWhetherOccupiedBanquetHallScheduleAction 程序异常!"+SDUtil.getExceptionDetail(e);
            log.info(actionError);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
    }

    private void _checkParams() {
        StringBuilder stringBuilder = new StringBuilder();
        if (StringUtils.isBlank(yht1FieldName)) {
            stringBuilder.append("yht1FieldName is null ");
        }
        if (StringUtils.isBlank(yht2FieldName)) {
            stringBuilder.append("yht2FieldName is null ");
        }
        if (StringUtils.isBlank(yht3FieldName)) {
            stringBuilder.append("yht3FieldName is null ");
        }
        actionError = stringBuilder.toString();
    }

}
