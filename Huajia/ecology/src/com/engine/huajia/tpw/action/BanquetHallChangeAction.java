package com.engine.huajia.tpw.action;

import com.alibaba.fastjson.JSONObject;
import com.engine.huajia.tpw.action.bean.BanquetHallScheduleInfo;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.sd2.db.util.DBUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import weaver.wechat.util.Utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @FileName TestAction1
 * @Description 销售订单销售订单变更流程:1.校验流程中的宴会厅信息是否被占用。 2.修改原订单的宴会厅状态。 3.新增
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/7/3
 */

@Getter
@Setter
public class BanquetHallChangeAction extends BaseBean implements Action {
    private static final String YHT_TABLE = "uf_yhtxx";
    private static final String YHTDQ_TABLE = "uf_yhtdqxx";
    //使用二开log类
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    private ActionInfo actionInfo;
    private HashMap<String, String> yhtMap;
    private String actionError;
    //宴会厅1字段
    private String yht1FieldName;
    //宴会厅2字段
    private String yht2FieldName;
    //宴会厅3字段
    private String yht3FieldName;
    //婚礼举办日期字段
    private String hljbrqFieldName;
    //场次字段
    private String ccFieldName;
    //宴会类型字段
    private String yhlxFieldName;
    //占用类型字段
    private String zylxFieldName;
    //宴会厅占用状态字段
    private String yhtzyztFieldName;
    @Override
    public String execute(RequestInfo requestInfo) {
        try {
            log.info(this.getClass().getName() + "--Start");
            actionInfo = ActionUtil.getInfo(requestInfo);
            actionError = "";
            _init();
            _checkParams();
            if(StringUtils.isNotBlank(actionError)){
                return ActionUtil.handleResult(actionError, requestInfo);
            }
            _resetParams();
            if(StringUtils.isNotBlank(actionError)){
                return ActionUtil.handleResult(actionError, requestInfo);
            }
            executeMy();
        } catch (Exception e) {
            actionError = "BanquetHallChangeAction 程序异常 "+SDUtil.getExceptionDetail(e);
            log.info(actionError);
        }
        log.info(this.getClass().getName() + "--end");
        return ActionUtil.handleResult(actionError, requestInfo);
    }


    private void _init() {
        yhtMap = null;
    }

    private void _resetParams() {
        try {
            yhtMap = new HashMap<>();
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            recordSet.executeQuery("select * from "+YHT_TABLE);
            while (recordSet.next()) {
                yhtMap.put(recordSet.getString("id"), recordSet.getString("mc"));
            }
            log.info("yhtMap=" + JSONObject.toJSONString(yhtMap));
        } catch (Exception e) {
            actionError = "_resetParams Exception : "+ SDUtil.getExceptionDetail(e);
            log.info("_resetParams Exception : "+SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
    }

    private void _checkParams() {
        StringBuilder stringBuilder = new StringBuilder();
        if(StringUtils.isBlank(yht1FieldName)){
            stringBuilder.append("yht1FieldName is null ");
        }
        if(StringUtils.isBlank(yht2FieldName)){
            stringBuilder.append("yht2FieldName is null ");
        }
        if(StringUtils.isBlank(yht3FieldName)){
            stringBuilder.append("yht3FieldName is null ");
        }
        if(StringUtils.isBlank(hljbrqFieldName)){
            stringBuilder.append("hljbrqFieldName is null ");
        }
        if(StringUtils.isBlank(ccFieldName)){
            stringBuilder.append("ccFieldName is null ");
        }
        if(StringUtils.isBlank(yhlxFieldName)){
            stringBuilder.append("yhlxFieldName is null ");
        }
        if(StringUtils.isBlank(zylxFieldName)){
            stringBuilder.append("zylxFieldName is null ");
        }
        if(StringUtils.isBlank(yhtzyztFieldName)){
            stringBuilder.append("yhtzyztFieldName is null ");
        }
        actionError = stringBuilder.toString();
    }

    private void executeMy() {
        try {
            log.info(this.getClass().getName() + "--executeMy--Start");
            //获取流程主表信息
            Map<String, String> mainData = actionInfo.getMainData();
            //开始获取参数
            String requestId = actionInfo.getRequestId();
            //申请人
            String sqr = Utils.null2String(mainData.get("sqr"));
            //婚礼举办日期
            String hljbrq = Utils.null2String(mainData.get("hljbrq"));
            //场次
            String cc = Utils.null2String(mainData.get("cc"));
            //宴会类型
            String yhlx = Utils.null2String(mainData.get("yhlx"));
            //占用类型
            String zylx = Utils.null2String(mainData.get("zylx"));
            //宴会厅占用状态
            String yhtzyzt = Utils.null2String(mainData.get("yhtzyzt"));

            // 3. 使用循环处理多个宴会厅（避免重复代码）
            List<BanquetHallScheduleInfo> banquetHallScheduleInfos = new ArrayList<>();
            String[] ballroomKeys = {yht1FieldName, yht2FieldName, yht3FieldName};

            for (String key : ballroomKeys) {
                String ballroom = Utils.null2String(mainData.get(key));
                if (StringUtils.isNotBlank(ballroom)) {  // 只处理非空的宴会厅
                    BanquetHallScheduleInfo info = new BanquetHallScheduleInfo();
                    info.setGlxsdd(requestId);
                    info.setGlxs(sqr);
                    info.setYht(ballroom);
                    info.setDqzyrq(hljbrq);
                    info.setCc(cc);
                    info.setYhlx(yhlx);
                    info.setZylx(zylx);
                    info.setZt(yhtzyzt);
                    info.setLcid(requestId);
                    info.setDqzyrq(hljbrq);
                    banquetHallScheduleInfos.add(info);
                }
            }
            //判断三个宴会厅是否已被占档
            checkYhtOccupancyInfo(banquetHallScheduleInfos);
            if (StringUtils.isBlank(actionError)) {
                //像建模表中插入数据
                ModuleResult mr = ModuleDataUtil.insertObjList(banquetHallScheduleInfos, BanquetHallScheduleInfo.TABLE_NAME, ModuleDataUtil.getModuleIdByName(BanquetHallScheduleInfo.TABLE_NAME), Integer.parseInt(sqr));
                log.info("往建模" + BanquetHallScheduleInfo.TABLE_NAME + "插入数据结果" + JSONObject.toJSONString(mr));
                if (!mr.isSuccess()) {
                    actionError = mr.getErroMsg();
                }
            }
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            String yddbh = Utils.null2String(mainData.get("yddbh"));
            if (StringUtils.isNotBlank(yddbh)) {
                recordSet.executeQuery("select * from uf_xsddtz where id = " + yddbh);
                if (recordSet.next()) {
                    String ballroom1 = recordSet.getString(yht1FieldName);
                    String ballroom2 = recordSet.getString(yht2FieldName);
                    String ballroom3 = recordSet.getString(yht3FieldName);
                    hljbrq = recordSet.getString("hljbrq");
                    cc = recordSet.getString("cc");
                    yhtzyzt = recordSet.getString("yhtzyzt");
                    recordSet.executeUpdate("update " + YHTDQ_TABLE + " set zt = 1 where zt = " + yhtzyzt + " and hljbrq = '" + hljbrq + "' and cc = " + cc + " and yht = " + ballroom1);
                    recordSet.executeUpdate("update " + YHTDQ_TABLE + " set zt = 1 where zt = " + yhtzyzt + " and hljbrq = '" + hljbrq + "' and cc = " + cc + " and yht = " + ballroom2);
                    recordSet.executeUpdate("update " + YHTDQ_TABLE + " set zt = 1 where zt = " + yhtzyzt + " and hljbrq = '" + hljbrq + "' and cc = " + cc + " and yht = " + ballroom3);
                }
            }

        } catch (Exception e) {
            actionError = "executeMy Exception"+SDUtil.getExceptionDetail(e);
            log.info("executeMy Exception"+SDUtil.getExceptionDetail(e));
        }
        log.info(this.getClass().getName() + "--executeMy--End");
    }

    private void checkYhtOccupancyInfo(List<BanquetHallScheduleInfo> yhts) {
        try {
            log.info(this.getClass().getName() + "--checkYhtOccupancyInfo--Start");
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            if(yhts.isEmpty()){
                actionError = "流程表单未选择宴会厅,不允许提交!";
                return;
            }
            for (int i = 0; i < yhts.size(); i++) {
                BanquetHallScheduleInfo yhtObj = yhts.get(i);
                String yht = Utils.null2String(yhtObj.getYht());
                String dqzyrq = Utils.null2String(yhtObj.getDqzyrq());
                String cc = Utils.null2String(yhtObj.getCc());
                if (StringUtils.isNotBlank(yht)) {
                    recordSet.executeQuery("select * from "+YHTDQ_TABLE+" where zt = 0 and yht = " + yht + " and dqzyrq = '" + dqzyrq+"' and cc = " + cc);
                    if (recordSet.next()) {
                        actionError = yhtMap.get(yht) + "宴会厅已经被占用";
                    }
                }
            }
        } catch (Exception e) {
            log.info(this.getClass().getName() + "--checkYhtOccupancyInfo--Exception:" + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        log.info(this.getClass().getName() + "--checkYhtOccupancyInfo--End");
    }
}
