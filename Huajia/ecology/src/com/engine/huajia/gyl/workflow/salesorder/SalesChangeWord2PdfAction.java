package com.engine.huajia.gyl.workflow.salesorder;

import com.engine.grtool.doc.util.WordsUtil;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.workflow.BaseSDAction;
import com.engine.sd2.db.util.DBUtil;
import lombok.Getter;
import lombok.Setter;
import weaver.conn.RecordSet;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.Map;

/**
 * @FileName SalesChangeWord2PdfAction.java
 * @Description 销售订单变更-word转pdf
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/9/4
 */
@Getter
@Setter
public class SalesChangeWord2PdfAction extends BaseSDAction implements Action {
    //Action参数---START---
    /**
     * 主表字段名-word文件
     */
    private String field_word;
    /**
     * 主表字段名-转换后带水印的pdf
     */
    private String field_pdf_withwater;
    /**
     * 主表字段名-转换后不带水印的pdf
     */
    private String field_pdf_withoutwater;
    //Action参数---END---


    //使用二开log类
    private final Logger log = LoggerFactory.getLogger(this.getClass());


    /**
     * action执行入口
     *
     * @param requestInfo
     * @return
     */
    @Override
    public String execute(RequestInfo requestInfo) {
        //SDAction初始化
        initAction(requestInfo, this.getClass(), null, "销售订单变更-word文件转pdf带水印");
        try {
            //执行业务逻辑
            execuetMy();
        } catch (Exception e) {
            getThreadLocalBaseParam().actionError = "执行异常：" + e.getMessage();
            log.error("执行异常：", e);
        } finally {
            log.info(this.getClass().getName() + "---END---requestid:" + requestInfo.getRequestid());
        }
        //action返回一直成功
        return actionReturnAlwaysSuccess();
    }

    private void execuetMy() {
        try {
            //主表数据
            Map<String, String> mainData = getThreadLocalBaseParam().actionInfo.getMainData();
            String table = getThreadLocalBaseParam().actionInfo.getFormtableName();
            String waterStr = Util.null2String(getThreadLocalBaseParam().user.getLastname()) + "-" + TimeUtil.getOnlyCurrentTimeString();
            String wordDocid = Util.null2String(mainData.get(field_word));
            if (wordDocid.isEmpty()) {
                appendLog("word文件docid为空，不执行转换");
                return;
            }
            int waterDocId = WordsUtil.word2Pdf(wordDocid, getThreadLocalBaseParam().user, waterStr);
            int noWaterDocId = WordsUtil.word2Pdf(wordDocid, getThreadLocalBaseParam().user);

            if (waterDocId < 0 || noWaterDocId < 0) {
                getThreadLocalBaseParam().actionError = "未能正常生成新的pdf文件";
                return;
            }
            appendLog("生成带水印docid:" + waterDocId);
            appendLog("生成不带水印docid:" + noWaterDocId);

            String sql = "update " + table + " set " + field_pdf_withwater + " = ?," + field_pdf_withoutwater + " = ? where id = ?";
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            if (rs.executeUpdate(sql, waterDocId, noWaterDocId, mainData.get("id"))) {
                appendLog("回写pdf字段成功！");
            } else {
                getThreadLocalBaseParam().actionError = "回写pdf字段失败:" + rs.getExceptionMsg();
            }

        } catch (Exception e) {
            getThreadLocalBaseParam().actionError = this.getClass().getName() + "异常：" + SDUtil.getExceptionDetail(e);
        }
    }


}
