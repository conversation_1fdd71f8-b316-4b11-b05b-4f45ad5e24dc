package com.engine.zhongshi4.gyl.mod.zaojia.core;

import com.engine.parent.common.ModActionCoreBase;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.sd2.db.util.DBUtil;
import com.engine.zhongshi4.gyl.mod.zaojia.bean.ZaojiaOrderData;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.Map;

/**
 * @FileName ZaoJIaQuxiaoZhidingCore.java
 * @Description 造价查询视图-取消置顶
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/8/18
 */
public class ZaoJIaQuxiaoZhidingCore extends ModActionCoreBase {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    public ZaoJIaQuxiaoZhidingCore(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    /**
     * 主执行器
     */
    public Map<String, Object> execute() {
        try {
            super.initBase(this.getClass().getSimpleName(), this.getClass().getName(), "造价查询-取消置顶");
            // 参数验证
            if (user == null || user.getUID() <= 0) {
                error = "用户信息无效";
                return handleResult();
            }

            String checkedIds = Util.null2String(params.get("checkedIds"));
            if (checkedIds.isEmpty()) {
                error = "缺失参数checkedIds";
                return handleResult();
            }

            // 验证checkedIds格式
            if (!isValidCheckedIds(checkedIds)) {
                error = "参数checkedIds格式错误，应为逗号分隔的数字ID";
                return handleResult();
            }

            appendLog("开始处理取消置顶操作，用户ID：" + user.getUID() + "，勾选数据ID：" + checkedIds);

            // 执行取消置顶操作
            processCancelTopOperation(checkedIds);
            if (!error.isEmpty()) {
                return handleResult();
            }

            appendLog("取消置顶操作完成");

        } catch (Exception e) {
            error = "处理异常：" + e.getMessage();
            log.error("处理异常", e);
        }
        return handleResult();
    }

    /**
     * 处理取消置顶操作
     * 删除勾选数据对应的置顶记录
     *
     * @param checkedIds 勾选的数据ID（逗号分隔）
     */
    private void processCancelTopOperation(String checkedIds) {
        try {
            RecordSet rs = DBUtil.getThreadLocalRecordSet();

            // 直接使用IN语句一次性删除所有勾选数据的置顶记录
            String deleteSql = "DELETE FROM " + ZaojiaOrderData.TABLE_NAME + " WHERE zjsjid IN (" + checkedIds + ") AND ry = ?";
            appendLog("取消置顶sql:" + deleteSql);
            if (rs.executeUpdate(deleteSql, user.getUID())) {
                appendLog("成功取消置顶，勾选数据ID：" + checkedIds);
            } else {
                error = "取消置顶失败，错误信息：" + rs.getExceptionMsg();
                log.error("取消置顶失败，错误信息：" + rs.getExceptionMsg());
            }

        } catch (Exception e) {
            error = "处理取消置顶操作异常：" + e.getMessage();
            log.error("处理取消置顶操作异常", e);
        }
    }

    /**
     * 验证checkedIds参数格式
     *
     * @param checkedIds 勾选的数据ID
     * @return 格式是否有效
     */
    private boolean isValidCheckedIds(String checkedIds) {
        if (checkedIds == null || checkedIds.trim().isEmpty()) {
            return false;
        }

        String[] idArray = checkedIds.split(",");
        for (String id : idArray) {
            if (!id.trim().isEmpty()) {
                try {
                    int zjsjid = Integer.parseInt(id.trim());
                    if (zjsjid <= 0) {
                        return false;
                    }
                } catch (NumberFormatException e) {
                    return false;
                }
            }
        }
        return true;
    }


}
