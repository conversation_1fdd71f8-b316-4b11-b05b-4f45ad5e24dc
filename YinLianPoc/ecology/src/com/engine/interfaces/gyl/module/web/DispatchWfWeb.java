package com.engine.interfaces.gyl.module.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.interfaces.gyl.module.service.DispatchWfService;
import com.engine.interfaces.gyl.module.service.impl.DispatchServiceImpl;
import weaver.general.BaseBean;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @description : 派单流程
 */
public class DispatchWfWeb {
    private DispatchWfService getService(User user) {
        return ServiceUtil.getService(DispatchServiceImpl.class, user);
    }

    /**
     * 批量派单
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/dispatchWf")
    @Produces(MediaType.TEXT_PLAIN)
    public String dispatchWf(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "dispatchWf接口调用---START");
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).dispatchWf(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("erroMsg", "catch exception : " + ex.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "dispatchWf接口完毕");
        return JSONObject.toJSONString(result);
    }

    /**
     * 批量派单
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/dispatchWfSingle")
    @Produces(MediaType.TEXT_PLAIN)
    public String dispatchWfSingle(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "dispatchWf接口调用---START");
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).dispatchWfSingle(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("erroMsg", "catch exception : " + ex.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "dispatchWf接口完毕");
        return JSONObject.toJSONString(result);
    }

}
