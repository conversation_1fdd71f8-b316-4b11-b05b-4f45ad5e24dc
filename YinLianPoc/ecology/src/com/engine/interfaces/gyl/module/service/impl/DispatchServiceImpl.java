package com.engine.interfaces.gyl.module.service.impl;

import com.engine.core.impl.Service;
import com.engine.interfaces.gyl.module.cmd.DispatchWfCmd;
import com.engine.interfaces.gyl.module.cmd.DispatchWfSingleCmd;
import com.engine.interfaces.gyl.module.service.DispatchWfService;
import weaver.hrm.User;

import java.util.Map;

/**
 * <AUTHOR> Mason
 * @description : 派单流程
 */
public class DispatchServiceImpl extends Service implements DispatchWfService {
    @Override
    public Map<String, Object> dispatchWf(Map<String, Object> params, User user) {
        return commandExecutor.execute(new DispatchWfCmd(params, user));
    }

    @Override
    public Map<String, Object> dispatchWfSingle(Map<String, Object> params, User user) {
        return commandExecutor.execute(new DispatchWfSingleCmd(params, user));
    }
}
