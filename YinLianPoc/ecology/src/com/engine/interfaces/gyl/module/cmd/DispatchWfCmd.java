package com.engine.interfaces.gyl.module.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.workflow.util.WorkFlowUtil;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @description : 一键派单
 */
public class DispatchWfCmd extends AbstractCommonCommand<Map<String, Object>> {

    public DispatchWfCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        BaseBean bb = new BaseBean();
        Map<String, Object> result = new HashMap<>(2);
        boolean rsFlag;
        String erroMsg = "", wfreqId;
        String status;
        boolean checkErro = false;
        RecordSetTrans rst = null;
        bb.writeLog(this.getClass().getName() + "---START");
        List<String> allReqIds = new ArrayList<>();
        //默认成功
        result.put("status", "1");
        try {
            //获取参数
            //建模勾选的id
            String selectedDataIds = Util.null2String(params.get("selectedDataIds"));
            //选中的人力资源
            String selectedValue = Util.null2String(params.get("selectedValue"));
            if (!selectedDataIds.isEmpty() && !selectedValue.isEmpty()) {
                //获取配置信息
                //建模表名
                String moduleTable = Util.null2String(bb.getPropValue("UnionPay_dispatch", "moduleTable"));
                //建模的状态字段
                String status_field = Util.null2String(bb.getPropValue("UnionPay_dispatch", "status_field"));
                //状态正常的值
                String status_valid = Util.null2String(bb.getPropValue("UnionPay_dispatch", "status_valid"));
                //建模的流程请求id字段
                String wfreqId_field = Util.null2String(bb.getPropValue("UnionPay_dispatch", "wfreqId_field"));
                //提交人id
                String submiter = Util.null2String(bb.getPropValue("UnionPay_dispatch", "submiter"));

                RecordSet rs = new RecordSet();
                String sql = "select * from " + moduleTable + " where id in (" + selectedDataIds + ") ";
                rsFlag = rs.executeQuery(sql);
                if (rsFlag) {
                    while (rs.next()) {
                        //校验状态
                        status = Util.null2String(rs.getString(status_field));
                        if (status_valid.equals(status)) {
                            wfreqId = Util.null2String(rs.getString(wfreqId_field));
                            allReqIds.add(wfreqId);
                        } else {
                            //校验状态错误后跳出循环
                            checkErro = true;
                            break;
                        }
                    }
                    if (checkErro) {
                        erroMsg = "存在审核状态错误的数据，请刷新页面重新勾选！";
                    } else {
                        rst = new RecordSetTrans();
                        rst.setAutoCommit(false);
                        //更新流程人力字段
                        updateWfHrm(rst, allReqIds);
                        rst.commit();

                        //将勾选的流程提交
                        for (String str : allReqIds) {
                            //提交流程
                            String re = WorkFlowUtil.submitWorkflowRequest(Integer.parseInt(str),
                                    Integer.parseInt(submiter), "submit");
                            if (!"success".equals(re)) {
                                erroMsg = re;
                                break;
                            }
                        }
                        //将当前建模的状态从未审批改成审批中
                        if (erroMsg.isEmpty()) {
                            updateModuleStatus(moduleTable, status_field, selectedValue);
                        }

                    }
                } else {
                    erroMsg = "查询建模数据失败：" + rs.getMsg() + ";" + rs.getExceptionMsg();
                }
            } else {
                erroMsg = "未收到勾选建模数据的信息或者选择的审核人信息";
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("status", "-1");
            result.put("erroMsg", e.getMessage());
            if (rst != null) {
                rst.rollback();
            }
        }
        if (!erroMsg.isEmpty()) {
            result.put("status", "-1");
            result.put("erroMsg", erroMsg);
        }
        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }

    /**
     * 更新流程上的人力信息
     *
     * @param rst
     * @param allReqIds
     * @throws Exception
     */
    private void updateWfHrm(RecordSetTrans rst, List<String> allReqIds) throws Exception {
        BaseBean bb = new BaseBean();
        //流程上的人员字段
        String wfhrm_field = Util.null2String(bb.getPropValue("UnionPay_dispatch", "wfhrm_field"));
        for (String str : allReqIds) {
            //根据requestid查询对应的流程表
            // WfInfoVo wf = QueryWfInfoUtil.getWfInfoByRequestid(str);
//            rst.executeUpdate("update " + wf.getFormtableName() + " set " + wfhrm_field + "="
//                    + Util.null2String(params.get("selectedValue")) + " where requestid = ?", wf.getRequestid());
        }
    }

    /**
     * 建模表名，状态字段名
     *
     * @param moduleTable
     * @param statusField
     */
    private void updateModuleStatus(String moduleTable, String statusField, String approver) {
        BaseBean bb = new BaseBean();
        RecordSet rs = new RecordSet();
        //状态正常的值
        String status_approved = Util.null2String(bb.getPropValue("UnionPay_dispatch", "status_approved"));
        String approver_field = Util.null2String(bb.getPropValue("UnionPay_dispatch", "approver_field"));
        String receive_date_field = Util.null2String(bb.getPropValue("UnionPay_dispatch", "receive_date_field"));

        String today = TimeUtil.getToday();
        rs.executeUpdate("update " + moduleTable + " set " + statusField + "='" + status_approved + "'," +
                approver_field + "=" + approver + "," +
                receive_date_field + "='" + today + "' " +
                " where id in  ( " + Util.null2String(params.get("selectedDataIds")) + ")");
    }

}
