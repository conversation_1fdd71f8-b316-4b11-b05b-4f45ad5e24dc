package com.engine.interfaces.gyl.module.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.workflow.util.WorkFlowUtil;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Mason
 * @description : 单人派单
 */
public class DispatchWfSingleCmd extends AbstractCommonCommand<Map<String, Object>> {

    public DispatchWfSingleCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        BaseBean bb = new BaseBean();
        Map<String, Object> result = new HashMap<>(2);
        boolean rsFlag;
        String erroMsg = "", wfreqId;
        RecordSetTrans rst = null;
        bb.writeLog(this.getClass().getName() + "---START");
        //默认成功
        result.put("status", "1");
        String dataId;
        List<Map<String, String>> listRandom = new ArrayList<>();
        Map<String, String> map;
        try {
            //获取参数
            //人员id
            String userId = Util.null2String(params.get("userId"));

            if (!userId.isEmpty()) {
                //获取配置信息
                //建模表名
                String moduleTable = Util.null2String(bb.getPropValue("UnionPay_dispatch", "moduleTable"));
                //建模的状态字段
                String status_field = Util.null2String(bb.getPropValue("UnionPay_dispatch", "status_field"));
                //状态正常的值
                String status_valid = Util.null2String(bb.getPropValue("UnionPay_dispatch", "status_valid"));
                //建模的流程请求id字段
                String wfreqId_field = Util.null2String(bb.getPropValue("UnionPay_dispatch", "wfreqId_field"));
                //提交人id
                String submiter = Util.null2String(bb.getPropValue("UnionPay_dispatch", "submiter"));

                RecordSet rs = new RecordSet();
                String sql = "select * from " + moduleTable +
                        " where " + status_field + " = '" + status_valid + "' ";
                rsFlag = rs.executeQuery(sql);
                if (rsFlag) {
                    String randomReqId;
                    while (rs.next()) {
                        map = new HashMap<>(2);
                        dataId = Util.null2String(rs.getString("id"));
                        wfreqId = Util.null2String(rs.getString(wfreqId_field));
                        map.put("dataId", dataId);
                        map.put("wfReqId", wfreqId);
                        listRandom.add(map);

                    }
                    if (listRandom.isEmpty()) {
                        erroMsg = "无可接单数据";
                    } else {
                        int randomIndex = (int) (Math.random() * listRandom.size());
                        bb.writeLog("randomIndex:" + randomIndex);
                        randomReqId = listRandom.get(randomIndex).get("wfReqId");
                        bb.writeLog("randomReqId:" + randomReqId);
                        rst = new RecordSetTrans();
                        rst.setAutoCommit(false);
                        //更新流程人力字段
                        updateWfHrm(rst, randomReqId);
                        rst.commit();

                        //将随机的流程提交
                        String re = WorkFlowUtil.submitWorkflowRequest(Integer.parseInt(randomReqId),
                                Integer.parseInt(submiter), "submit");
                        if (!"success".equals(re)) {
                            erroMsg = re;
                        } else {
                            result.put("data", randomReqId);
                        }
                        //将当前建模的状态从未审批改成审批中
                        if (erroMsg.isEmpty()) {
                            updateModuleStatus(moduleTable,
                                    status_field,
                                    userId,
                                    listRandom.get(randomIndex).get("dataId"));
                        }
                    }
                } else {
                    erroMsg = "查询建模数据失败：" + rs.getMsg() + ";" + rs.getExceptionMsg();
                }
            } else {
                erroMsg = "未收到审核人id信息";
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("status", "-1");
            result.put("erroMsg", e.getMessage());
            if (rst != null) {
                rst.rollback();
            }
        }
        if (!erroMsg.isEmpty()) {
            result.put("status", "-1");
            result.put("erroMsg", erroMsg);
        }
        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }

    /**
     * 更新流程上的人力信息
     *
     * @param rst
     * @param randomReqId
     * @throws Exception
     */
    private void updateWfHrm(RecordSetTrans rst, String randomReqId) throws Exception {
        BaseBean bb = new BaseBean();
        //流程上的人员字段
        String wfhrm_field = Util.null2String(bb.getPropValue("UnionPay_dispatch", "wfhrm_field"));
        //根据requestid查询对应的流程表
//        WfInfoVo wf = QueryWfInfoUtil.getWfInfoByRequestid(randomReqId);
//        rst.executeUpdate("update " + wf.getFormtableName() + " set " + wfhrm_field + "="
//                + Util.null2String(params.get("userId")) + " where requestid = ? ", randomReqId);
    }

    /**
     * 建模表名，状态字段名
     *
     * @param moduleTable
     * @param statusField
     */
    private void updateModuleStatus(String moduleTable, String statusField, String approver, String dataId) {
        BaseBean bb = new BaseBean();
        RecordSet rs = new RecordSet();
        //状态正常的值
        String status_approved = Util.null2String(bb.getPropValue("UnionPay_dispatch", "status_approved"));
        String approver_field = Util.null2String(bb.getPropValue("UnionPay_dispatch", "approver_field"));
        String receive_date_field = Util.null2String(bb.getPropValue("UnionPay_dispatch", "receive_date_field"));

        String today = TimeUtil.getToday();
        rs.executeUpdate("update " + moduleTable + " set " + statusField + "='" + status_approved + "'," +
                approver_field + "=" + approver + "," +
                receive_date_field + "='" + today + "' " +
                " where id =" + dataId);
    }

}
