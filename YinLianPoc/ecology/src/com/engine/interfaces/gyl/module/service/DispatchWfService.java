package com.engine.interfaces.gyl.module.service;

import weaver.hrm.User;

import java.util.Map;

/**
 * <AUTHOR> Mason
 * @description :  派单流程
 */
public interface DispatchWfService {

    /**
     * 批量派单
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> dispatchWf(Map<String, Object> params, User user);

    /**
     * 单人派单
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> dispatchWfSingle(Map<String, Object> params, User user);
}
