package com.engine.sd2.functionlog.bean;

import com.engine.parent.doc.DocUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.sd2.time.util.SDTimeUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import weaver.general.ThreadPoolUtil;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutorService;

/**
 * @FileName SDFunctionLog.java
 * @Description 二开功能执行记录
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/5/22
 */
@Data
public class SDLog {
    public final static Logger log = LoggerFactory.getLogger(SDLog.class); // 二开log类

    /**
     * 数据id
     */
    private Integer id;
    /**
     * 日志序列号，一次二开功能的执行对应一个序列号
     */
    private String serial_number;
    /**
     * 功能标识
     */
    private String fuc_code;
    /**
     * 功能简述
     */
    private String fuc_desc;
    /**
     * 功能类型
     * 0流程action
     * 1定时任务
     * 2自定义接口
     * 3建模页面扩展自定义接口动作
     * 4其他
     */
    private Integer fuc_type;
    /**
     * 执行人
     */
    private Integer execute_person;
    /**
     * 执行日期
     */
    private String execute_date;
    /**
     * 执行开始时间
     */
    private String execute_begin_time;
    /**
     * 执行结束时间
     */
    private String execute_end_time;
    /**
     * 执行耗时(ms)
     */
    private BigDecimal execute_during;
    /**
     * 执行结果
     * 0成功
     * 1失败
     */
    private Integer execute_result;
    /**
     * 失败信息(严重)
     */
    private String error;
    /**
     * 警告信息（一般错误，但不影响整体逻辑的）
     */
    private String warn_msg;
    /**
     * 日志文本
     */
    private String log_str;
    /**
     * 日志文件
     */
    private String log_file;
    /**
     * 主代码路径
     */
    private String code_path;
    /**
     * 来源模块
     */
    private String relate_module;
    /**
     * 来源表单
     */
    private String relate_table;
    /**
     * 来源数据id
     */
    private String relate_dataid;
    /**
     * 备注
     */
    private String remark;
    /**
     * 扩展字段1
     */
    private String extend1;
    /**
     * 扩展字段2
     */
    private String extend2;
    /**
     * 扩展字段3
     */
    private String extend3;
    /**
     * 扩展字段4
     */
    private String extend4;
    /**
     * 扩展字段5
     */
    private String extend5;

    /**
     * 建模表单名
     */
    public static final String TABLE_NAME = "uf_sd_function_log";

    /**
     * 成功
     */
    public static final int SUCCESS = 0;
    /**
     * 失败
     */
    public static final int FAIL = 1;
    /**
     * 功能类型 - 流程action
     */
    public static final int TYPE_ACTION = 0;
    /**
     * 功能类型 - 定时任务
     */
    public static final int TYPE_JOB = 1;
    /**
     * 功能类型 - 自定义接口
     */
    public static final int TYPE_API = 2;
    /**
     * 功能类型 - 建模页面扩展自定义接口动作
     */
    public static final int TYPE_MOD_ACTION = 3;
    /**
     * 功能类型 - 其他
     */
    public static final int TYPE_OTHERS = 4;

    /**
     * 无参构造，方便反射class时候，可以newInstance
     */
    public SDLog() {
    }

    /**
     * 构造
     *
     * @param execute_person 执行人id
     * @param functionCode   功能标识（一般为代码类名）
     * @param codePath       代码主入口路径(例如action的类路径)
     * @param functionType   功能类型  0流程action 1定时任务 2自定义接口  3建模页面扩展自定义接口动作 4其他
     * @param functionDesc   功能描述
     */
    public SDLog(int execute_person, String functionCode, String codePath, int functionType, String functionDesc) {
        this.setExecute_person(execute_person);
        this.setExecute_date(TimeUtil.getToday());
        this.setExecute_begin_time(SDTimeUtil.getCurrentTimeMillliString());
        this.setFuc_code(functionCode);
        this.setCode_path(codePath);
        this.setFuc_type(functionType);
        this.setFuc_desc(functionDesc);
        String serialNumber = UUID.randomUUID().toString().replace("-", "");
        this.setSerial_number(serialNumber);
    }

    /**
     * 同步保存日志
     *
     * @param mainLog
     * @param logStr
     */
    public static ModuleResult saveLog(SDLog mainLog, String logStr) {
        log.info("开始保存二开日志");
        try {
            int modid = ModuleDataUtil.getModuleIdByName(TABLE_NAME);
            if (modid > 0) {
                int secCatId = ModuleDataUtil.getDefaultSecCatId(modid); //获取默认的附件目录
                if (secCatId > 0 && !Util.null2String(logStr).isEmpty()) {
                    //使用实际业务操作人，作为日志文件的创建人
                    User executeUser = new User(mainLog.getExecute_person());
                    int fileDocId = DocUtil.generateStrFile2Doc(logStr, secCatId, "日志信息.txt", executeUser);
                    mainLog.setLog_file(String.valueOf(fileDocId));
                }
                mainLog.setLog_str(truncateIfExceeds(logStr)); //设置日志文本

                String endTime = SDTimeUtil.getCurrentTimeMillliString();
                mainLog.setExecute_end_time(endTime);
                mainLog.setExecute_during(BigDecimal.valueOf(SDTimeUtil.timeDifferInMillliSeconds(mainLog.getExecute_begin_time(), endTime)));
                //同步保存主表日志
                return ModuleDataUtil.insertObj(mainLog, TABLE_NAME, ModuleDataUtil.getModuleIdByName(TABLE_NAME), mainLog.getExecute_person());
            }
            log.error("未获取到二开日志的建模id，请检查建模是否已导入，不处理保存日志");
            return null;
        } catch (Exception e) {
            log.error("保存日志异常", e);
        }
        return null;
    }

    /**
     * 同步保存日志
     *
     * @param mainLog
     * @param logStr
     * @param errorMsg
     */
    public static ModuleResult saveLog(SDLog mainLog, String logStr, String errorMsg) {
        mainLog.setExecute_result(errorMsg.isEmpty() ? 0 : 1); //成功失败
        mainLog.setError(errorMsg); //错误信息
        return saveLog(mainLog, logStr);
    }

    /**
     * 同步保存日志
     *
     * @param mainLog
     * @param logStr
     * @param errorMsg
     * @param warnMsg
     */
    public static ModuleResult saveLog(SDLog mainLog, String logStr, String errorMsg, String warnMsg) {
        mainLog.setError(errorMsg); //错误信息
        mainLog.setWarn_msg(warnMsg);//警告信息
        //成功失败,没有错误也没有警告，才成功
        if (StringUtils.isBlank(errorMsg) && StringUtils.isBlank(warnMsg)) {
            mainLog.setExecute_result(0);
        } else {
            mainLog.setExecute_result(1);
        }
        return saveLog(mainLog, logStr);
    }

    /**
     * 异步保存日志
     *
     * @param mainLog
     * @param logStr
     * @param errorMsg
     */
    public static void saveLogAsync(SDLog mainLog, String logStr, String errorMsg) {
        getThreadPool().execute(() -> {
            saveLog(mainLog, logStr, errorMsg);
        });
    }

    /**
     * 异步保存日志
     *
     * @param mainLog
     * @param logStr
     * @param errorMsg
     * @param warnMsg
     */
    public static void saveLogAsync(SDLog mainLog, String logStr, String errorMsg, String warnMsg) {
        getThreadPool().execute(() -> {
            saveLog(mainLog, logStr, errorMsg, warnMsg);
        });
    }

    /**
     * 异步保存日志
     *
     * @param mainLog
     * @param logStr
     */
    public static void saveLogAsync(SDLog mainLog, String logStr) {
        //方式1：使用泛微的线程池工具类，这个线程池会先从缓存读取，没有则会创建。这个线程池不可销毁
        getThreadPool().execute(() -> {
            saveLog(mainLog, logStr);
        });
    }


    /**
     * 异步保存日志
     *
     * @param mainLog
     * @param logStr
     * @param errorMsg
     * @param updateDataLogList
     * @param apiLogList
     */
    public static void saveLogAsync(SDLog mainLog, String logStr, String errorMsg, List<SDUpdateDataLog> updateDataLogList, List<SDApiLog> apiLogList) {
        //方式1：使用泛微的线程池工具类，这个线程池会先从缓存读取，没有则会创建。这个线程池不可销毁
        getThreadPool().execute(() -> {
            //同步保存主表日志
            ModuleResult mr = saveLog(mainLog, logStr, errorMsg);
            if (mr != null && mr.isSuccess()) {
                if (updateDataLogList != null && !updateDataLogList.isEmpty()) {
                    //同步保存更新数据日志
                    SDUpdateDataLog.saveLog(updateDataLogList, mainLog, mr.getBillid());
                }
                if (apiLogList != null && !apiLogList.isEmpty()) {
                    //同步保存api接口数据日志
                    SDApiLog.saveLog(apiLogList, mainLog, mr.getBillid());
                }
            }
        });
    }

    private static ExecutorService getThreadPool() {
        return ThreadPoolUtil.getThreadPool("SDLog", "5");
    }

    /**
     * 截断文本，按最大3800字节
     *
     * @param str
     * @return
     */
    public static String truncateIfExceeds(String str) {
        int maxBytes = 3800;
        byte[] bytes = str.getBytes(StandardCharsets.UTF_8);
        if (bytes.length <= maxBytes) {
            return str;
        }
        // 精确截断到最大字节数
        int truncateLength = str.length();
        while (str.substring(0, truncateLength).getBytes(StandardCharsets.UTF_8).length > maxBytes) {
            truncateLength--;
        }
        return str.substring(0, truncateLength) + "---已截断，完整信息请查看文件";
    }


}
