package com.engine.sd2.functionlog.bean;

import com.engine.parent.doc.DocUtil;
import com.engine.parent.module.util.ModuleDataUtil;
import lombok.Data;
import weaver.general.Util;
import weaver.hrm.User;

import java.math.BigDecimal;
import java.util.List;

/**
 * @FileName SDApiLog.java
 * @Description 二开功能执行接口日志
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/6/15
 */
@Data
public class SDApiLog {
    /**
     * 数据id
     */
    private Integer id;
    /**
     * 二开功能日志id
     */
    private Integer function_logid;
    /**
     * 日志序列号，一次二开功能的执行对应一个序列号
     */
    private String serial_number;
    /**
     * 执行日期
     */
    private String excute_date;
    /**
     * 执行人
     */
    private Integer execute_person;
    /**
     * 来源模块
     */
    private String relate_module;
    /**
     * 来源表单
     */
    private String relate_table;
    /**
     * 来源数据id
     */
    private String relate_dataid;
    /**
     * 接口名称
     */
    private String api_name;
    /**
     * 接口地址
     */
    private String api_address;
    /**
     * 请求开始时间
     */
    private String request_time;
    /**
     * 请求结束时间
     */
    private String response_time;
    /**
     * 请求耗时
     */
    private BigDecimal during;
    /**
     * 接口结果
     * 0 成功
     * 1 失败
     */
    private Integer api_result;
    /**
     * 失败信息
     */
    private String error;
    /**
     * 请求报文
     */
    private String request_str;
    /**
     * 请求报文文件
     */
    private String request_file;
    /**
     * 响应报文
     */
    private String response_str;
    /**
     * 响应报文文件
     */
    private String response_file;
    /**
     * 备注
     */
    private String remark;
    /**
     * 扩展字段1
     */
    private String extend1;
    /**
     * 扩展字段2
     */
    private String extend2;
    /**
     * 扩展字段3
     */
    private String extend3;
    /**
     * 扩展字段4
     */
    private String extend4;
    /**
     * 扩展字段5
     */
    private String extend5;


    /**
     * 建模表单名
     */
    public static final String TABLE_NAME = "uf_sd_api_log";

    /**
     * 保存日志
     *
     * @param logList
     * @param functionLog
     * @param functionLogId
     */
    public static void saveLog(List<SDApiLog> logList, SDLog functionLog, int functionLogId) {
        String requestStr, responseStr;
        String requestStrNew, responseStrNew;
        int fileDocId;
        if (logList == null || logList.isEmpty()) {
            return;
        }
        int modid = ModuleDataUtil.getModuleIdByName(TABLE_NAME);
        int secCatId = ModuleDataUtil.getDefaultSecCatId(modid); //获取默认的附件目录
        User executeUser = new User(functionLog.getExecute_person());
        //从functionLog中获取数据,并设置给每个明细对象
        for (SDApiLog log : logList) {
            log.setFunction_logid(functionLogId);
            log.setFunction_logid(functionLogId);
            log.setExecute_person(functionLog.getExecute_person());
            log.setExcute_date(functionLog.getExecute_date());
            log.setRelate_module(functionLog.getRelate_module());
            log.setRelate_table(functionLog.getRelate_table());
            log.setRelate_dataid(functionLog.getRelate_dataid());

            //请求报文、响应报文，转为文件
            requestStr = Util.null2String(log.getRequest_str());
            responseStr = Util.null2String(log.getResponse_str());
            if (secCatId > 0) {
                //使用实际业务操作人，作为日志文件的创建人
                fileDocId = DocUtil.generateStrFile2Doc(requestStr, secCatId, "请求报文.txt", executeUser);
                log.setRequest_file(String.valueOf(fileDocId));

                fileDocId = DocUtil.generateStrFile2Doc(responseStr, secCatId, "响应报文.txt", executeUser);
                log.setResponse_file(String.valueOf(fileDocId));
            }
            //不超过3500字符
            requestStrNew = requestStr;
            if (requestStr.length() > 3500) {
                requestStrNew = requestStr.substring(0, 3500) + "---已截断，完整报文请查看文件";
            }

            //不超过3500字符
            responseStrNew = responseStr;
            if (responseStr.length() > 3500) {
                responseStrNew = responseStr.substring(0, 3500) + "---已截断，完整报文请查看文件";
            }
            log.setRequest_str(requestStrNew);
            log.setResponse_str(responseStrNew);
        }
        ModuleDataUtil.insertObjList(logList, TABLE_NAME, modid, functionLog.getExecute_person());
    }
}
