package com.engine.sd2.functionlog.bean;

import com.engine.parent.module.util.ModuleDataUtil;
import lombok.Data;

import java.util.List;

/**
 * @FileName FCUpdateDataLog.java
 * @Description 二开功能-数据更新日志
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/6/12
 */
@Data
public class SDUpdateDataLog {
    /**
     * 数据id
     */
    private Integer id;
    /**
     * 二开功能日志id
     */
    private Integer function_logid;
    /**
     * 日志序列号，一次二开功能的执行对应一个序列号
     */
    private String serial_number;
    /**
     * 执行日期
     */
    private String excute_date;
    /**
     * 执行人
     */
    private Integer execute_person;
    /**
     * 来源模块
     */
    private String relate_module;
    /**
     * 来源表单
     */
    private String relate_table;
    /**
     * 来源数据id
     */
    private String relate_dataid;
    /**
     * 更新的表单
     */
    private String up_table;
    /**
     * 更新的表单建模id
     */
    private Integer up_modid;
    /**
     * 更新的数据id
     */
    private String up_dataid;
    /**
     * 更新的字段s
     */
    private String up_fields;
    /**
     * 更新前数据
     */
    private String before_data;
    /**
     * 更新后数据
     */
    private String after_data;
    /**
     * 更新sql
     */
    private String up_dml;
    /**
     * 更新结果
     * 0成功
     * 1失败
     */
    private Integer up_result;
    /**
     * 更新说明
     */
    private String up_desc;
    /**
     * 失败信息
     */
    private String error;
    /**
     * 备注
     */
    private String remark;
    /**
     * 扩展字段1
     */
    private String extend1;
    /**
     * 扩展字段2
     */
    private String extend2;
    /**
     * 扩展字段3
     */
    private String extend3;
    /**
     * 扩展字段4
     */
    private String extend4;
    /**
     * 扩展字段5
     */
    private String extend5;

    /**
     * 建模表单名
     */
    public static final String TABLE_NAME = "uf_sd_updata_log";

    /**
     * 保存日志
     *
     * @param logList
     * @param functionLog
     * @param functionLogId
     */
    public static void saveLog(List<SDUpdateDataLog> logList, SDLog functionLog, int functionLogId) {
        if (logList == null || logList.isEmpty()) {
            return;
        }
        //从functionLog中获取数据,并设置给每个明细对象
        for (SDUpdateDataLog log : logList) {
            log.setFunction_logid(functionLogId);
            log.setSerial_number(functionLog.getSerial_number());
            log.setExecute_person(functionLog.getExecute_person());
            log.setExcute_date(functionLog.getExecute_date());
            log.setRelate_module(functionLog.getRelate_module());
            log.setRelate_table(functionLog.getRelate_table());
            log.setRelate_dataid(functionLog.getRelate_dataid());
        }
        ModuleDataUtil.insertObjList(logList, TABLE_NAME, ModuleDataUtil.getModuleIdByName(TABLE_NAME), functionLog.getExecute_person());
    }
}
