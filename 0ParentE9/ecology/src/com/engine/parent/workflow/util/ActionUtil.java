package com.engine.parent.workflow.util;

import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.workflow.dto.ActionInfo;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;
import weaver.workflow.request.RequestManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @FileName ActionUtil
 * @Description 流程action工具类
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/6/13
 */
public class ActionUtil {
    //使用二开log类
    private static final Logger log = LoggerFactory.getLogger(ActionUtil.class);

    /**
     * 获取action操作信息
     * 数据逻辑：前台流程操作后，会先保存流程数据，再执行附加操作，所以action拿到的数据的是最新保存之后的数据，一定会有requestid
     *
     * @param requestInfo
     * @return
     */
    public static ActionInfo getInfo(RequestInfo requestInfo) {
        ActionInfo info = new ActionInfo();
        try {
            //流程id
            String workflowId = requestInfo.getWorkflowid();
            //请求id
            String requestId = requestInfo.getRequestid();
            //RequestManager对象，获取一些流转的信息
            RequestManager rm = requestInfo.getRequestManager();
            //当前操作类型 submit:提交/reject:退回
            String src = rm.getSrc();
            //当前节点的id
            int currentNodeId = rm.getNodeid();
            //当前节点类型
            String currentNodeType = rm.getNodetype();
            //下一个节点的id
            int nextNodeId = rm.getNextNodeid();
            //下一个节点类型
            String nextNodeType = rm.getNextNodetype();
            //获取当前操作用户对象
            User usr = rm.getUser();
            //请求标题
            String requestName = rm.getRequestname();
            //当前用户提交时的签字意见
            String remark = rm.getRemark();
            //是否为单据(1为是)
            int isBill = rm.getIsbill();
            //获取数据库主表名(如果 不为1，流程数据是存在"workflow_form"表中)
            String tableName = isBill == 1 ? rm.getBillTableName() : "workflow_form";
            //获取主表信息
            Property[] propertys = requestInfo.getMainTableInfo().getProperty();
            Map<String, String> map = new HashMap<>();
            for (Property property : propertys) {
                String str = property.getName();
                String value = Util.null2String(property.getValue());
                map.put(str, value);
            }
            //put主表数据id
            int id = getIdByRequestId(requestId, tableName);
            map.put("id", String.valueOf(id));

            //转换提交类型，1提交 0退回 2强制收回 3流程干预
            int submitType = -1;
            if ("submit".equals(src)) {
                submitType = 1;
            } else if ("reject".equals(src)) {
                submitType = 0;
            } else if ("drawBack".equals(src)) {
                submitType = 2;
            } else if ("intervenor".equals(src)) {
                submitType = 3;
            }
            //获取客户端ip
            String clientIp = "";
            if (rm.getRequest() != null) {
                clientIp = Util.getIpAddr(rm.getRequest());
            }

            //获取明细表信息
            Map<Integer, List<Map<String, String>>> detailInfo = getDetailMap(requestInfo);
            info.setId(id)
                    .setRequestId(requestId)
                    .setWorkflowId(workflowId)
                    .setFormtableName(tableName)
                    .setUser(usr)
                    .setRequestName(requestName)
                    .setRemark(remark)
                    .setCurrentNodeId(currentNodeId)
                    .setCurrentNodeType(currentNodeType)
                    .setNextNodeId(nextNodeId)
                    .setNextNodeType(nextNodeType)
                    .setSubmitType(submitType)
                    .setMainData(map)
                    .setDetailData(detailInfo)
                    .setClientIp(clientIp);
        } catch (Exception e) {
            log.error("获取ActionInfo异常：", e);
        }
        return info;
    }

    /**
     * 根据请求id获取数据id
     *
     * @param requestId
     * @param tableName
     * @return
     */
    public static Integer getIdByRequestId(String requestId, String tableName) {
        RecordSet rs = new RecordSet();
        rs.executeQuery("select id from " + tableName + " where requestId = ?", requestId);
        if (rs.next()) {
            return rs.getInt("id");
        }
        return -1;
    }

    /**
     * 获取明细表Map
     *
     * @param request
     * @return key:明细表的index
     * value:明细表字段合集
     */
    public static Map<Integer, List<Map<String, String>>> getDetailMap(RequestInfo request) {
        Map<Integer, List<Map<String, String>>> result = new HashMap<>();
        try {
            //获取明细表
            DetailTable[] detailtable = request.getDetailTableInfo().getDetailTable();
            for (DetailTable detailTable : detailtable) {
                String tablename = "";
                Row[] s = detailTable.getRow();
                if (s.length > 0) {
                    List<Map<String, String>> rowList = new ArrayList<>();
                    for (Row row : s) {
                        Map<String, String> rowMap = new HashMap<>();
                        rowMap.put("id", row.getId());
                        Cell[] c = row.getCell();
                        for (Cell c1 : c) {
                            String name = c1.getName();
                            String value = Util.null2String(c1.getValue());
                            tablename = c1.getCol1();
                            rowMap.put(name, value);
                        }
                        rowList.add(rowMap);
                    }
                    int detailIndex = Integer.parseInt(tablename.substring(tablename.indexOf("_dt") + 3));
                    result.put(detailIndex, rowList);
                }

            }
        } catch (Exception e) {
            log.error("获取action明细表数据出错:" + SDUtil.getExceptionDetail(e));
        }
        return result;
    }

    /**
     * 处理action结果，判断errorMsg是否有值
     *
     * @param errorMsg
     * @param requestInfo
     * @return
     */
    public static String handleResult(String errorMsg, RequestInfo requestInfo) {
        if (errorMsg == null || errorMsg.trim().isEmpty()) {
            log.info("Action Return requestid:" + requestInfo.getRequestid() + " SUCCESS END");
            return Action.SUCCESS;
        } else {
            //流程提交失败信息内容
            //前后增加-，防止json格式内容不显示
            errorMsg = "-" + errorMsg + "-";
            requestInfo.getRequestManager().setMessagecontent(errorMsg);
            log.warn("Action Return requestid:" + requestInfo.getRequestid() + " FAIL END");
            return Action.FAILURE_AND_CONTINUE;
        }
    }

    /**
     * 获取明细表 list
     *
     * @param requestInfo
     * @param detailnum   明细表序号 as 1,2,3
     * @return ArrayList<HashMap < String, String>> or null
     */
    public static ArrayList<HashMap<String, String>> getDetailMap(RequestInfo requestInfo, Integer detailnum) {
        DetailTable[] detailtable = requestInfo.getDetailTableInfo().getDetailTable();
        if (detailtable.length == 0) {
            return null;
        } else if (detailnum <= detailtable.length && detailnum > 0) {
            DetailTable dt = detailtable[detailnum - 1];
            ArrayList<HashMap<String, String>> detaillist = new ArrayList<>();
            Row[] s = dt.getRow();
            for (Row r : s) {
                Cell[] c = r.getCell();
                HashMap<String, String> rowmap = new HashMap<>();
                for (Cell c1 : c) {
                    rowmap.put(Util.null2String(c1.getName()), Util.null2String(c1.getValue()));
                }
                detaillist.add(rowmap);
            }
            return detaillist;
        } else {
            return null;
        }
    }

    /**
     * 主表Map
     *
     * @param requestInfo
     * @return HashMap<String, String>
     */
    public static HashMap<String, String> getMainMap(RequestInfo requestInfo) {
        HashMap<String, String> maindata = new HashMap<>();
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        for (Property property : propertys) {
            String str = Util.null2String(property.getName());
            String value = Util.null2String(property.getValue());
            maindata.put(str, value);
        }
        return maindata;
    }


}
