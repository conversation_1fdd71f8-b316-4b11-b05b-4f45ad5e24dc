//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package weaver.zzn.zc;

import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import weaver.zzn.zc.ws.*;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.net.URL;
import java.util.*;

public class Action_Approve_NC implements Action {
    public Action_Approve_NC() {
    }

    public String execute(RequestInfo requestInfo) {
        BaseBean bb = new BaseBean();
        (new BaseBean()).writeLog("\n>>>>>>>>>>>>>>>>>>>>>>Action_Approve_NC Begining<<<<<<<<<<<<<<<<<");
        String requestid = Util.null2String(requestInfo.getRequestid());
        String workflowid = Util.null2String(requestInfo.getWorkflowid());
        int formid = requestInfo.getRequestManager().getFormid();
        String request_name = "";
        String request_mark = "";
        RecordSet rs = new RecordSet();
        String select_base_sql = "select * from workflow_requestbase where requestid = ?";

        try {
            if (rs.executeQuery(select_base_sql, new Object[]{requestid})) {
                while (rs.next()) {
                    request_name = Util.null2String(rs.getString("requestname"));
                    request_mark = Util.null2String(rs.getString("requestmark"));
                }
            }

            (new BaseBean()).writeLog("main_requestname:[" + request_name + "],main_requestmark:[" + request_mark + "],workflowid:[" + workflowid + "],requestid:[" + requestid + "],formid:[" + formid + "]");
        } catch (Exception var28) {
            (new BaseBean()).writeLog("获取该流程事物数据集异常:[" + var28.getMessage() + "/" + var28.toString() + "]");
            return "0";
        }

        String[] baseArray = new String[]{requestid, request_name, request_mark};
        String requestUrl = "";
        String select_main_config = "select * from \tuf_approve4NC where jkss = 0 and lclx in (select id from workflow_base where activeVersionID in (select activeVersionID  from workflow_base where id = ?) or id = ?)";
        (new BaseBean()).writeLog("\n查询主表配置信息:[" + select_main_config + "]");
        String config_main_keyid = "";
        int indexs = 0;
        if (rs.executeQuery(select_main_config, new Object[]{workflowid, workflowid})) {
            while (rs.next()) {
                requestUrl = Util.null2String(rs.getString("jkdz"));
                config_main_keyid = Util.null2String(rs.getString("id"));
                indexs = Util.getIntValue(rs.getString("indexs"), 0);
            }
        }

        (new BaseBean()).writeLog("\n接口地址requestUrl=" + requestUrl + "  indexs=" + indexs);
        List<Map<String, Object>> oamain_list = new ArrayList<>();
        String select_detail_config = "select dt.*,wb.fieldname,wb.viewtype from uf_approve4NC_dt1 dt left join workflow_billfield wb on dt.OAFieldname = wb.id where dt.mainid = ? and dt.belongto = 0 ";
        (new BaseBean()).writeLog("\n主表字段记录:[" + select_detail_config + "],[" + config_main_keyid + "]");
        String ncFieldname, oaFieldName;
        int viewtype;
        int detailindex;
        int changerule;
        String belongto, cussql;
        if (rs.executeQuery(select_detail_config, new Object[]{config_main_keyid})) {
            while (rs.next()) {
                ncFieldname = Util.null2String(rs.getString("NCFieldname"));
                oaFieldName = Util.null2String(rs.getString("fieldname"));
                viewtype = Util.getIntValue(rs.getString("viewtype"), 0);
                detailindex = Util.getIntValue(rs.getString("detailindex"), 0);
                changerule = Util.getIntValue(rs.getString("changerule"), 0);
                cussql = Util.null2String(rs.getString("cussql"));
                belongto = Util.null2String(rs.getString("belongto"));
                Map<String, Object> detail_map = new HashMap<>();
                detail_map.put("ncFieldname", ncFieldname);
                detail_map.put("oaFieldname", oaFieldName);
                detail_map.put("viewtype", viewtype);
                detail_map.put("detailindex", detailindex);
                detail_map.put("changerule", changerule);
                detail_map.put("cussql", cussql);
                detail_map.put("belongto", belongto);
                oamain_list.add(detail_map);
            }
        }

        List<Map<String, Object>> oadetail_list = new ArrayList<>();
        select_detail_config = "select dt.*,wb.fieldname,wb.viewtype from uf_approve4NC_dt1 dt left join workflow_billfield wb on dt.OAFieldname = wb.id where dt.mainid = ? and dt.belongto = 1";
        (new BaseBean()).writeLog("\n主表字段记录:[" + select_detail_config + "],[" + config_main_keyid + "]");
        String select_detail;
        if (rs.executeQuery(select_detail_config, new Object[]{config_main_keyid})) {
            while (rs.next()) {
                ncFieldname = Util.null2String(rs.getString("NCFieldname"));
                oaFieldName = Util.null2String(rs.getString("fieldname"));
                viewtype = Util.getIntValue(rs.getString("viewtype"), 0);
                detailindex = Util.getIntValue(rs.getString("detailindex"), 0);
                changerule = Util.getIntValue(rs.getString("changerule"), 0);
                cussql = Util.null2String(rs.getString("cussql"));
                select_detail = Util.null2String(rs.getString("belongto"));
                Map<String, Object> detail_map = new HashMap<>();
                detail_map.put("ncFieldname", ncFieldname);
                detail_map.put("oaFieldname", oaFieldName);
                detail_map.put("viewtype", viewtype);
                detail_map.put("detailindex", detailindex);
                detail_map.put("changerule", changerule);
                detail_map.put("cussql", cussql);
                detail_map.put("belongto", select_detail);
                oadetail_list.add(detail_map);
            }
        }

        Map<String, String> oamain_value_map = new HashMap<>();
        RecordSet rs_main = new RecordSet();
        String select_main = "select * from formtable_main_" + Math.abs(formid) + " where requestid = ?";
        String workflow_main_keyid = "";
        if (rs_main.executeQuery(select_main, new Object[]{requestid})) {
            rs_main.next();
            workflow_main_keyid = Util.null2String(rs_main.getString("id"));
            if (oamain_list != null && oamain_list.size() > 0) {
                oamain_value_map.putAll(this.getAttributeValue(oamain_list, rs_main, (RecordSet) null, 0, Util.getIntValue(workflow_main_keyid, 0), -1, baseArray));
            }
        }

        (new BaseBean()).writeLog("\n【流程主表字段的值=+" + oamain_value_map + "】");
        List<Map<String, String>> oadetail_value_list = new ArrayList();
        RecordSet rs_detail = new RecordSet();
        if (indexs > 0) {
            select_detail = "select dt.* from formtable_main_" + Math.abs(formid) + " fm inner join formtable_main_" + Math.abs(formid) + "_dt" + indexs + " dt on fm.id = dt.mainid where requestid = ?";
            (new BaseBean()).writeLog("\n明细表获取sql=" + select_detail);
            if (rs_detail.executeQuery(select_detail, new Object[]{requestid})) {
                while (rs_detail.next()) {
                    String workflow_detail_keyid = Util.null2String(rs_detail.getString("id"));
                    if (oadetail_list != null && oadetail_list.size() > 0) {
                        oadetail_value_list.add(this.getAttributeValue(oadetail_list, rs_main, rs_detail, 0, Util.getIntValue(workflow_detail_keyid, 0), -1, baseArray));
                    }
                }
            }

            (new BaseBean()).writeLog("\n【流程明细表表字段的值=+" + oadetail_value_list + "】");
        }

        try {
            (new BaseBean()).writeLog("\n反射机制注入对象值1");
            NcInterceptionRequest ncInterceptionRequest = new NcInterceptionRequest();
            (new BaseBean()).writeLog("\n反射机制注入对象值2");
            bb.writeLog("oamain_value_map:" + oamain_value_map);
            ncInterceptionRequest = (NcInterceptionRequest) this.setObjectValue(ncInterceptionRequest, oamain_value_map);
            (new BaseBean()).writeLog("\n封装明细");
            if (oadetail_value_list.size() > 0) {
                bb.writeLog("oadetail_value_list:" + oadetail_value_list);
                NcInterceptionRequestData[] datas = this.setDetailObjectValue(oadetail_value_list);
                (new BaseBean()).writeLog("\ndata长度=" + datas.length);
                ncInterceptionRequest.setDatas(datas);
            }

            (new BaseBean()).writeLog("\nncInterceptionRequest=" + JSONObject.toJSONString(ncInterceptionRequest));
            URL url = new URL(requestUrl);
            NcInterceptionServiceSOAP11BindingStub bind = (NcInterceptionServiceSOAP11BindingStub) (new NcInterceptionServiceLocator()).getNcInterceptionServiceSOAP11port_http(url);
            NcInterceptionResult ncresult = bind.updateBXZT(ncInterceptionRequest);
            bb.writeLog("NcInterceptionResult:" + JSONObject.toJSONString(ncresult));
            (new BaseBean()).writeLog("\nncresult=" + ncresult.getIsSuccess() + "  描述=" + ncresult.getErrMsg());
            if ("0".equals(ncresult.getIsSuccess())) {
                (new BaseBean()).writeLog("\n>>>>>>>>>>>>>>>>>>>>>>Action_Approve_NC_SUCCESS Ending<<<<<<<<<<<<<<<<<");
                return "1";
            } else {
                (new BaseBean()).writeLog("\n>>>>>>>>>>>>>>>>>>>>>>Action_Approve_NC_ERROR Ending<<<<<<<<<<<<<<<<<");
                requestInfo.getRequestManager().setMessagecontent(ncresult.getErrMsg());
                return "0";
            }
        } catch (Exception var27) {
            (new BaseBean()).writeLog("\ne=" + Arrays.toString(var27.getStackTrace()));
            requestInfo.getRequestManager().setMessagecontent("审核结果回写NC失败,请稍后再试");
            (new BaseBean()).writeLog("\n>>>>>>>>>>>>>>>>>>>>>>Action_Approve_NC Ending<<<<<<<<<<<<<<<<<");
            return "0";
        }
    }

    private NcInterceptionRequestData[] setDetailObjectValue(List<Map<String, String>> oadetail_value_list) {
        NcInterceptionRequestData[] datas = new NcInterceptionRequestData[oadetail_value_list.size()];
        if (oadetail_value_list.size() > 0) {
            for (int i = 0; i < oadetail_value_list.size(); ++i) {
                NcInterceptionRequestData ncInterceptionRequestData = new NcInterceptionRequestData();
                Field[] detail_fields = ncInterceptionRequestData.getClass().getDeclaredFields();
                Field[] arr$ = detail_fields;
                int len$ = detail_fields.length;

                for (int i$ = 0; i$ < len$; ++i$) {
                    Field field = arr$[i$];
                    String fieldname = field.getName();
                    String type = field.getGenericType().toString();
                    (new BaseBean()).writeLog("\n明细类型=" + type);
                    if (type.equals("class java.lang.String")) {
                        try {
                            (new BaseBean()).writeLog("\n对象值=set" + fieldname.substring(0, 1).toUpperCase() + fieldname.substring(1) + "  反射机制的值=" + (String) ((Map) oadetail_value_list.get(i)).get(fieldname));
                            Method m = ncInterceptionRequestData.getClass().getMethod("set" + fieldname.substring(0, 1).toUpperCase() + fieldname.substring(1), String.class);
                            m.invoke(ncInterceptionRequestData, Util.null2String((String) ((Map) oadetail_value_list.get(i)).get(fieldname)));
                            datas[i] = ncInterceptionRequestData;
                        } catch (Exception var13) {
                            var13.printStackTrace();
                            (new BaseBean()).writeLog("明细注入类属性赋值异常:[" + fieldname + "],[" + var13.toString() + "],[" + var13.getMessage() + "]");
                        }
                    }
                }
            }
        }

        return datas;
    }

    private Object setObjectValue(Object header_obj, Map<String, String> header_value_map) {
        (new BaseBean()).writeLog("\nsetObjectValue=" + header_value_map);
        Field[] fields = header_obj.getClass().getDeclaredFields();
        Field[] arr$ = fields;
        int len$ = fields.length;

        for (int i$ = 0; i$ < len$; ++i$) {
            Field field = arr$[i$];
            String fieldname = field.getName();
            String type = field.getGenericType().toString();
            if (type.equals("class java.lang.String")) {
                try {
                    (new BaseBean()).writeLog("\n2222=" + fieldname.substring(0, 1).toUpperCase() + fieldname.substring(1));
                    (new BaseBean()).writeLog("\n对象值=set" + fieldname.substring(0, 1).toUpperCase() + fieldname.substring(1));
                    Method m = header_obj.getClass().getMethod("set" + fieldname.substring(0, 1).toUpperCase() + fieldname.substring(1), String.class);
                    (new BaseBean()).writeLog("\n反射机制的值=" + (String) header_value_map.get(fieldname));
                    m.invoke(header_obj, Util.null2String((String) header_value_map.get(fieldname)));
                } catch (Exception var11) {
                    var11.printStackTrace();
                    (new BaseBean()).writeLog("类属性赋值异常:[" + fieldname + "],[" + var11.toString() + "],[" + var11.getMessage() + "]");
                }
            }
        }

        return header_obj;
    }

    private Map<String, String> getAttributeValue(List<Map<String, Object>> list, RecordSet rs_main, RecordSet rs_detail, int datatype, int detailKeyValue, int itemType, String[] baseArray) {
        Map<String, String> attribute_map = new HashMap();
        if (list != null && list.size() > 0) {
            Iterator i$ = list.iterator();

            while (i$.hasNext()) {
                Map<String, Object> detail_map = (Map) i$.next();
                String ncFieldname = Util.null2String(detail_map.get("ncFieldname"));
                String fieldvalue = this.getFieldValue(detail_map, rs_main, rs_detail, detailKeyValue, datatype, baseArray);
                (new BaseBean()).writeLog("ncFieldname:[" + ncFieldname.toUpperCase() + "],fieldvalue:[" + fieldvalue + "]");
                attribute_map.put(ncFieldname, fieldvalue);
            }
        }

        return attribute_map;
    }

    private String getFieldValue(Map<String, Object> configMap, RecordSet rs, RecordSet rs_detail, int detailKeyValue, int datatype, String[] baseArray) {
        (new BaseBean()).writeLog("\n配置信息=" + configMap);
        String wffieldname = (String) configMap.get("oaFieldname");
        int changerule = (Integer) configMap.get("changerule");
        String cussql = (String) configMap.get("cussql");
        cussql = cussql.replace("&nbsp;", " ");
        cussql = cussql.replace("////", "@");
        String wfbelongto = Util.null2String(configMap.get("belongto"));
        String ncFieldname = Util.null2String(configMap.get("ncFieldname"));
        String wffieldvalue = "";
        if (!"".equals(wffieldname)) {
            if ("0".equals(wfbelongto)) {
                wffieldvalue = Util.null2String(rs.getString(wffieldname));
            } else {
                wffieldvalue = Util.null2String(rs_detail.getString(wffieldname));
            }
        }

        (new BaseBean()).writeLog("\nwffieldvalue【" + wffieldvalue + "】");
        if (changerule == 0) {
            wffieldvalue = baseArray[0];
        } else if (changerule == 1) {
            wffieldvalue = baseArray[1];
        } else if (changerule == 2) {
            wffieldvalue = baseArray[2];
        } else if (changerule == 3) {
            wffieldvalue = TimeUtil.getCurrentTimeString().toString();
        } else if (changerule == 4) {
            wffieldvalue = cussql;
        } else if (changerule == 6) {
            String changevalue = this.getValueByChangeRule(cussql, wffieldvalue, baseArray[0], detailKeyValue);
            wffieldvalue = changevalue;
        }

        return wffieldvalue;
    }

    public String getValueByChangeRule(String cus_sql, String value, String requestid, int detailKeyvalue) {
        String endValue = "";
        (new BaseBean()).writeLog("\ncus_sql=" + cus_sql + "  value=" + value + "  requestid=" + requestid);
        cus_sql = cus_sql.replace("&nbsp;", " ");
        cus_sql = cus_sql.replace("{?dt.id}", String.valueOf(detailKeyvalue));
        String sqlString = cus_sql.replace("{?requestid}", requestid);
        sqlString = sqlString.replace("?", value);
        if (detailKeyvalue > 0) {
            (new BaseBean()).writeLog("执行的转换后的SQL:[" + sqlString + "]");
        }

        RecordSet rs = new RecordSet();
        if (rs.execute(sqlString)) {
            rs.next();
            endValue = Util.null2String(rs.getString(1));
        }

        return endValue;
    }
}
