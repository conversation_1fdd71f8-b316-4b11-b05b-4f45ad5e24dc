//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package weaver.zzn.zc;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import weaver.zzn.zc.ws.NcInterceptionRequest;
import weaver.zzn.zc.ws.NcInterceptionResult;
import weaver.zzn.zc.ws.NcInterceptionServiceLocator;
import weaver.zzn.zc.ws.NcInterceptionServiceSOAP11BindingStub;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.net.URL;
import java.util.*;

public class Action_RejectAudit_NC extends ToolUtil implements Action {
    public Action_RejectAudit_NC() {
    }

    public String execute(RequestInfo requestInfo) {
        (new BaseBean()).writeLog("\n>>>>>>>>>>>>>>>>>>>>>>Action_RejectAudit_NC Begining<<<<<<<<<<<<<<<<<");
        String workflowid = Util.null2String(requestInfo.getWorkflowid());
        String src = requestInfo.getRequestManager().getSrc();
        String requestid = Util.null2String(requestInfo.getRequestid());
        int formid = requestInfo.getRequestManager().getFormid();
        if ("reject".equals(src)) {
            String request_name = "";
            String request_mark = "";
            RecordSet rs = new RecordSet();
            String select_base_sql = "select * from workflow_requestbase where requestid = ?";

            try {
                if (rs.executeQuery(select_base_sql, new Object[]{requestid})) {
                    while (rs.next()) {
                        request_name = Util.null2String(rs.getString("requestname"));
                        request_mark = Util.null2String(rs.getString("requestmark"));
                    }
                }

                (new BaseBean()).writeLog("main_requestname:[" + request_name + "],main_requestmark:[" + request_mark + "],workflowid:[" + workflowid + "],requestid:[" + requestid + "],formid:[" + formid + "]");
            } catch (Exception var25) {
                var25.printStackTrace();
                (new BaseBean()).writeLog("获取该流程事物数据集异常:[" + var25.getMessage() + "/" + var25.toString() + "]");
                return "0";
            }

            String[] baseArray = new String[]{requestid, request_name, request_mark};
            String requestUrl = "";
            String select_main_config = "select * from \tuf_approve4NC where jkss = 1 and lclx in (select id from workflow_base where activeVersionID in (select activeVersionID  from workflow_base where id = ?) or id = ?)";
            (new BaseBean()).writeLog("\n查询主表配置信息:[" + select_main_config + "]");
            String config_main_keyid = "";
            if (rs.executeQuery(select_main_config, new Object[]{workflowid, workflowid})) {
                while (rs.next()) {
                    requestUrl = Util.null2String(rs.getString("jkdz"));
                    config_main_keyid = Util.null2String(rs.getString("id"));
                }
            }

            List<Map<String, Object>> oamain_list = new ArrayList();
            String select_detail_config = "select dt.*,wb.fieldname,wb.viewtype from uf_approve4NC_dt1 dt left join workflow_billfield wb on dt.OAFieldname = wb.id where dt.mainid = ?";
            (new BaseBean()).writeLog("\n主表字段记录:[" + select_detail_config + "],[" + config_main_keyid + "]");
            if (rs.executeQuery(select_detail_config, new Object[]{config_main_keyid})) {
                while (rs.next()) {
                    String ncFieldname = Util.null2String(rs.getString("NCFieldname"));
                    String OAFieldname = Util.null2String(rs.getString("fieldname"));
                    int viewtype = Util.getIntValue(rs.getString("viewtype"), 0);
                    int detailindex = Util.getIntValue(rs.getString("detailindex"), 0);
                    int changerule = Util.getIntValue(rs.getString("changerule"), 0);
                    String cussql = Util.null2String(rs.getString("cussql"));
                    Map<String, Object> detail_map = new HashMap();
                    detail_map.put("ncFieldname", ncFieldname);
                    detail_map.put("oaFieldname", OAFieldname);
                    detail_map.put("viewtype", viewtype);
                    detail_map.put("detailindex", detailindex);
                    detail_map.put("changerule", changerule);
                    detail_map.put("cussql", cussql);
                    oamain_list.add(detail_map);
                }
            }

            Map<String, String> oamain_value_map = new HashMap();
            RecordSet rs_main = new RecordSet();
            String select_main = "select * from formtable_main_" + Math.abs(formid) + " where requestid = ?";
            String workflow_main_keyid = "";
            if (rs_main.executeQuery(select_main, new Object[]{requestid})) {
                rs_main.next();
                workflow_main_keyid = Util.null2String(rs_main.getString("id"));
                if (oamain_list != null && oamain_list.size() > 0) {
                    oamain_value_map.putAll(this.getAttributeValue(oamain_list, rs_main, (RecordSet) null, 0, Util.getIntValue(workflow_main_keyid, 0), -1, baseArray));
                }
            }

            (new BaseBean()).writeLog("\n【流程主表字段的值=+" + oamain_value_map + "】");

            try {
                (new BaseBean()).writeLog("\n反射机制注入对象值1");
                NcInterceptionRequest ncInterceptionRequest = new NcInterceptionRequest();
                (new BaseBean()).writeLog("\n初始化对象完成2");
                ncInterceptionRequest = (NcInterceptionRequest) this.setObjectValue(ncInterceptionRequest, oamain_value_map);
                (new BaseBean()).writeLog("\nncInterceptionRequest=" + ncInterceptionRequest.getApproveDate());
                URL url = new URL(requestUrl);
                NcInterceptionServiceSOAP11BindingStub bind = (NcInterceptionServiceSOAP11BindingStub) (new NcInterceptionServiceLocator()).getNcInterceptionServiceSOAP11port_http(url);
                NcInterceptionResult ncresult = bind.updateBXZT(ncInterceptionRequest);
                (new BaseBean()).writeLog("\nncresult=" + ncresult.getIsSuccess() + "  描述=" + ncresult.getErrMsg());
                (new BaseBean()).writeLog("\n>>>>>>>>>>>>>>>>>>>>>>Action_Approve_NC Ending<<<<<<<<<<<<<<<<<");
                return "1";
            } catch (Exception var24) {
                (new BaseBean()).writeLog("\ne=" + var24.getMessage());
                requestInfo.getRequestManager().setMessagecontent("审核结果回写NC失败,请稍后再试");
                (new BaseBean()).writeLog("\n>>>>>>>>>>>>>>>>>>>>>>Action_Approve_NC Ending<<<<<<<<<<<<<<<<<");
                return "0";
            }
        } else {
            (new BaseBean()).writeLog("\n>>>>>>>>>>>>>>>>>>>>>>Action_RejectAudit_NC Ending<<<<<<<<<<<<<<<<<");
            return "1";
        }
    }

    private Object setObjectValue(Object header_obj, Map<String, String> header_value_map) {
        Field[] fields = header_obj.getClass().getDeclaredFields();
        Field[] arr$ = fields;
        int len$ = fields.length;

        for (int i$ = 0; i$ < len$; ++i$) {
            Field field = arr$[i$];
            String fieldname = field.getName();
            String type = field.getGenericType().toString();
            if (type.equals("class java.lang.String")) {
                try {
                    (new BaseBean()).writeLog("\n2222=" + fieldname.substring(0, 1).toUpperCase() + fieldname.substring(1));
                    Method m = header_obj.getClass().getMethod("set" + fieldname.substring(0, 1).toUpperCase() + fieldname.substring(1), String.class);
                    (new BaseBean()).writeLog("\n反射机制的值=" + (String) header_value_map.get(fieldname));
                    m.invoke(header_obj, Util.null2String((String) header_value_map.get(fieldname)));
                } catch (Exception var11) {
                    var11.printStackTrace();
                    this.writeNewDebuggerLog(this.getClass(), "类属性赋值异常:[" + fieldname + "],[" + var11.toString() + "],[" + var11.getMessage() + "]");
                }
            }
        }

        return header_obj;
    }

    private Map<String, String> getAttributeValue(List<Map<String, Object>> list, RecordSet rs_main, RecordSet rs_detail, int datatype, int detailKeyValue, int itemType, String[] baseArray) {
        Map<String, String> attribute_map = new HashMap();
        if (list != null && list.size() > 0) {
            Iterator i$ = list.iterator();

            while (i$.hasNext()) {
                Map<String, Object> detail_map = (Map) i$.next();
                String ncFieldname = Util.null2String(detail_map.get("ncFieldname"));
                String fieldvalue = this.getFieldValue(detail_map, rs_main, rs_detail, detailKeyValue, baseArray);
                attribute_map.put(ncFieldname, fieldvalue);
            }
        }

        return attribute_map;
    }

    private String getFieldValue(Map<String, Object> configMap, RecordSet rs, RecordSet rs_detail, int detailKeyValue, String[] baseArray) {
        String wffieldname = (String) configMap.get("oaFieldname");
        int changerule = (Integer) configMap.get("changerule");
        String cussql = (String) configMap.get("cussql");
        cussql = cussql.replace("&nbsp;", " ");
        cussql = cussql.replace("////", "@");
        int wfbelongto = (Integer) configMap.get("viewtype");
        String ncFieldname = Util.null2String(configMap.get("ncFieldname"));
        String wffieldvalue = "";
        if (!"".equals(wffieldname)) {
            wffieldvalue = Util.null2String(rs.getString(wffieldname));
        }

        if (changerule == 0) {
            wffieldvalue = baseArray[0];
        } else if (changerule == 1) {
            wffieldvalue = baseArray[1];
        } else if (changerule == 2) {
            wffieldvalue = baseArray[2];
        } else if (changerule == 3) {
            wffieldvalue = TimeUtil.getCurrentTimeString().toString();
        } else if (changerule == 4) {
            wffieldvalue = cussql;
        } else if (changerule == 6) {
            String changevalue = this.getValueByChangeRule(cussql, wffieldvalue, baseArray[0], detailKeyValue);
            wffieldvalue = changevalue;
        }

        return wffieldvalue;
    }
}
