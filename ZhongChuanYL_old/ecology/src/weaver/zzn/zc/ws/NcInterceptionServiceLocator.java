//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package weaver.zzn.zc.ws;

import org.apache.axis.AxisFault;
import org.apache.axis.EngineConfiguration;
import org.apache.axis.client.Service;
import org.apache.axis.client.Stub;

import javax.xml.namespace.QName;
import javax.xml.rpc.ServiceException;
import java.net.MalformedURLException;
import java.net.URL;
import java.rmi.Remote;
import java.util.HashSet;
import java.util.Iterator;

public class NcInterceptionServiceLocator extends Service implements NcInterceptionService {
    private String NcInterceptionServiceSOAP11port_http_address = "http://*************:9088/uapws/service/NcInterceptionService";
    private String NcInterceptionServiceSOAP11port_httpWSDDServiceName = "NcInterceptionServiceSOAP11port_http";
    private HashSet ports = null;

    public NcInterceptionServiceLocator() {
    }

    public NcInterceptionServiceLocator(EngineConfiguration config) {
        super(config);
    }

    public NcInterceptionServiceLocator(String wsdlLoc, QName sName) throws ServiceException {
        super(wsdlLoc, sName);
    }

    public String getNcInterceptionServiceSOAP11port_httpAddress() {
        return this.NcInterceptionServiceSOAP11port_http_address;
    }

    public String getNcInterceptionServiceSOAP11port_httpWSDDServiceName() {
        return this.NcInterceptionServiceSOAP11port_httpWSDDServiceName;
    }

    public void setNcInterceptionServiceSOAP11port_httpWSDDServiceName(String name) {
        this.NcInterceptionServiceSOAP11port_httpWSDDServiceName = name;
    }

    public NcInterceptionServicePortType getNcInterceptionServiceSOAP11port_http() throws ServiceException {
        URL endpoint;
        try {
            endpoint = new URL(this.NcInterceptionServiceSOAP11port_http_address);
        } catch (MalformedURLException var3) {
            throw new ServiceException(var3);
        }

        return this.getNcInterceptionServiceSOAP11port_http(endpoint);
    }

    public NcInterceptionServicePortType getNcInterceptionServiceSOAP11port_http(URL portAddress) throws ServiceException {
        try {
            NcInterceptionServiceSOAP11BindingStub _stub = new NcInterceptionServiceSOAP11BindingStub(portAddress, this);
            _stub.setPortName(this.getNcInterceptionServiceSOAP11port_httpWSDDServiceName());
            return _stub;
        } catch (AxisFault var3) {
            return null;
        }
    }

    public void setNcInterceptionServiceSOAP11port_httpEndpointAddress(String address) {
        this.NcInterceptionServiceSOAP11port_http_address = address;
    }

    public Remote getPort(Class serviceEndpointInterface) throws ServiceException {
        try {
            if (NcInterceptionServicePortType.class.isAssignableFrom(serviceEndpointInterface)) {
                NcInterceptionServiceSOAP11BindingStub _stub = new NcInterceptionServiceSOAP11BindingStub(new URL(this.NcInterceptionServiceSOAP11port_http_address), this);
                _stub.setPortName(this.getNcInterceptionServiceSOAP11port_httpWSDDServiceName());
                return _stub;
            }
        } catch (Throwable var3) {
            throw new ServiceException(var3);
        }

        throw new ServiceException("There is no stub implementation for the interface:  " + (serviceEndpointInterface == null ? "null" : serviceEndpointInterface.getName()));
    }

    public Remote getPort(QName portName, Class serviceEndpointInterface) throws ServiceException {
        if (portName == null) {
            return this.getPort(serviceEndpointInterface);
        } else {
            String inputPortName = portName.getLocalPart();
            if ("NcInterceptionServiceSOAP11port_http".equals(inputPortName)) {
                return this.getNcInterceptionServiceSOAP11port_http();
            } else {
                Remote _stub = this.getPort(serviceEndpointInterface);
                ((Stub) _stub).setPortName(portName);
                return _stub;
            }
        }
    }

    public QName getServiceName() {
        return new QName("http://itf.servlet.yonyou.bs.nc/NcInterceptionService", "NcInterceptionService");
    }

    public Iterator getPorts() {
        if (this.ports == null) {
            this.ports = new HashSet();
            this.ports.add(new QName("http://itf.servlet.yonyou.bs.nc/NcInterceptionService", "NcInterceptionServiceSOAP11port_http"));
        }

        return this.ports.iterator();
    }

    public void setEndpointAddress(String portName, String address) throws ServiceException {
        if ("NcInterceptionServiceSOAP11port_http".equals(portName)) {
            this.setNcInterceptionServiceSOAP11port_httpEndpointAddress(address);
        } else {
            throw new ServiceException(" Cannot set Endpoint Address for Unknown Port" + portName);
        }
    }

    public void setEndpointAddress(QName portName, String address) throws ServiceException {
        this.setEndpointAddress(portName.getLocalPart(), address);
    }
}
