//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package weaver.zzn.zc.ws;

import org.apache.axis.AxisFault;
import org.apache.axis.NoEndPointException;
import org.apache.axis.client.Call;
import org.apache.axis.client.Stub;
import org.apache.axis.constants.Style;
import org.apache.axis.constants.Use;
import org.apache.axis.description.OperationDesc;
import org.apache.axis.description.ParameterDesc;
import org.apache.axis.encoding.DeserializerFactory;
import org.apache.axis.encoding.ser.*;
import org.apache.axis.soap.SOAPConstants;
import org.apache.axis.utils.JavaUtils;
import weaver.general.BaseBean;

import javax.xml.namespace.QName;
import javax.xml.rpc.Service;
import javax.xml.rpc.encoding.SerializerFactory;
import java.net.URL;
import java.rmi.RemoteException;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.Vector;

public class NcInterceptionServiceSOAP11BindingStub extends Stub implements NcInterceptionServicePortType {
    private Vector cachedSerClasses;
    private Vector cachedSerQNames;
    private Vector cachedSerFactories;
    private Vector cachedDeserFactories;
    static OperationDesc[] _operations = new OperationDesc[1];

    private static void _initOperationDesc1() {
        OperationDesc oper = new OperationDesc();
        oper.setName("updateBXZT");
        ParameterDesc param = new ParameterDesc(new QName("", "ncInterceptionRequest"), (byte) 1, new QName("http://itf.servlet.yonyou.bs.nc/NcInterceptionRequest", "NcInterceptionRequest"), NcInterceptionRequest.class, false, false);
        param.setOmittable(true);
        param.setNillable(true);
        oper.addParameter(param);
        oper.setReturnType(new QName("http://itf.servlet.yonyou.bs.nc/NcInterceptionResult", "NcInterceptionResult"));
        oper.setReturnClass(NcInterceptionResult.class);
        oper.setReturnQName(new QName("", "return"));
        oper.setStyle(Style.WRAPPED);
        oper.setUse(Use.LITERAL);
        _operations[0] = oper;
    }

    public NcInterceptionServiceSOAP11BindingStub() throws AxisFault {
        this((Service) null);
    }

    public NcInterceptionServiceSOAP11BindingStub(URL endpointURL, Service service) throws AxisFault {
        this(service);
        super.cachedEndpoint = endpointURL;
    }

    public NcInterceptionServiceSOAP11BindingStub(Service service) throws AxisFault {
        this.cachedSerClasses = new Vector();
        this.cachedSerQNames = new Vector();
        this.cachedSerFactories = new Vector();
        this.cachedDeserFactories = new Vector();
        if (service == null) {
            super.service = new org.apache.axis.client.Service();
        } else {
            super.service = service;
        }

        ((org.apache.axis.client.Service) super.service).setTypeMappingVersion("1.2");
        Class beansf = BeanSerializerFactory.class;
        Class beandf = BeanDeserializerFactory.class;
        Class enumsf = EnumSerializerFactory.class;
        Class enumdf = EnumDeserializerFactory.class;
        Class arraysf = ArraySerializerFactory.class;
        Class arraydf = ArrayDeserializerFactory.class;
        Class simplesf = SimpleSerializerFactory.class;
        Class simpledf = SimpleDeserializerFactory.class;
        Class simplelistsf = SimpleListSerializerFactory.class;
        Class simplelistdf = SimpleListDeserializerFactory.class;
        QName qName = new QName("http://itf.servlet.yonyou.bs.nc/NcInterceptionRequestData", "NcInterceptionRequestData");
        this.cachedSerQNames.add(qName);
        Class cls = NcInterceptionRequestData.class;
        this.cachedSerClasses.add(cls);
        this.cachedSerFactories.add(beansf);
        this.cachedDeserFactories.add(beandf);
        qName = new QName("http://itf.servlet.yonyou.bs.nc/NcInterceptionRequest", "NcInterceptionRequest");
        this.cachedSerQNames.add(qName);
        cls = NcInterceptionRequest.class;
        this.cachedSerClasses.add(cls);
        this.cachedSerFactories.add(beansf);
        this.cachedDeserFactories.add(beandf);
        qName = new QName("http://itf.servlet.yonyou.bs.nc/NcInterceptionResult", "NcInterceptionResult");
        this.cachedSerQNames.add(qName);
        cls = NcInterceptionResult.class;
        this.cachedSerClasses.add(cls);
        this.cachedSerFactories.add(beansf);
        this.cachedDeserFactories.add(beandf);
    }

    protected Call createCall() throws RemoteException {
        try {
            Call _call = super._createCall();
            if (super.maintainSessionSet) {
                _call.setMaintainSession(super.maintainSession);
            }

            if (super.cachedUsername != null) {
                _call.setUsername(super.cachedUsername);
            }

            if (super.cachedPassword != null) {
                _call.setPassword(super.cachedPassword);
            }

            if (super.cachedEndpoint != null) {
                _call.setTargetEndpointAddress(super.cachedEndpoint);
            }

            if (super.cachedTimeout != null) {
                _call.setTimeout(super.cachedTimeout);
            }

            if (super.cachedPortName != null) {
                _call.setPortName(super.cachedPortName);
            }

            Enumeration keys = super.cachedProperties.keys();

            while (keys.hasMoreElements()) {
                String key = (String) keys.nextElement();
                _call.setProperty(key, super.cachedProperties.get(key));
            }

            synchronized (this) {
                if (this.firstCall()) {
                    _call.setEncodingStyle((String) null);

                    for (int i = 0; i < this.cachedSerFactories.size(); ++i) {
                        Class cls = (Class) this.cachedSerClasses.get(i);
                        QName qName = (QName) this.cachedSerQNames.get(i);
                        Object x = this.cachedSerFactories.get(i);
                        if (x instanceof Class) {
                            Class sf = (Class) this.cachedSerFactories.get(i);
                            Class df = (Class) this.cachedDeserFactories.get(i);
                            _call.registerTypeMapping(cls, qName, sf, df, false);
                        } else if (x instanceof SerializerFactory) {
                            org.apache.axis.encoding.SerializerFactory sf = (org.apache.axis.encoding.SerializerFactory) this.cachedSerFactories.get(i);
                            DeserializerFactory df = (DeserializerFactory) this.cachedDeserFactories.get(i);
                            _call.registerTypeMapping(cls, qName, sf, df, false);
                        }
                    }
                }
            }

            return _call;
        } catch (Throwable var12) {
            throw new AxisFault("Failure trying to get the Call object", var12);
        }
    }

    public NcInterceptionResult updateBXZT(NcInterceptionRequest ncInterceptionRequest) throws RemoteException {
        BaseBean bb = new BaseBean();
        bb.writeLog("updateBXZT---START");
        if (super.cachedEndpoint == null) {
            throw new NoEndPointException();
        } else {
            Call _call = this.createCall();
            _call.setOperation(_operations[0]);
            _call.setUseSOAPAction(true);
            _call.setSOAPActionURI("urn:updateBXZT");
            _call.setEncodingStyle((String) null);
            _call.setProperty("sendXsiTypes", Boolean.FALSE);
            _call.setProperty("sendMultiRefs", Boolean.FALSE);
            _call.setSOAPVersion(SOAPConstants.SOAP11_CONSTANTS);
            _call.setOperationName(new QName("http://itf.servlet.yonyou.bs.nc/NcInterceptionService", "updateBXZT"));
            this.setRequestHeaders(_call);
            this.setAttachments(_call);

            try {
                bb.writeLog("updateBXZT--- before_call.invoke");
                Object _resp = _call.invoke(new Object[]{ncInterceptionRequest});
                bb.writeLog("updateBXZT--- afert_call.invoke");
                if (_resp instanceof RemoteException) {
                    throw (RemoteException) _resp;
                } else {
                    this.extractAttachments(_call);

                    try {
                        return (NcInterceptionResult) _resp;
                    } catch (Exception var5) {
                        bb.writeLog("NcInterceptionResult  Exception:" + Arrays.toString(var5.getStackTrace()));
                        return (NcInterceptionResult) JavaUtils.convert(_resp, NcInterceptionResult.class);
                    }
                }
            } catch (AxisFault var6) {
                bb.writeLog("AxisFault :" + Arrays.toString(var6.getStackTrace()));
                throw var6;
            }
        }
    }

    static {
        _initOperationDesc1();
    }
}
