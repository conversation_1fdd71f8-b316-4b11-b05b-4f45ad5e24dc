//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package weaver.zzn.zc.ws;

import org.apache.axis.description.ElementDesc;
import org.apache.axis.description.TypeDesc;
import org.apache.axis.encoding.Deserializer;
import org.apache.axis.encoding.Serializer;
import org.apache.axis.encoding.ser.BeanDeserializer;
import org.apache.axis.encoding.ser.BeanSerializer;

import javax.xml.namespace.QName;
import java.io.Serializable;

public class NcInterceptionResult implements Serializable {
    private String def5;
    private String def6;
    private String errMsg;
    private String isSuccess;
    private String def4;
    private String def3;
    private String def2;
    private String def1;
    private Object __equalsCalc = null;
    private boolean __hashCodeCalc = false;
    private static TypeDesc typeDesc = new TypeDesc(NcInterceptionResult.class, true);

    public NcInterceptionResult() {
    }

    public NcInterceptionResult(String def5, String def6, String errMsg, String isSuccess, String def4, String def3, String def2, String def1) {
        this.def5 = def5;
        this.def6 = def6;
        this.errMsg = errMsg;
        this.isSuccess = isSuccess;
        this.def4 = def4;
        this.def3 = def3;
        this.def2 = def2;
        this.def1 = def1;
    }

    public String getDef5() {
        return this.def5;
    }

    public void setDef5(String def5) {
        this.def5 = def5;
    }

    public String getDef6() {
        return this.def6;
    }

    public void setDef6(String def6) {
        this.def6 = def6;
    }

    public String getErrMsg() {
        return this.errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public String getIsSuccess() {
        return this.isSuccess;
    }

    public void setIsSuccess(String isSuccess) {
        this.isSuccess = isSuccess;
    }

    public String getDef4() {
        return this.def4;
    }

    public void setDef4(String def4) {
        this.def4 = def4;
    }

    public String getDef3() {
        return this.def3;
    }

    public void setDef3(String def3) {
        this.def3 = def3;
    }

    public String getDef2() {
        return this.def2;
    }

    public void setDef2(String def2) {
        this.def2 = def2;
    }

    public String getDef1() {
        return this.def1;
    }

    public void setDef1(String def1) {
        this.def1 = def1;
    }

    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof NcInterceptionResult)) {
            return false;
        } else {
            NcInterceptionResult other = (NcInterceptionResult) obj;
            if (obj == null) {
                return false;
            } else if (this == obj) {
                return true;
            } else if (this.__equalsCalc != null) {
                return this.__equalsCalc == obj;
            } else {
                this.__equalsCalc = obj;
                boolean _equals = (this.def5 == null && other.getDef5() == null || this.def5 != null && this.def5.equals(other.getDef5())) && (this.def6 == null && other.getDef6() == null || this.def6 != null && this.def6.equals(other.getDef6())) && (this.errMsg == null && other.getErrMsg() == null || this.errMsg != null && this.errMsg.equals(other.getErrMsg())) && (this.isSuccess == null && other.getIsSuccess() == null || this.isSuccess != null && this.isSuccess.equals(other.getIsSuccess())) && (this.def4 == null && other.getDef4() == null || this.def4 != null && this.def4.equals(other.getDef4())) && (this.def3 == null && other.getDef3() == null || this.def3 != null && this.def3.equals(other.getDef3())) && (this.def2 == null && other.getDef2() == null || this.def2 != null && this.def2.equals(other.getDef2())) && (this.def1 == null && other.getDef1() == null || this.def1 != null && this.def1.equals(other.getDef1()));
                this.__equalsCalc = null;
                return _equals;
            }
        }
    }

    public synchronized int hashCode() {
        if (this.__hashCodeCalc) {
            return 0;
        } else {
            this.__hashCodeCalc = true;
            int _hashCode = 1;
            if (this.getDef5() != null) {
                _hashCode += this.getDef5().hashCode();
            }

            if (this.getDef6() != null) {
                _hashCode += this.getDef6().hashCode();
            }

            if (this.getErrMsg() != null) {
                _hashCode += this.getErrMsg().hashCode();
            }

            if (this.getIsSuccess() != null) {
                _hashCode += this.getIsSuccess().hashCode();
            }

            if (this.getDef4() != null) {
                _hashCode += this.getDef4().hashCode();
            }

            if (this.getDef3() != null) {
                _hashCode += this.getDef3().hashCode();
            }

            if (this.getDef2() != null) {
                _hashCode += this.getDef2().hashCode();
            }

            if (this.getDef1() != null) {
                _hashCode += this.getDef1().hashCode();
            }

            this.__hashCodeCalc = false;
            return _hashCode;
        }
    }

    public static TypeDesc getTypeDesc() {
        return typeDesc;
    }

    public static Serializer getSerializer(String mechType, Class _javaType, QName _xmlType) {
        return new BeanSerializer(_javaType, _xmlType, typeDesc);
    }

    public static Deserializer getDeserializer(String mechType, Class _javaType, QName _xmlType) {
        return new BeanDeserializer(_javaType, _xmlType, typeDesc);
    }

    static {
        typeDesc.setXmlType(new QName("http://itf.servlet.yonyou.bs.nc/NcInterceptionResult", "NcInterceptionResult"));
        ElementDesc elemField = new ElementDesc();
        elemField.setFieldName("def5");
        elemField.setXmlName(new QName("", "def5"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def6");
        elemField.setXmlName(new QName("", "def6"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("errMsg");
        elemField.setXmlName(new QName("", "errMsg"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("isSuccess");
        elemField.setXmlName(new QName("", "isSuccess"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def4");
        elemField.setXmlName(new QName("", "def4"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def3");
        elemField.setXmlName(new QName("", "def3"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def2");
        elemField.setXmlName(new QName("", "def2"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def1");
        elemField.setXmlName(new QName("", "def1"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
    }
}
