//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package weaver.zzn.zc.ws;

import javax.xml.rpc.Service;
import javax.xml.rpc.ServiceException;
import java.net.URL;

public interface NcInterceptionService extends Service {
    String getNcInterceptionServiceSOAP11port_httpAddress();

    NcInterceptionServicePortType getNcInterceptionServiceSOAP11port_http() throws ServiceException;

    NcInterceptionServicePortType getNcInterceptionServiceSOAP11port_http(URL var1) throws ServiceException;
}
