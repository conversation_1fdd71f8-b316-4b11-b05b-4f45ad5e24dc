//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package weaver.zzn.zc.ws;

import org.apache.axis.description.ElementDesc;
import org.apache.axis.description.TypeDesc;
import org.apache.axis.encoding.Deserializer;
import org.apache.axis.encoding.Serializer;
import org.apache.axis.encoding.ser.BeanDeserializer;
import org.apache.axis.encoding.ser.BeanSerializer;

import javax.xml.namespace.QName;
import java.io.Serializable;
import java.lang.reflect.Array;
import java.util.Arrays;

public class NcInterceptionRequest implements Serializable {
    private String def12;
    private String def13;
    private String approveResult;
    private String def10;
    private String def11;
    private String approverCode;
    private String def18;
    private String def19;
    private String def16;
    private String def17;
    private String def14;
    private String def15;
    private NcInterceptionRequestData[] datas;
    private String billNo;
    private String approveDate;
    private String def30;
    private String approverName;
    private String def7;
    private String billType;
    private String def8;
    private String def5;
    private String def6;
    private String def9;
    private String def28;
    private String def27;
    private String def26;
    private String def25;
    private String approveNote;
    private String def29;
    private String def20;
    private String def24;
    private String def4;
    private String def23;
    private String def3;
    private String def22;
    private String def2;
    private String def1;
    private String def21;
    private Object __equalsCalc = null;
    private boolean __hashCodeCalc = false;
    private static TypeDesc typeDesc = new TypeDesc(NcInterceptionRequest.class, true);

    public NcInterceptionRequest() {
    }

    public NcInterceptionRequest(String def12, String def13, String approveResult, String def10, String def11, String approverCode, String def18, String def19, String def16, String def17, String def14, String def15, NcInterceptionRequestData[] datas, String billNo, String approveDate, String def30, String approverName, String def7, String billType, String def8, String def5, String def6, String def9, String def28, String def27, String def26, String def25, String approveNote, String def29, String def20, String def24, String def4, String def23, String def3, String def22, String def2, String def1, String def21) {
        this.def12 = def12;
        this.def13 = def13;
        this.approveResult = approveResult;
        this.def10 = def10;
        this.def11 = def11;
        this.approverCode = approverCode;
        this.def18 = def18;
        this.def19 = def19;
        this.def16 = def16;
        this.def17 = def17;
        this.def14 = def14;
        this.def15 = def15;
        this.datas = datas;
        this.billNo = billNo;
        this.approveDate = approveDate;
        this.def30 = def30;
        this.approverName = approverName;
        this.def7 = def7;
        this.billType = billType;
        this.def8 = def8;
        this.def5 = def5;
        this.def6 = def6;
        this.def9 = def9;
        this.def28 = def28;
        this.def27 = def27;
        this.def26 = def26;
        this.def25 = def25;
        this.approveNote = approveNote;
        this.def29 = def29;
        this.def20 = def20;
        this.def24 = def24;
        this.def4 = def4;
        this.def23 = def23;
        this.def3 = def3;
        this.def22 = def22;
        this.def2 = def2;
        this.def1 = def1;
        this.def21 = def21;
    }

    public String getDef12() {
        return this.def12;
    }

    public void setDef12(String def12) {
        this.def12 = def12;
    }

    public String getDef13() {
        return this.def13;
    }

    public void setDef13(String def13) {
        this.def13 = def13;
    }

    public String getApproveResult() {
        return this.approveResult;
    }

    public void setApproveResult(String approveResult) {
        this.approveResult = approveResult;
    }

    public String getDef10() {
        return this.def10;
    }

    public void setDef10(String def10) {
        this.def10 = def10;
    }

    public String getDef11() {
        return this.def11;
    }

    public void setDef11(String def11) {
        this.def11 = def11;
    }

    public String getApproverCode() {
        return this.approverCode;
    }

    public void setApproverCode(String approverCode) {
        this.approverCode = approverCode;
    }

    public String getDef18() {
        return this.def18;
    }

    public void setDef18(String def18) {
        this.def18 = def18;
    }

    public String getDef19() {
        return this.def19;
    }

    public void setDef19(String def19) {
        this.def19 = def19;
    }

    public String getDef16() {
        return this.def16;
    }

    public void setDef16(String def16) {
        this.def16 = def16;
    }

    public String getDef17() {
        return this.def17;
    }

    public void setDef17(String def17) {
        this.def17 = def17;
    }

    public String getDef14() {
        return this.def14;
    }

    public void setDef14(String def14) {
        this.def14 = def14;
    }

    public String getDef15() {
        return this.def15;
    }

    public void setDef15(String def15) {
        this.def15 = def15;
    }

    public NcInterceptionRequestData[] getDatas() {
        return this.datas;
    }

    public void setDatas(NcInterceptionRequestData[] datas) {
        this.datas = datas;
    }

    public NcInterceptionRequestData getDatas(int i) {
        return this.datas[i];
    }

    public void setDatas(int i, NcInterceptionRequestData _value) {
        this.datas[i] = _value;
    }

    public String getBillNo() {
        return this.billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public String getApproveDate() {
        return this.approveDate;
    }

    public void setApproveDate(String approveDate) {
        this.approveDate = approveDate;
    }

    public String getDef30() {
        return this.def30;
    }

    public void setDef30(String def30) {
        this.def30 = def30;
    }

    public String getApproverName() {
        return this.approverName;
    }

    public void setApproverName(String approverName) {
        this.approverName = approverName;
    }

    public String getDef7() {
        return this.def7;
    }

    public void setDef7(String def7) {
        this.def7 = def7;
    }

    public String getBillType() {
        return this.billType;
    }

    public void setBillType(String billType) {
        this.billType = billType;
    }

    public String getDef8() {
        return this.def8;
    }

    public void setDef8(String def8) {
        this.def8 = def8;
    }

    public String getDef5() {
        return this.def5;
    }

    public void setDef5(String def5) {
        this.def5 = def5;
    }

    public String getDef6() {
        return this.def6;
    }

    public void setDef6(String def6) {
        this.def6 = def6;
    }

    public String getDef9() {
        return this.def9;
    }

    public void setDef9(String def9) {
        this.def9 = def9;
    }

    public String getDef28() {
        return this.def28;
    }

    public void setDef28(String def28) {
        this.def28 = def28;
    }

    public String getDef27() {
        return this.def27;
    }

    public void setDef27(String def27) {
        this.def27 = def27;
    }

    public String getDef26() {
        return this.def26;
    }

    public void setDef26(String def26) {
        this.def26 = def26;
    }

    public String getDef25() {
        return this.def25;
    }

    public void setDef25(String def25) {
        this.def25 = def25;
    }

    public String getApproveNote() {
        return this.approveNote;
    }

    public void setApproveNote(String approveNote) {
        this.approveNote = approveNote;
    }

    public String getDef29() {
        return this.def29;
    }

    public void setDef29(String def29) {
        this.def29 = def29;
    }

    public String getDef20() {
        return this.def20;
    }

    public void setDef20(String def20) {
        this.def20 = def20;
    }

    public String getDef24() {
        return this.def24;
    }

    public void setDef24(String def24) {
        this.def24 = def24;
    }

    public String getDef4() {
        return this.def4;
    }

    public void setDef4(String def4) {
        this.def4 = def4;
    }

    public String getDef23() {
        return this.def23;
    }

    public void setDef23(String def23) {
        this.def23 = def23;
    }

    public String getDef3() {
        return this.def3;
    }

    public void setDef3(String def3) {
        this.def3 = def3;
    }

    public String getDef22() {
        return this.def22;
    }

    public void setDef22(String def22) {
        this.def22 = def22;
    }

    public String getDef2() {
        return this.def2;
    }

    public void setDef2(String def2) {
        this.def2 = def2;
    }

    public String getDef1() {
        return this.def1;
    }

    public void setDef1(String def1) {
        this.def1 = def1;
    }

    public String getDef21() {
        return this.def21;
    }

    public void setDef21(String def21) {
        this.def21 = def21;
    }

    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof NcInterceptionRequest)) {
            return false;
        } else {
            NcInterceptionRequest other = (NcInterceptionRequest) obj;
            if (obj == null) {
                return false;
            } else if (this == obj) {
                return true;
            } else if (this.__equalsCalc != null) {
                return this.__equalsCalc == obj;
            } else {
                this.__equalsCalc = obj;
                boolean _equals = (this.def12 == null && other.getDef12() == null || this.def12 != null && this.def12.equals(other.getDef12())) && (this.def13 == null && other.getDef13() == null || this.def13 != null && this.def13.equals(other.getDef13())) && (this.approveResult == null && other.getApproveResult() == null || this.approveResult != null && this.approveResult.equals(other.getApproveResult())) && (this.def10 == null && other.getDef10() == null || this.def10 != null && this.def10.equals(other.getDef10())) && (this.def11 == null && other.getDef11() == null || this.def11 != null && this.def11.equals(other.getDef11())) && (this.approverCode == null && other.getApproverCode() == null || this.approverCode != null && this.approverCode.equals(other.getApproverCode())) && (this.def18 == null && other.getDef18() == null || this.def18 != null && this.def18.equals(other.getDef18())) && (this.def19 == null && other.getDef19() == null || this.def19 != null && this.def19.equals(other.getDef19())) && (this.def16 == null && other.getDef16() == null || this.def16 != null && this.def16.equals(other.getDef16())) && (this.def17 == null && other.getDef17() == null || this.def17 != null && this.def17.equals(other.getDef17())) && (this.def14 == null && other.getDef14() == null || this.def14 != null && this.def14.equals(other.getDef14())) && (this.def15 == null && other.getDef15() == null || this.def15 != null && this.def15.equals(other.getDef15())) && (this.datas == null && other.getDatas() == null || this.datas != null && Arrays.equals(this.datas, other.getDatas())) && (this.billNo == null && other.getBillNo() == null || this.billNo != null && this.billNo.equals(other.getBillNo())) && (this.approveDate == null && other.getApproveDate() == null || this.approveDate != null && this.approveDate.equals(other.getApproveDate())) && (this.def30 == null && other.getDef30() == null || this.def30 != null && this.def30.equals(other.getDef30())) && (this.approverName == null && other.getApproverName() == null || this.approverName != null && this.approverName.equals(other.getApproverName())) && (this.def7 == null && other.getDef7() == null || this.def7 != null && this.def7.equals(other.getDef7())) && (this.billType == null && other.getBillType() == null || this.billType != null && this.billType.equals(other.getBillType())) && (this.def8 == null && other.getDef8() == null || this.def8 != null && this.def8.equals(other.getDef8())) && (this.def5 == null && other.getDef5() == null || this.def5 != null && this.def5.equals(other.getDef5())) && (this.def6 == null && other.getDef6() == null || this.def6 != null && this.def6.equals(other.getDef6())) && (this.def9 == null && other.getDef9() == null || this.def9 != null && this.def9.equals(other.getDef9())) && (this.def28 == null && other.getDef28() == null || this.def28 != null && this.def28.equals(other.getDef28())) && (this.def27 == null && other.getDef27() == null || this.def27 != null && this.def27.equals(other.getDef27())) && (this.def26 == null && other.getDef26() == null || this.def26 != null && this.def26.equals(other.getDef26())) && (this.def25 == null && other.getDef25() == null || this.def25 != null && this.def25.equals(other.getDef25())) && (this.approveNote == null && other.getApproveNote() == null || this.approveNote != null && this.approveNote.equals(other.getApproveNote())) && (this.def29 == null && other.getDef29() == null || this.def29 != null && this.def29.equals(other.getDef29())) && (this.def20 == null && other.getDef20() == null || this.def20 != null && this.def20.equals(other.getDef20())) && (this.def24 == null && other.getDef24() == null || this.def24 != null && this.def24.equals(other.getDef24())) && (this.def4 == null && other.getDef4() == null || this.def4 != null && this.def4.equals(other.getDef4())) && (this.def23 == null && other.getDef23() == null || this.def23 != null && this.def23.equals(other.getDef23())) && (this.def3 == null && other.getDef3() == null || this.def3 != null && this.def3.equals(other.getDef3())) && (this.def22 == null && other.getDef22() == null || this.def22 != null && this.def22.equals(other.getDef22())) && (this.def2 == null && other.getDef2() == null || this.def2 != null && this.def2.equals(other.getDef2())) && (this.def1 == null && other.getDef1() == null || this.def1 != null && this.def1.equals(other.getDef1())) && (this.def21 == null && other.getDef21() == null || this.def21 != null && this.def21.equals(other.getDef21()));
                this.__equalsCalc = null;
                return _equals;
            }
        }
    }

    public synchronized int hashCode() {
        if (this.__hashCodeCalc) {
            return 0;
        } else {
            this.__hashCodeCalc = true;
            int _hashCode = 1;
            if (this.getDef12() != null) {
                _hashCode += this.getDef12().hashCode();
            }

            if (this.getDef13() != null) {
                _hashCode += this.getDef13().hashCode();
            }

            if (this.getApproveResult() != null) {
                _hashCode += this.getApproveResult().hashCode();
            }

            if (this.getDef10() != null) {
                _hashCode += this.getDef10().hashCode();
            }

            if (this.getDef11() != null) {
                _hashCode += this.getDef11().hashCode();
            }

            if (this.getApproverCode() != null) {
                _hashCode += this.getApproverCode().hashCode();
            }

            if (this.getDef18() != null) {
                _hashCode += this.getDef18().hashCode();
            }

            if (this.getDef19() != null) {
                _hashCode += this.getDef19().hashCode();
            }

            if (this.getDef16() != null) {
                _hashCode += this.getDef16().hashCode();
            }

            if (this.getDef17() != null) {
                _hashCode += this.getDef17().hashCode();
            }

            if (this.getDef14() != null) {
                _hashCode += this.getDef14().hashCode();
            }

            if (this.getDef15() != null) {
                _hashCode += this.getDef15().hashCode();
            }

            if (this.getDatas() != null) {
                for (int i = 0; i < Array.getLength(this.getDatas()); ++i) {
                    Object obj = Array.get(this.getDatas(), i);
                    if (obj != null && !obj.getClass().isArray()) {
                        _hashCode += obj.hashCode();
                    }
                }
            }

            if (this.getBillNo() != null) {
                _hashCode += this.getBillNo().hashCode();
            }

            if (this.getApproveDate() != null) {
                _hashCode += this.getApproveDate().hashCode();
            }

            if (this.getDef30() != null) {
                _hashCode += this.getDef30().hashCode();
            }

            if (this.getApproverName() != null) {
                _hashCode += this.getApproverName().hashCode();
            }

            if (this.getDef7() != null) {
                _hashCode += this.getDef7().hashCode();
            }

            if (this.getBillType() != null) {
                _hashCode += this.getBillType().hashCode();
            }

            if (this.getDef8() != null) {
                _hashCode += this.getDef8().hashCode();
            }

            if (this.getDef5() != null) {
                _hashCode += this.getDef5().hashCode();
            }

            if (this.getDef6() != null) {
                _hashCode += this.getDef6().hashCode();
            }

            if (this.getDef9() != null) {
                _hashCode += this.getDef9().hashCode();
            }

            if (this.getDef28() != null) {
                _hashCode += this.getDef28().hashCode();
            }

            if (this.getDef27() != null) {
                _hashCode += this.getDef27().hashCode();
            }

            if (this.getDef26() != null) {
                _hashCode += this.getDef26().hashCode();
            }

            if (this.getDef25() != null) {
                _hashCode += this.getDef25().hashCode();
            }

            if (this.getApproveNote() != null) {
                _hashCode += this.getApproveNote().hashCode();
            }

            if (this.getDef29() != null) {
                _hashCode += this.getDef29().hashCode();
            }

            if (this.getDef20() != null) {
                _hashCode += this.getDef20().hashCode();
            }

            if (this.getDef24() != null) {
                _hashCode += this.getDef24().hashCode();
            }

            if (this.getDef4() != null) {
                _hashCode += this.getDef4().hashCode();
            }

            if (this.getDef23() != null) {
                _hashCode += this.getDef23().hashCode();
            }

            if (this.getDef3() != null) {
                _hashCode += this.getDef3().hashCode();
            }

            if (this.getDef22() != null) {
                _hashCode += this.getDef22().hashCode();
            }

            if (this.getDef2() != null) {
                _hashCode += this.getDef2().hashCode();
            }

            if (this.getDef1() != null) {
                _hashCode += this.getDef1().hashCode();
            }

            if (this.getDef21() != null) {
                _hashCode += this.getDef21().hashCode();
            }

            this.__hashCodeCalc = false;
            return _hashCode;
        }
    }

    public static TypeDesc getTypeDesc() {
        return typeDesc;
    }

    public static Serializer getSerializer(String mechType, Class _javaType, QName _xmlType) {
        return new BeanSerializer(_javaType, _xmlType, typeDesc);
    }

    public static Deserializer getDeserializer(String mechType, Class _javaType, QName _xmlType) {
        return new BeanDeserializer(_javaType, _xmlType, typeDesc);
    }

    static {
        typeDesc.setXmlType(new QName("http://itf.servlet.yonyou.bs.nc/NcInterceptionRequest", "NcInterceptionRequest"));
        ElementDesc elemField = new ElementDesc();
        elemField.setFieldName("def12");
        elemField.setXmlName(new QName("", "def12"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def13");
        elemField.setXmlName(new QName("", "def13"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("approveResult");
        elemField.setXmlName(new QName("", "approveResult"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def10");
        elemField.setXmlName(new QName("", "def10"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def11");
        elemField.setXmlName(new QName("", "def11"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("approverCode");
        elemField.setXmlName(new QName("", "approverCode"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def18");
        elemField.setXmlName(new QName("", "def18"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def19");
        elemField.setXmlName(new QName("", "def19"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def16");
        elemField.setXmlName(new QName("", "def16"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def17");
        elemField.setXmlName(new QName("", "def17"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def14");
        elemField.setXmlName(new QName("", "def14"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def15");
        elemField.setXmlName(new QName("", "def15"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("datas");
        elemField.setXmlName(new QName("", "datas"));
        elemField.setXmlType(new QName("http://itf.servlet.yonyou.bs.nc/NcInterceptionRequestData", "NcInterceptionRequestData"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        elemField.setMaxOccursUnbounded(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("billNo");
        elemField.setXmlName(new QName("", "billNo"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("approveDate");
        elemField.setXmlName(new QName("", "approveDate"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def30");
        elemField.setXmlName(new QName("", "def30"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("approverName");
        elemField.setXmlName(new QName("", "approverName"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def7");
        elemField.setXmlName(new QName("", "def7"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("billType");
        elemField.setXmlName(new QName("", "billType"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def8");
        elemField.setXmlName(new QName("", "def8"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def5");
        elemField.setXmlName(new QName("", "def5"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def6");
        elemField.setXmlName(new QName("", "def6"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def9");
        elemField.setXmlName(new QName("", "def9"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def28");
        elemField.setXmlName(new QName("", "def28"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def27");
        elemField.setXmlName(new QName("", "def27"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def26");
        elemField.setXmlName(new QName("", "def26"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def25");
        elemField.setXmlName(new QName("", "def25"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("approveNote");
        elemField.setXmlName(new QName("", "approveNote"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def29");
        elemField.setXmlName(new QName("", "def29"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def20");
        elemField.setXmlName(new QName("", "def20"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def24");
        elemField.setXmlName(new QName("", "def24"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def4");
        elemField.setXmlName(new QName("", "def4"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def23");
        elemField.setXmlName(new QName("", "def23"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def3");
        elemField.setXmlName(new QName("", "def3"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def22");
        elemField.setXmlName(new QName("", "def22"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def2");
        elemField.setXmlName(new QName("", "def2"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def1");
        elemField.setXmlName(new QName("", "def1"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new ElementDesc();
        elemField.setFieldName("def21");
        elemField.setXmlName(new QName("", "def21"));
        elemField.setXmlType(new QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
    }
}
