//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package weaver.zzn.zc;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.GCONST;
import weaver.general.TimeUtil;
import weaver.general.Util;

import java.io.*;
import java.util.HashMap;
import java.util.Map;

public class ToolUtil extends BaseBean {
    boolean isDebug = false;
    private int logLevel = -1;

    public ToolUtil() {
        String isopen = this.getSystemParamValue("Debug_Mode");
        this.logLevel = Util.getIntValue(this.getSystemParamValue("Logger_Level"), -1);
        if ("1".equals(isopen)) {
            this.isDebug = true;
        }

    }

    public Map<String, String> getSystemParamValueMap(String likestr) {
        return this.getSystemParamList(likestr);
    }

    public Map<String, String> getAllSystemParamValue() {
        return this.getSystemParamList("");
    }

    private Map<String, String> getSystemParamList(String likestr) {
        Map<String, String> param_map = new HashMap();
        String select_sql = "select uuid,paramvalue from uf_systemconfig";
        RecordSet rs = new RecordSet();
        if (!"".equals(likestr)) {
            select_sql = select_sql + " where uuid like '%" + likestr + "%'";
        }

        if (rs.execute(select_sql)) {
            while (rs.next()) {
                String uuid = Util.null2String(rs.getString(1));
                String paramvalue = Util.null2String(rs.getString(2));
                param_map.put(uuid, paramvalue);
            }
        }

        return param_map;
    }

    public String getSystemParamValue(String uuid) {
        String paramvalue = "";
        if (!"".equals(uuid)) {
            String select_sql = "select paramvalue from uf_systemconfig where uuid = '" + uuid + "'";
            RecordSet rs = new RecordSet();
            rs.execute(select_sql);
            if (rs.next()) {
                paramvalue = Util.null2String(rs.getString(1));
            }
        }

        return paramvalue;
    }

    public String getValueByChangeRule(String cus_sql, String value) {
        return this.getValueByChangeRule(cus_sql, value, "");
    }

    public String getValueByChangeRule(String cus_sql, String value, String requestid) {
        String endValue = "";
        cus_sql = cus_sql.replace("&nbsp;", " ");
        String sqlString = cus_sql.replace("{?requestid}", requestid);
        sqlString = sqlString.replace("?", value);
        RecordSet rs = new RecordSet();
        if (rs.execute(sqlString)) {
            rs.next();
            endValue = Util.null2String(rs.getString(1));
        }

        return endValue;
    }

    public String getValueByChangeRule(String cus_sql, String value, String requestid, int detailKeyvalue) {
        String endValue = "";
        (new BaseBean()).writeLog("\ncus_sql=" + cus_sql + "  value=" + value + "  requestid=" + requestid);
        cus_sql = cus_sql.replace("&nbsp;", " ");
        cus_sql = cus_sql.replace("{?dt.id}", String.valueOf(detailKeyvalue));
        String sqlString = cus_sql.replace("{?requestid}", requestid);
        sqlString = sqlString.replace("?", value);
        if (detailKeyvalue > 0) {
            this.writeLog("执行的转换后的SQL:[" + sqlString + "]");
        }

        RecordSet rs = new RecordSet();
        if (rs.execute(sqlString)) {
            rs.next();
            endValue = Util.null2String(rs.getString(1));
        }

        return endValue;
    }

    public String getValueByChangeRule_SingleParam(String cus_sql, String value) {
        String endValue = "";
        cus_sql = cus_sql.replace("&nbsp;", " ");
        RecordSet rs = new RecordSet();
        if (rs.executeQuery(cus_sql, new Object[]{value})) {
            rs.next();
            endValue = Util.null2String(rs.getString(1));
        }

        return endValue;
    }

    public String getFieldNameByFieldid(int fieldid) {
        return fieldid > 0 ? this.getFieldNameByFieldid(String.valueOf(fieldid)) : "";
    }

    public String getFieldNameByFieldid(String fieldid) {
        String fieldname = "";
        if (!"".equals(fieldid)) {
            if (fieldid.startsWith(",")) {
                fieldid = fieldid.substring(1);
            }

            if (fieldid.endsWith(",")) {
                fieldid = fieldid.substring(0, fieldid.length() - 1);
            }

            String select_sql = "select fieldname from workflow_billfield where id in (" + fieldid + ")";
            RecordSet rs = new RecordSet();
            if (rs.execute(select_sql)) {
                while (rs.next()) {
                    fieldname = fieldname + "," + Util.null2String(rs.getString(1));
                }
            }
        }

        if (fieldname.startsWith(",")) {
            fieldname = fieldname.substring(1);
        }

        return fieldname;
    }

    public void writeDebuggerLog(String className, String logstr) {
        if (this.logLevel >= 0) {
            this.writeNewLog(className, logstr);
        }

    }

    public void writeWarningLog(String className, String logstr) {
        if (this.logLevel >= 1) {
            this.writeNewLog(className, logstr);
        }

    }

    public void writeErrorLog(String className, String logstr) {
        if (this.logLevel >= 2) {
            this.writeNewLog(className, logstr);
        }

    }

    public void writeDebugLog(Object logstr) {
        if (this.isDebug) {
            this.writeLog(logstr.toString());
        }

    }

    public void writeNewDebuggerLog(Object o, Object logstr) {
        if (this.isDebug) {
            this.writeNewLog(o.toString(), logstr.toString());
        }

    }

    public void writeNewLog(String o, String s) {
        try {
            String filename = "cus_" + TimeUtil.getCurrentDateString() + "_ecology.log";
            String folder = GCONST.getRootPath() + "log" + File.separatorChar + "cus";
            File f = new File(folder);
            if (!f.exists()) {
                f.mkdirs();
            }

            f = new File(folder + File.separatorChar + filename);
            if (!f.exists()) {
                f.createNewFile();
            }

            BufferedWriter out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(f, true)));
            out.write("[" + o.getClass() + "][" + TimeUtil.getCurrentTimeString() + "]:" + s + "\r\n");
            out.close();
        } catch (IOException var7) {
            var7.printStackTrace();
            this.writeDebugLog("创建日志文件存在异常:[" + var7.getMessage() + "/" + var7.toString() + "]");
        }

    }

    public boolean isDebug() {
        return this.isDebug;
    }

    public void setDebug(boolean isDebug) {
        this.isDebug = isDebug;
    }
}
