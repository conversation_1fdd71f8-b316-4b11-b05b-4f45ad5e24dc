<%@ page import="java.util.*" %>
<%@ page import="weaver.toolbox.json.*"%>
<%@ page import="weaver.general.Util" %>
<%@ page import="weaver.toolbox.db.recordset.QueryUtil" %>

<%
    response.setContentType("application/json;charset=UTF-8");

    String billId = Util.null2String(request.getParameter("billid")).trim();

    String userNames   = "";
    String qryUserIds  = "SELECT cydx FROM uf_sws_nbcy_dt3 WHERE mainid="+billId;
    String userIds     = QueryUtil.doQueryMergedData(qryUserIds,"uf_sws_nbcy_dt3","cydx");
    String qryUserInfo = "SELECT id,lastname FROM hrmresource WHERE id IN ("+userIds+")";
    JSONArray rtnArray = QueryUtil.doQuery(qryUserInfo,"hrmresource","id,lastname");
    userIds            = "";
    for(int i=0; i<rtnArray.size(); i++){
        JSONObject rtnObj   = rtnArray.getJSONObject(i);
        String     id       = rtnObj.getStr("id");
        String     lastname = rtnObj.getStr("lastname");

        userIds   += "," + id;
        userNames += "," + lastname;
    }

    if(!userIds.equals("")){
        userIds = userIds.substring(1);
    }

    if(!userNames.equals("")){
        userNames = userNames.substring(1);
    }

    Map result = new HashMap();
    result.put("res", true);
    result.put("userIds", userIds);
    result.put("userNames", userNames);
    JSONObject jo = JSONUtil.parseObj(result);
    out.println(jo);
%>