<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="utf-8"%>
<%@ page import="net.sf.json.JSONObject"%>
<%@ page import="java.util.HashMap" %>
<%@ page import="java.util.Map" %>
<%@ page import="weaver.general.Util" %>
<%@ page import="weaver.toolbox.db.recordset.ExecuteUtil" %>

<%
    response.setContentType("application/json;charset=UTF-8");

    //明细表3的id(主表id_明细表id) 需处理
    String idsStr   = Util.null2String(request.getParameter("ids")).trim();
    String ids      = "";
    String[] idList = idsStr.split(",");
    for(int l=0; l<idList.length; l++){
        ids += "," + idList[l].split("_")[1];
    }
    if(ids.length() > 0){
        ids = ids.substring(1);
    }

    String  sql = "DELETE FROM uf_sws_nbcy_dt3 WHERE id IN ("+ids+")";
    boolean res = ExecuteUtil.executeSql(sql);

    Map result = new HashMap();
    result.put("res", res);
    JSONObject jo = JSONObject.fromObject(result);
    out.println(jo);
%>

