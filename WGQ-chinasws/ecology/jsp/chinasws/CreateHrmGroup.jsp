<%@ page import="java.util.Map" %>
<%@ page import="java.util.HashMap" %>
<%@ page import="java.util.ArrayList" %>
<%@ page import="com.weaver.formmodel.mobile.manager.MobileUserInit" %>
<%@ page import="weaver.general.Util" %>
<%@ page import="weaver.hrm.User" %>
<%@ page import="weaver.hrm.HrmUserVarify" %>
<%@ page import="weaver.toolbox.db.recordset.QueryUtil" %>
<%@ page import="weaver.toolbox.db.recordset.ExecuteUtil" %>
<%@ page import="weaver.toolbox.json.JSONUtil" %>
<%@ page import="weaver.toolbox.json.JSONObject" %>
<%@ page import="weaver.conn.RecordSetTrans" %>
<%@ page import="weaver.toolbox.json.JSONArray" %>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />
<jsp:useBean id="log" class="weaver.general.BaseBean" scope="page" />

<%!
    public boolean executeBatchSqlWithTrans(ArrayList sqlArray){
        boolean res;

        RecordSetTrans rst = new RecordSetTrans();
        rst.setAutoCommit(false);

        try{
            for (int i = 0; i < sqlArray.size(); i++) {
                rst.execute(sqlArray.get(i).toString());
            }

            rst.commit();
            res = true;
        }catch (Exception e){
            rst.rollback();
            res = false;
        }

        return res;
    }
%>

<%
    response.setContentType("application/json;charset=UTF-8");

    User user = HrmUserVarify.getUser(request, response);
    if(user == null){
        user = MobileUserInit.getUser(request, response);
    }
    int userId = user.getUID();

    boolean res = true;
    String  msg = "";

    String groupName  = Util.null2String(request.getParameter("groupname")).trim();
    String billid     = Util.null2String(request.getParameter("billid")).trim();

    String qryUserIds       = "SELECT cydx FROM uf_sws_nbcy_dt3 a JOIN hrmresource b ON a.cydx=b.id WHERE a.mainid="+billid+" ORDER BY b.dsporder,b.workcode";
    JSONArray userIdArray   = QueryUtil.doQuery(qryUserIds,"uf_sws_nbcy_dt3","cydx");

    String chkSql = "SELECT * FROM hrmgroup WHERE name='"+groupName+"' AND type=0 AND owner="+userId;
    rs.execute(chkSql);
    int count = rs.getCounts();

    if(count > 0){
        res = false;
        msg = "已存在相同的组名";
    }else{
        String insertGroup   = "INSERT INTO hrmgroup(name,type,owner) VALUES('"+groupName+"',0,"+userId+")";
        boolean g = ExecuteUtil.executeSql(insertGroup);
        if(!g){
            res = false;
            msg = "创建组时出错";
        }else{
            ArrayList insertMembersList = new ArrayList();
            String groupId           = QueryUtil.doQueryFieldValue(chkSql,"id");
            for(int i=0; i<userIdArray.size(); i++){
                JSONObject  userObj  = userIdArray.getJSONObject(i);
                String uid           = userObj.getStr("cydx");
                String insertMembers = "INSERT INTO hrmgroupmembers(groupid,userid,usertype,dsporder) VALUES("+groupId+","+uid+",0,"+i+")";
                insertMembersList.add(insertMembers);
            }
            boolean m = executeBatchSqlWithTrans(insertMembersList);
            if(!m){
                res = false;
                msg = "新增组成员时出错";
            }
        }
    }

    Map result = new HashMap();
    result.put("res", res);
    result.put("msg", msg);
    JSONObject jo = JSONUtil.parseObj(result);
    out.println(jo);
%>