<%@ page import="java.util.*" %>
<%@ page import="weaver.toolbox.json.*"%>
<%@ page import="weaver.general.*" %>
<%@ page import="weaver.hrm.User" %>
<%@ page import="weaver.hrm.HrmUserVarify" %>
<%@ page import="weaver.toolbox.db.recordset.QueryUtil" %>

<%
    response.setContentType("application/json;charset=UTF-8");

    //明细表3的id(主表id_明细表id) 需处理
    String idsStr   = Util.null2String(request.getParameter("ids")).trim();
    String ids      = "";
    String[] idList = idsStr.split(",");
    for(int l=0; l<idList.length; l++){
        ids += "," + idList[l].split("_")[1];
    }
    if(ids.length() > 0){
        ids = ids.substring(1);
    }

    User   user          = HrmUserVarify.getUser(request, response);
    String currentUserId = user.getUID() + "";

    //发起人
    String qryFqr = "SELECT fqr FROM uf_sws_nbcy a JOIN uf_sws_nbcy_dt3 b ON a.id=b.mainid WHERE b.id="+ids.split(",")[0];
    String fqr    = QueryUtil.doQueryFieldValue(qryFqr,"fqr");

    String    qry      = "SELECT * FROM uf_sws_nbcy_dt3 WHERE id IN ("+ids+")";
    JSONArray rtnArray = QueryUtil.doQuery(qry,"uf_sws_nbcy_dt3","zt,tjczr");

    boolean canDel = true;
    for(int i=0; i<rtnArray.size(); i++){
        JSONObject rtnObj = rtnArray.getJSONObject(i);
        String zt    = rtnObj.getStr("zt");
        String tjczr = rtnObj.getStr("tjczr");

        //未确认过的和本人创建的可以删除
        if(zt.equals("2") || (!currentUserId.equals(fqr) && !currentUserId.equals(tjczr))){
            canDel = false;
        }
    }

    Map result = new HashMap();
    result.put("res", canDel);
    JSONObject jo = JSONUtil.parseObj(result);
    out.println(jo);
%>