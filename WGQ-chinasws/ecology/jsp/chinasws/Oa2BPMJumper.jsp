<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/systeminfo/init_wev8.jsp"%>
<script src="md5.min.js?_v=20221001_01"></script>
<%
    String userId = user.getUID() + "";
%>

<html>
<head>
    <title>OA跳转到BPM</title>
    <script type="text/javascript">
        function jump(){
            var userid = "<%=userId %>";
            $.ajax({
                async:false,
                url:"/jsp/chinasws/GetWorkcode.jsp",
                type: "GET",
                data : {id:userid},
                success: function (data) {
                    var username = data.workcode;
                    //console.log("username => ", username);
                    var timestamp = Date.parse(new Date());
                    //console.log("timestamp => ", timestamp);
                    var sharekey = "20200301";
                    //console.log("sharekey => ", sharekey);
                    var md5token = md5(username + sharekey + timestamp);
                    //console.log("md5token => ", md5token);
                    var qsdata = {
                        'action':'login',
                        'loginmode':'web',
                        'logintype':'0',
                        'username':username,
                        'from':'portal',
                        'timestamp':timestamp,
                        'token':md5token
                    }

                    // $.ajax({
                    //     async:false,
                    //     url:"http://************/ssoserver/login",
                    //     type: "GET",
                    //     dataType: 'jsonp',
                    //     jsonp: 'callback',
                    //     data : $.param(qsdata,true),
                    //     success: function (json) {
                    //         //console.log(json);
                    //         //console.log("当前登录用户：",json.username);
                    //         if(json.stat == 0){
                    //             //console.log("验证通过");
                    //             window.location.href = "http://************/ssoserver/bssso.do?appName=SWS-BPM";
                    //         }
                    //         else if(json.stat == 21){
                    //             console.log("无效token");
                    //         }
                    //         else if(json.stat == 22){
                    //             console.log("token过期");
                    //         }
                    //     }
                    // });

                }
            });
        }
    </script>
</head>
<body onload="jump();">
</body>
</html>
