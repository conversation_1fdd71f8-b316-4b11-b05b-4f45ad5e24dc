<%@ page import="net.sf.json.JSONObject"%>
<%@ page import="weaver.general.Util" %>
<%@ page import="java.util.HashMap" %>
<%@ page import="java.util.Map" %>
<%@ page import="weaver.formmode.setup.ModeRightInfo" %>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="utf-8"%>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />

<%
    response.setContentType("application/json;charset=UTF-8");
    String qrnr =Util.null2String(request.getParameter("qrnr")).trim();
    String zt =Util.null2String(request.getParameter("zt")).trim();
    String kfsj =Util.null2String(request.getParameter("kfsj")).trim();
    String qrrq =Util.null2String(request.getParameter("qrrq")).trim();
    String qrsj =Util.null2String(request.getParameter("qrsj")).trim();
    String cydx =Util.null2String(request.getParameter("cydx")).trim();
    String sjid =Util.null2String(request.getParameter("sjid")).trim();

    boolean res = false;
    String sql="update uf_sws_nbcy_dt3 set qrnr='"+qrnr+"',zt='"+zt+"',kfsj='"+kfsj+"',qrrq1='"+qrrq+"',qrsj1='"+qrsj+"' where cydx='"+cydx+"' and mainid='"+sjid+"'";
    boolean execute = rs.execute(sql);
    sql="select id from uf_sws_nbcy_dt3 where mainid='"+sjid+"' and cydx='"+cydx+"'";
    rs.execute(sql);
    String billid="";
    if(rs.next()){
        billid= Util.null2String(rs.getString("id"));
    }

   //权限重构
    sql ="select modedatacreater,formmodeid from uf_sws_nbcy where id="+sjid;
    rs.execute(sql);
    if(rs.next()){
        int formmodeid=rs.getInt("formmodeid");
        int modedatacreater=rs.getInt("modedatacreater");
        int id=Integer.parseInt(sjid);
        ModeRightInfo ModeRightInfo = new ModeRightInfo();
        ModeRightInfo.rebuildModeDataShareByEdit(modedatacreater, formmodeid, id);
    }



    if(execute){
        res=true;
    }
    Map result = new HashMap();
    result.put("res", res);
    result.put("billid", billid);
    JSONObject jo = JSONObject.fromObject(result);
    out.println(jo);
%>

