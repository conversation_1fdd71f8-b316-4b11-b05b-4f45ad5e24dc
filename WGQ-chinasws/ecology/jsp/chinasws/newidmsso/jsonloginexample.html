
<%@ page contentType="text/html; charset=gb2312"%>

<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
<script src="jquery-1.7.js"></script>

<title>utrust json登录示例</title>
</head>
<script type="text/javascript">

var tgc = "";
function toSubmit(){
	var username=document.getElementById("username").value;
	var password=document.getElementById("password").value;
	//参数用旧版本的即可
	var qsdata={'action':'login',
			'loginmode':'web',
			'logintype':'0',
			'username':username,
		    'from':'portal',
		    'password':password,
			'timestamp':'12324234243243535',
			'md5':'9eb02002e46c777e48ace548f5db53cd'
			}
	$.ajax({
	   async:false,
	   url:"http://*************:8088/ssoserver/ignore/jsonloginbytoken",
	   type: "GET",
	   dataType: 'jsonp',
	   jsonp: 'callback',
	   data : $.param(qsdata,true),
	   success: function (json) {
		  if(json.stat==0){
			  tgc = json.tgc;
				alert("验证通过"+tgc);
		  }
		  else if(json.stat==1){
			 alert("用户名或密码错误");
		  } 
		   else if(json.stat==2){
			 alert("验证方式错误");
		  } 
		   else if(json.stat==3){
			 alert("账号已经过期，您不能再登录");
		  }
		  else if(json.stat==4){
			 alert("登录错误,您的账号为离职或被锁定状态");
		  }
		   else if(json.stat==5){
			 alert("密码重试次数过多");
		  }
			else {
			 alert("登录错误，错误代码:"+json.stat);
		  }
	   }
	});
}

function tosingo(){
	var appCode=document.getElementById("appCode").value;
	window.open("http://*************/ssologinweb/resLogin.html?gatewayurl=http://*************:8082&appCode="+appCode+"&tgc="+tgc);
	
}
</script>
<body>
<table>
	<tr>
		<td colspan="2">
		<div id="div"></div>
		</td>
	</tr>
	<tr>
		<td>用户名</td>
		<td><input id="username" type="text" name="username" value=""/></td>
	</tr>
	<tr>
		<td>密码</td>
		<td><input id="password" type="password" name="password" value=""></td>
	</tr>
	<tr>
		<td>资源</td>
		<td><input id="appCode" type="appCode" name="appCode" value=""></td>
	</tr>
	<button type="button" onclick="toSubmit()" >认证</button>
	<button type="button" onclick="tosingo()" >单点</button>
</table>
</body>
</html>