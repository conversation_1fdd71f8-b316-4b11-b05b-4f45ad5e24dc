<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/systeminfo/init_wev8.jsp"%>
<jsp:useBean id="log" class="weaver.general.BaseBean" scope="page" />
<script src="../md5.min.js?_v=20221001_01"></script>
<%
    String userId    = user.getUID() + "";
    String appCode   = Util.null2String(request.getParameter("appCode"));
    String idmssoip1 = log.getPropValue("idm", "idmssoip1");
    String idmssoip2 = log.getPropValue("idm", "idmssoip2");
    String idmssoip3 = log.getPropValue("idm", "idmssoip3");
%>

<html>
<head>
    <title>OA跳转到BPM</title>
    <script type="text/javascript">
        function jump(){
            var userid    = "<%=userId %>";
            var appCode   = "<%=appCode %>";
            var idmssoip1 = "<%=idmssoip1 %>";
            var idmssoip2 = "<%=idmssoip2 %>";
            var idmssoip3 = "<%=idmssoip3 %>";
            $.ajax({
                async:false,
                url:"/jsp/chinasws/GetWorkcode.jsp",
                type: "GET",
                data : {id:userid},
                success: function (data) {
                    var username = data.workcode;
                    //console.log("username => ", username);
                    var timestamp = Date.parse(new Date());
                    //console.log("timestamp => ", timestamp);
                    var sharekey = "20200301";
                    //console.log("sharekey => ", sharekey);
                    var md5token = md5(username + sharekey + timestamp);
                    //console.log("md5token => ", md5token);
                    var qsdata = {
                        'action':'login',
                        'loginmode':'web',
                        'logintype':'0',
                        'username':username,
                        'from':'portal',
                        'timestamp':timestamp,
                        'token':md5token
                    }

                    $.ajax({
                        async:false,
                        url:"http://" + idmssoip1 + "/ssoserver/ignore/jsonloginbytoken",
                        type: "GET",
                        dataType: 'jsonp',
                        jsonp: 'callback',
                        data : $.param(qsdata,true),
                        success: function (json) {
                            //console.log(json);
                            //console.log("当前登录用户：",json.username);
                            if(json.stat==0){
                                // alert("验证通过"+tgc);
                                window.location.href = "http://" + idmssoip2 + "/ssologinweb/resLogin.html?gatewayurl=http://" + idmssoip3 + "&appCode="+appCode+"&tgc="+json.tgc;
                            }
                            else if(json.stat==1){
                                top.Dialog.alert("用户名或密码错误");
                            }
                            else if(json.stat==2){
                                top.Dialog.alert("验证方式错误");
                            }
                            else if(json.stat==3){
                                top.Dialog.alert("账号已经过期，您不能再登录");
                            }
                            else if(json.stat==4){
                                top.Dialog.alert("登录错误,您的账号为离职或被锁定状态");
                            }
                            else if(json.stat==5){
                                top.Dialog.alert("密码重试次数过多");
                            }
                            else {
                                top.Dialog.alert("登录错误，错误代码:"+json.stat);
                            }
                        }
                    });

                }
            });
        }
    </script>
</head>
<body onload="jump();">
</body>
</html>
