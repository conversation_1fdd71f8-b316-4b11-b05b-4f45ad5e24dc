<%@ page import="java.util.*" %>
<%@ page import="weaver.hrm.User" %>
<%@ page import="weaver.hrm.HrmUserVarify" %>
<%@ page import="weaver.general.Util" %>
<%@ page import="weaver.toolbox.json.*"%>
<%@ page import="weaver.toolbox.db.recordset.QueryUtil" %>
<%@ page import="weaver.toolbox.core.collection.CollectionUtil" %>
<%@ page import="weaver.toolbox.db.recordset.ExecuteUtil" %>
<%@ page import="weaver.formmode.setup.ModeRightInfo" %>
<jsp:useBean id="ResourceComInfo" class="weaver.hrm.resource.ResourceComInfo" scope="page" />
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />

<%
    response.setContentType("application/json;charset=UTF-8");

    String billId    = Util.null2String(request.getParameter("billid")).trim();
    String cydxs_all = Util.null2String(request.getParameter("cydxs")).trim();
    String fqr       = Util.null2String(request.getParameter("fqr")).trim();

    User   user   = HrmUserVarify.getUser(request,response);
    String userId = user.getUID()+"";

    //如果当前用户不是发起人，则需要加上"添加操作人"
    String tjczr = fqr.equals(userId) ? "" : userId;

    //去重,只插入不在明细3中的人员
    String qryExistedUserIds = "SELECT cydx FROM uf_sws_nbcy_dt3 WHERE mainid="+billId + " AND cydx IN ("+cydxs_all+")";
    String cydxs_existed     = QueryUtil.doQueryMergedData(qryExistedUserIds,"uf_sws_nbcy_dt3","cydx");

    //所有选择的传阅对象
    ArrayList<String> list_all      = new ArrayList<>(Arrays.asList(cydxs_all.split(",")));
    //已经存在的传阅对象
    ArrayList<String> list2_existed = new ArrayList<>(Arrays.asList(cydxs_existed.split(",")));

    //新增的传阅对象
    ArrayList<String> sqlList  = new ArrayList<>();
    ArrayList<String> list_new = (ArrayList<String>)CollectionUtil.subtract(list_all,list2_existed);
    for(int i=0 ;i<list_new.size(); i++){
        String cydx_new = list_new.get(i);
        String rypx = "";
        String rygh = "";
        JSONObject userObj = QueryUtil.doQuerySingleLine("hrmresource","id,dsporder,workcode","id",cydx_new);
        if(userObj.containsKey("id")){
            rypx = userObj.getStr("dsporder");
            rygh = userObj.getStr("workcode");
        }

        String sql;
        if(tjczr.equals("")){
            sql = "INSERT INTO uf_sws_nbcy_dt3(mainid,cydx,rypx,rygh,zt) VALUES("+billId+","+cydx_new+",'"+rypx+"','"+rygh+"',0)";
        }else{
            sql = "INSERT INTO uf_sws_nbcy_dt3(mainid,cydx,tjczr,rypx,rygh,zt) VALUES("+billId+","+cydx_new+","+tjczr+",'"+rypx+"','"+rygh+"',0)";
        }
        sqlList.add(sql);
    }

    boolean res = ExecuteUtil.executeBatchSqlWithTrans(sqlList);
    if(res){
        //权限重构
        String sql ="select modedatacreater,formmodeid from uf_sws_nbcy where id="+billId;
        //权限重构
        rs.execute(sql);
        if(rs.next()){
            int formmodeid      = rs.getInt("formmodeid");
            int modedatacreater = rs.getInt("modedatacreater");
            int id              = Integer.parseInt(billId);
            ModeRightInfo ModeRightInfo = new ModeRightInfo();
            ModeRightInfo.rebuildModeDataShareByEdit(modedatacreater, formmodeid, id);
        }
    }

    Map result = new HashMap();
    result.put("res", res);
    JSONObject jo = JSONUtil.parseObj(result);
    out.println(jo);
%>