<%@ page import="java.util.*" %>
<%@ page import="weaver.toolbox.json.*"%>
<%@ page import="weaver.general.Util" %>
<%@ page import="weaver.toolbox.db.recordset.QueryUtil" %>

<%
    response.setContentType("application/json;charset=UTF-8");

    String billId = Util.null2String(request.getParameter("billid")).trim();

    String qryUserIds = "SELECT cydx FROM uf_sws_nbcy_dt3 WHERE (zt=1 OR zt=2) AND mainid="+billId;
    int    count      = QueryUtil.doQueryCount(qryUserIds);

    Map result = new HashMap();
    result.put("res", count>0);
    JSONObject jo = JSONUtil.parseObj(result);
    out.println(jo);
%>