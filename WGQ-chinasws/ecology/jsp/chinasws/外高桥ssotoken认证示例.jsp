
<%@ page contentType="text/html; charset=gb2312"%>

<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
<script src="jquery-1.7.js"></script>

<title>utrust json登录示例</title>
</head>
<script type="text/javascript">


function toSubmit(){
	var username=document.getElementById("username").value;
	var password=document.getElementById("password").value;
	var timestamp='<%=System.currentTimeMillis()%>';
	var username=document.getElementById("username").value;
	var sharekey="20200301";
	var qsdata={'action':'login',
			'loginmode':'web',
			'logintype':'0',
			'username':username,
		    'from':'portal',
		    'timestamp':timestamp,
			'token':md5(sharekey+username+timestamp),
			}
	$.ajax({
		   async:false,
		   url:"http://127.0.0.1:8080/ssoserver/login",
		   type: "GET",
		   dataType: 'jsonp',
		   jsonp: 'callback',
		   data : $.param(qsdata,true),
		   success: function (json) {
			  alert("当前登录用户："+json.username);
			  if(json.stat==0){
				 alert("验证通过");
			  }
			  else if(json.stat==21){
				 alert("无效token");
			  } 
			   else if(json.stat==22){
				 alert("token过期");
			  }
		   }
		});
}

</script>
<body>
<table>
	<tr>
		<td colspan="2">
		<div id="div"></div>
		</td>
	</tr>
	<tr>
		<td>用户名</td>
		<td><input id="username" type="text" name="username" value=""/></td>
	</tr>
	<tr>
		<td>密码</td>
		<td><input id="password" type="password" name="password" value=""></td>
	</tr>
</table>
</body>
</html>