<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />
<%@ page import="java.util.HashMap" %>
<%@ page import="java.util.Map" %>
<%@ page import="weaver.toolbox.json.*"%>
<%@ page import="weaver.general.Util" %>

<%
    response.setContentType("application/json;charset=UTF-8");
    String id    = Util.null2String(request.getParameter("id")).trim();
    //回收标识
    String hsbs  = Util.null2String(request.getParameter("hsbs")).trim();
    //删除标识
    String scbs  = Util.null2String(request.getParameter("scbs")).trim();
    //收藏标识
    String socbs = Util.null2String(request.getParameter("socbs")).trim();

    boolean res = false;

    if(!hsbs.equals("") || !scbs.equals("") || !socbs.equals("")){
        String hsbsStr = !hsbs.equals("") ? (",hsbs='"+hsbs+"'") : "";
        String scbsStr = !scbs.equals("") ? (",scbs='"+scbs+"'") : "";
        String socbsStr = !socbs.equals("") ? (",socbs='"+socbs+"'") : "";
        String setStr = hsbsStr + scbsStr + socbsStr;
        setStr = setStr.substring(1);

        String sql = "update uf_sws_nbcy set " + setStr + " where id=" + id;
        res = rs.execute(sql);
    }



    Map result = new HashMap();
    result.put("res", res);
    JSONObject jo = JSONUtil.parseObj(result);
    out.println(jo);
%>