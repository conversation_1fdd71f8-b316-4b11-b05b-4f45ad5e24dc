<%@ page import="net.sf.json.JSONObject"%>
<%@ page import="weaver.general.Util" %>
<%@ page import="java.util.HashMap" %>
<%@ page import="java.util.Map" %>
<%@ page import="weaver.general.BaseBean" %>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="utf-8"%>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />

<%
    response.setContentType("application/json;charset=UTF-8");
    String type =Util.null2String(request.getParameter("type")).trim();
    String ids =Util.null2String(request.getParameter("ids")).trim();
    String mainid =Util.null2String(request.getParameter("mainid")).trim();
    boolean res = false;
    String sql="";
    BaseBean baseBean = new BaseBean();
baseBean.writeLog("czb--type========"+type);
    if("row1".equals(type)){
        sql = "delete from  uf_sws_nbcy_dt1  where mainid="+mainid+" and id in("+ids+")";
    }else if("row3".equals(type)){
        sql = "delete from  uf_sws_nbcy_dt3 where mainid="+mainid+" and cydx in("+ids+")";
    }
    baseBean.writeLog("uf_sws_nbcy_dt1========"+sql);
    boolean execute = rs.execute(sql);
    if(execute){
        res=true;
    }
    Map result = new HashMap();
    result.put("res", res);
    JSONObject jo = JSONObject.fromObject(result);
    out.println(jo);
%>

