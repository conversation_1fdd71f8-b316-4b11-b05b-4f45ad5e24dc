<%@ page import="weaver.toolbox.json.*"%>
<%@ page import="java.util.*" %>
<%@ page import="weaver.general.*" %>
<%@ page import="weaver.toolbox.db.recordset.HrmUtil" %>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />

<%
response.setContentType("application/json;charset=UTF-8");
String ids = Util.null2String(request.getParameter("ids")).trim();

String userNames = HrmUtil.getUserLastNamesByUserIds(ids);

Map result = new HashMap();
result.put("userNames", userNames);
JSONObject jo = JSONUtil.parseObj(result);
out.println(jo);
%>