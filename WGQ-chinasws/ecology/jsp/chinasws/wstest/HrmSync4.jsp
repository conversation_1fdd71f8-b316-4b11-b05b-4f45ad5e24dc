<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="utf-8"%>
<%@ page import="net.sf.json.JSONObject"%>
<%@ page import="java.util.HashMap" %>
<%@ page import="java.util.Map" %>
<%@ page import="chinasws.webservices.test.HrmServiceCustLocator" %>
<%@ page import="chinasws.webservices.test.HrmServiceCustHttpBindingStub" %>
<%@ page import="weaver.toolbox.core.io.FileUtil" %>

<%
    response.setContentType("application/json;charset=UTF-8");

    HrmServiceCustLocator locator = new HrmServiceCustLocator();
    HrmServiceCustHttpBindingStub stub = (HrmServiceCustHttpBindingStub)
            locator.getPort(HrmServiceCustHttpBindingStub.class);

    String xml = FileUtil.readUtf8String("/usr/weaver/ecology/jsp/chinasws/wstest/xml/04changeworkcode.xml");

    String res = stub.synHrmResource("",xml);

    Map result = new HashMap();
    result.put("res", res);
    JSONObject jo = JSONObject.fromObject(result);
    out.println(jo);
%>

