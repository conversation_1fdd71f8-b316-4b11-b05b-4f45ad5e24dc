
<%@ Page Language="C#" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<script runat="server">
    protected void Page_Load(object sender, EventArgs e)
    {
        iOfficeService.ioCom.Emp emp = new iOfficeService.ioCom.Emp();
        userId.Text = emp.LoginID;
    }
</script>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Untitled Document</title>
    <script type="text/javascript">
        function jump() {
            var argsArray = new Array();
            var empid = document.getElementById("userId");

            argsArray.push("userCd:" + userId.innerHTML);
            argsArray.push("sys:oa");

            document.getElementById("__EVENTVALIDATION").value = argsArray;
            WebForm_DoPostBackWithOptions(new WebForm_PostBackOptions(IbSubmit, "", false, "","http://***********:8084/informationLeading/loginNoPwd", false, true))
        }

    </script>
</head>
<body onload="jump();">
    <form id="form1" runat="server">
        <div>
            <div style="display: none;">
                <asp:Label ID="userId" runat="server" value="" />
            </div>
            <asp:LinkButton ID="IbSubmit" runat="server" PostBackUrl="http://***********:8084/informationLeading/">
            </asp:LinkButton>
        </div>
    </form>
</body>
</html>
