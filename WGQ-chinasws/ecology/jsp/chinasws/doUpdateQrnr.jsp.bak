<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="utf-8"%>
<%@ page import="net.sf.json.JSONObject"%>
<%@ page import="java.util.HashMap" %>
<%@ page import="java.util.Map" %>
<%@ page import="weaver.general.Util" %>
<%@ page import="weaver.toolbox.db.recordset.ExecuteUtil" %>
<%@ page import="weaver.toolbox.db.recordset.HrmUtil" %>
<%@ page import="weaver.hrm.User" %>
<%@ page import="weaver.hrm.HrmUserVarify" %>
<%@ page import="weaver.toolbox.db.recordset.QueryUtil" %>

<%
    response.setContentType("application/json;charset=UTF-8");

    User   user        = HrmUserVarify.getUser(request, response);
    String allAccounts = HrmUtil.getUserAllAccountsByUserId(user.getUID()+"");

    String billId = Util.null2String(request.getParameter("billid")).trim();
    String qrnr   = Util.null2String(request.getParameter("qrnr")).trim();
    String qrrq   = Util.null2String(request.getParameter("qrrq")).trim();
    String qrsj   = Util.null2String(request.getParameter("qrsj")).trim();

    String qry     = "SELECT qrnr,qrrq1,qrsj1 FROM uf_sws_nbcy_dt3 WHERE cydx IN ("+allAccounts+") AND mainid="+billId;
    weaver.toolbox.json.JSONObject rtn = QueryUtil.doQuerySingleLine(qry,"","qrnr,qrrq1,qrsj1");
    String oldQrnr = rtn.getStr("qrnr");
    String qrrq1   = rtn.getStr("qrrq1");
    String qrsj1   = rtn.getStr("qrsj1");
    String newQr;
    if(oldQrnr.equals("")){
        newQr = qrnr;
    }else{
        newQr = oldQrnr+"("+qrrq1+" "+qrsj1.substring(0,5)+")"+"<br>"+"----------------------------------------"+"<br>"+qrnr;
    }

    String  sql = "UPDATE uf_sws_nbcy_dt3 SET qrnr='"+newQr+"',zt=2,qrrq1='"+qrrq+"',qrsj1='"+qrsj+"' " +
                  "WHERE cydx IN ("+allAccounts+") AND mainid="+billId;
    boolean res = ExecuteUtil.executeSql(sql);

    Map result = new HashMap();
    result.put("res", res);
    JSONObject jo = JSONObject.fromObject(result);
    out.println(jo);
%>

