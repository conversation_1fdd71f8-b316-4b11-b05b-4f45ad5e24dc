<%@ page import="weaver.toolbox.json.*"%>
<%@ page import="java.util.*" %>
<%@ page import="weaver.general.*" %>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />

<%
response.setContentType("application/json;charset=UTF-8");
String hrmResourceID = Util.null2String(request.getParameter("HrmResourceID")).trim();

int billId = 0;
rs.execute("select id from uf_gbllb where xm="+hrmResourceID);
if (rs.next()) {
    billId = Util.getIntValue(rs.getString("id"), 0);
}

Map result = new HashMap();
result.put("billId", billId);
JSONObject jo = JSONUtil.parseObj(result);
out.println(jo);
%>