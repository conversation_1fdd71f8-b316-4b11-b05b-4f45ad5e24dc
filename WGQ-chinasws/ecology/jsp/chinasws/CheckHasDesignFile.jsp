<%@ page import="weaver.toolbox.json.*"%>
<%@ page import="java.util.*" %>
<%@ page import="weaver.general.*" %>
<%@ page import="weaver.hrm.User" %>
<%@ page import="weaver.hrm.HrmUserVarify" %>
<%@ page import="com.weaver.formmodel.mobile.manager.MobileUserInit" %>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />

<%
response.setContentType("application/json;charset=UTF-8");
String ids = Util.null2String(request.getParameter("ids")).trim();

User user = HrmUserVarify.getUser(request, response);
if(user == null){
    user = MobileUserInit.getUser(request, response);
}
int userId = user.getUID();

String sql = "select id from DocDetail where id in ("+ids+") and isdomaindoc=1";
rs.execute(sql);
int count = rs.getCounts();

boolean res = count > 0;

// 0：设计域，1：it域
// 先判断人力自定义字段field4，如果能找到0或者1，就是这个
// 如果是null，则继续找这个人所在部门的自定义字段szy
// 如果还是null，则找分部的szy，分部的肯定会维护
String qry1 = "select field4 from cus_fielddata where scopeid=-1 and id="+userId;
rs.execute(qry1);
if(rs.next()){
    String field4 = Util.null2String(rs.getString("field4"));
    if(field4.equals("0")){
        res = false;
    }

    if(field4.equals("")){
        String qry2 = "select szy from hrmresource a join hrmdepartmentdefined b on a.departmentid=b.deptid where a.id="+userId;
        rs.execute(qry2);
        if(rs.next()){
            String szy_dept = Util.null2String(rs.getString("szy"));
            if(szy_dept.equals("0")){
                res = false;
            }

            if(szy_dept.equals("")){
                String qry3 = "select szy from hrmresource a join hrmsubcompanydefined b on a.subcompanyid1=b.subcomid where a.id="+userId;
                rs.execute(qry3);
                if(rs.next()){
                    String szy_subcom = Util.null2String(rs.getString("szy"));
                    if(szy_subcom.equals("0")){
                        res = false;
                    }
                }
            }
        }
    }
}

Map result = new HashMap();
result.put("res", res);
JSONObject jo = JSONUtil.parseObj(result);
out.println(jo);
%>