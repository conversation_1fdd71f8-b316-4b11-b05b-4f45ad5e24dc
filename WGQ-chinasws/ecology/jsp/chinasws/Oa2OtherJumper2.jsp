<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ page import="weaver.toolbox.db.recordset.QueryUtil" %>
<%@ page import="weaver.toolbox.json.JSONObject" %>
<%@ include file="/systeminfo/init_wev8.jsp"%>

<%
    String workCode = "11493";
    String urldz = "http://*************:8107/sws-production-preparation/cp/oaPost";
    String dlmcs="userId";
%>

<html>
<head>
    <title>OA跳转到其他业务系统</title>
    <script type="text/javascript">
        function jump(){
            var argsArray = new Array();
            argsArray.push("<%=dlmcs%>:" + $("#userId").val());
            argsArray.push("sys:oa");
            document.getElementById("__EVENTVALIDATION").value = argsArray;

            var vPostForm = document.getElementById("PostForm");
            vPostForm.submit();
        }
    </script>
</head>
<body onload="jump();">
<form id="PostForm" name="PostForm" action="<%=urldz%>" method="POST">
    <div>
        <div style="display: hidden;">
            <input type="hidden" id="userId" name="userId" value="<%=workCode%>">
            <input type="hidden" id="__EVENTVALIDATION" name="__EVENTVALIDATION">
        </div>
    </div>
</form>
</body>
</html>
