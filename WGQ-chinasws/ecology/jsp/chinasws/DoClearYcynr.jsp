<%@ page import="weaver.toolbox.json.*"%>
<%@ page import="java.util.*" %>
<%@ page import="weaver.general.*" %>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />

<%
response.setContentType("application/json;charset=UTF-8");
BaseBean base = new BaseBean();
String billid = Util.null2String(request.getParameter("billid")).trim();

String sql = "update uf_sws_nbcy set ycynr=null where id="+billid;
boolean res = rs.execute(sql);

Map result = new HashMap();
result.put("res", res);
JSONObject jo = JSONUtil.parseObj(result);
out.println(jo);
%>