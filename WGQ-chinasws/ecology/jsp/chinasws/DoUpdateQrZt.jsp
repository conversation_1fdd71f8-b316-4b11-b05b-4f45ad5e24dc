<%@ page import="weaver.toolbox.json.*"%>
<%@ page import="weaver.toolbox.core.date.DateUtil" %>
<%@ page import="java.util.*" %>
<%@ page import="weaver.general.*" %>
<%@ page import="weaver.hrm.User" %>
<%@ page import="weaver.hrm.HrmUserVarify" %>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />

<%
response.setContentType("application/json;charset=UTF-8");
BaseBean base = new BaseBean();
String id = Util.null2String(request.getParameter("id")).trim();

User user = HrmUserVarify.getUser(request, response);
String now = DateUtil.now();

String sql = "UPDATE uf_sws_nbcy_dt3 SET zt=2, kfsj='"+now+"' WHERE mainid="+id+" AND cydx="+user.getUID();
base.writeLog("QrZt sql ===> ", sql);
boolean res = rs.execute(sql);

Map result = new HashMap();
result.put("res", res);
JSONObject jo = JSONUtil.parseObj(result);
out.println(jo);
%>