<%@ page contentType="text/html;charset=GB2312"%>
<%@ page import="weaver.toolbox.json.*"%>
<%@ page import="java.util.*" %>
<%@ page import="weaver.general.*" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%@ page import="weaver.conn.RecordSet" %>
<%@ page import="weaver.hrm.User" %>
<%@ page import="weaver.hrm.HrmUserVarify" %>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />

<%!
private void doUpdateDqqrry(String billid, User user){
    BaseBean log = new BaseBean();

    Date date = new Date();
    SimpleDateFormat dsdf = new SimpleDateFormat();
    dsdf.applyPattern("yyyy-MM-dd HH:mm:ss");
    String datetimeStr = dsdf.format(date);

    //cyxx==7 ����ȷ��
    String cyxx = "";
    RecordSet rs =new RecordSet();
    String sql = "select * from uf_sws_nbcy where id="+billid;
    rs.execute(sql);
    if(rs.next()){
        cyxx = Util.null2String(rs.getString("cyxx"));
    }

    boolean isNext = false;
    RecordSet rs1 = new RecordSet();
    String sql1 = "select * from uf_sws_nbcy_dt3 where mainid="+billid+" order by id";
    rs1.execute(sql1);
    while(rs1.next()){
        log.writeLog("isNext ===> ",isNext);

        String id = Util.null2String(rs1.getString("id"));
        int cydx = rs1.getInt("cydx");
        //String dqqrry = Util.null2String(rs1.getString("dqqrry"));

        if(user.getUID() == cydx){
            if(cyxx.contains("7")){
                RecordSet rs11 = new RecordSet();
                String sqlUpdate1 = "update uf_sws_nbcy_dt3 set dqqrry=0 where id="+id;
                isNext = true;

                log.writeLog("sqlUpdate1:",sqlUpdate1);
                rs11.execute(sqlUpdate1);
            }
        }else{
            //������һ��ȷ����
            if(isNext && cyxx.contains("7")){
                RecordSet rs12 =new RecordSet();
                String sqlUpdate2 = "update uf_sws_nbcy_dt3 set dqqrry=1 where id="+id;
                log.writeLog("sqlUpdate2 xxxx:",sqlUpdate2);
                rs12.execute(sqlUpdate2);
                isNext = false;

                break;
            }
        }
    }
}
%>

<%
//response.setContentType("application/json;charset=UTF-8");

User user = HrmUserVarify.getUser(request,response);

BaseBean base = new BaseBean();
String id = Util.null2String(request.getParameter("id")).trim();
String zt = Util.null2String(request.getParameter("zt")).trim();
String kfsj = Util.null2String(request.getParameter("kfsj")).trim();
String sfyy = Util.null2String(request.getParameter("sfyy")).trim();

//2020-06-13 ����ֱ��д�����������ٸ���
String yy = sfyy.contains("6") ? "已阅" : "";
String sql = "update uf_sws_nbcy_dt3 set zt='"+zt+"', kfsj='"+kfsj+"', qrnr='"+yy+"' where id="+id;
base.writeLog("sql ===> ", sql);
boolean res = rs.execute(sql);

String billid = "";
String qry = "select mainid from uf_sws_nbcy_dt3 where id = " + id;
rs.execute(qry);
if(rs.next()){
    billid = Util.null2String(rs.getString("mainid"));
}

//���µ�ǰȷ����Ա
if(sfyy.contains("6")){
  doUpdateDqqrry(billid, user);
}

Map result = new HashMap();
result.put("res", res);
JSONObject jo = JSONUtil.parseObj(result);
out.println(jo);
%>