<%@ page import="java.util.*" %>
<%@ page import="weaver.general.Util" %>
<%@ page import="weaver.toolbox.json.*"%>
<%@ page import="weaver.toolbox.db.recordset.QueryUtil" %>
<%@ page import="weaver.hrm.User" %>
<%@ page import="weaver.hrm.HrmUserVarify" %>

<%
    response.setContentType("application/json;charset=UTF-8");

    User user = HrmUserVarify.getUser(request, response);

    String billId = Util.null2String(request.getParameter("billid")).trim();

    String qry = "SELECT qrnr FROM uf_sws_nbcy_dt3 WHERE cydx="+user.getUID()+" AND mainid="+billId;
    String qr  = QueryUtil.doQueryFieldValue(qry,"qrnr");

    Map result = new HashMap();
    result.put("qr", qr);
    JSONObject jo = JSONUtil.parseObj(result);
    out.println(jo);
%>