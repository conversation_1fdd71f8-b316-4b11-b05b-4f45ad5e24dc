<%@ page import="weaver.toolbox.json.*"%>
<%@ page import="java.util.*" %>
<%@ page import="weaver.general.*" %>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />

<%
response.setContentType("application/json;charset=UTF-8");
String id = Util.null2String(request.getParameter("id")).trim();

String workcode = "";
String sql = "select workcode from HrmResource where id=" + id;
rs.execute(sql);
if(rs.next()){
    workcode = Util.null2String(rs.getString("workcode"));
}

Map result = new HashMap();
result.put("workcode", workcode);
JSONObject jo = JSONUtil.parseObj(result);
out.println(jo);
%>