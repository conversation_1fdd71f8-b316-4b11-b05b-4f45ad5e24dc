<%@ page import="net.sf.json.JSONObject"%>
<%@ page import="weaver.general.Util" %>
<%@ page import="java.util.HashMap" %>
<%@ page import="java.util.Map" %>
<%@ page import="weaver.formmode.setup.ModeRightInfo" %>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="utf-8"%>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />

<%
    response.setContentType("application/json;charset=UTF-8");
    String cydx =Util.null2String(request.getParameter("cydx")).trim();
    String tjczr =Util.null2String(request.getParameter("tjczr")).trim();
    String mainid =Util.null2String(request.getParameter("mainid")).trim();

    boolean res = false;
    String sql="select dsporder,workcode from hrmresource where id="+cydx;
    rs.execute(sql);
    String rypx="";
    String rygh="";
    if(rs.next()){
        rypx=Util.null2String(rs.getString("dsporder"));
        rygh=Util.null2String(rs.getString("workcode"));
    }
    sql = "insert into uf_sws_nbcy_dt3(mainid,cydx,tjczr,rypx,rygh,zt) values('"+mainid+"','"+cydx+"','"+tjczr+"','"+rypx+"','"+rygh+"',0)";

    boolean execute = rs.execute(sql);
    if(execute){
        res=true;
    }
    //权限重构
    sql ="select modedatacreater,formmodeid from uf_sws_nbcy where id="+mainid;
    //权限重构
    rs.execute(sql);
    if(rs.next()){
        int formmodeid=rs.getInt("formmodeid");
        int modedatacreater=rs.getInt("modedatacreater");
        int id=Integer.parseInt(mainid);
        ModeRightInfo ModeRightInfo = new ModeRightInfo();
        ModeRightInfo.rebuildModeDataShareByEdit(modedatacreater, formmodeid, id);
    }


    Map result = new HashMap();
    result.put("res", res);
    JSONObject jo = JSONObject.fromObject(result);
    out.println(jo);
%>

