<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ page import="weaver.toolbox.db.recordset.QueryUtil" %>
<%@ page import="weaver.toolbox.json.JSONObject" %>
<%@ include file="/systeminfo/init_wev8.jsp"%>

<%
    String userId = user.getUID() + "";
    String workCode = QueryUtil.doQueryFieldValue("hrmresource","workcode","id",userId);

    String billid = Util.null2String(request.getParameter("billid")).trim();
    JSONObject rtn = QueryUtil.doQuerySingleLine("uf_dddlpz","dlmcs,urldz","id",billid);
    String dlmcs = rtn.getStr("dlmcs");
    String urldz = rtn.getStr("urldz");
%>

<html>
<head>
    <title>OA跳转到其他业务系统</title>
    <script type="text/javascript">
        function jump(){
            var argsArray = new Array();
            argsArray.push("<%=dlmcs%>:" + $("#userId").val());
            argsArray.push("sys:oa");

            document.getElementById("__EVENTVALIDATION").value = argsArray;

            var vPostForm = document.getElementById("PostForm");
            vPostForm.submit();
        }
    </script>
</head>
<body onload="jump();">
<form id="PostForm" name="PostForm" action="<%=urldz%>" method="POST">
    <div>
        <div style="display: none;">
            <input type="hidden" id="userId" name="userId" value="<%=workCode%>">
            <input type="hidden" id="__EVENTVALIDATION" name="__EVENTVALIDATION">
        </div>
    </div>
</form>
</body>
</html>