<%@ page import="java.util.*" %>
<%@ page import="weaver.toolbox.json.*"%>
<%@ page import="weaver.general.Util" %>
<%@ page import="weaver.toolbox.db.recordset.QueryUtil" %>

<%
    response.setContentType("application/json;charset=UTF-8");

    String billId       = Util.null2String(request.getParameter("billid")).trim();
    String cyxx         = Util.null2String(request.getParameter("cyxx")).trim();
    String allAccounts  = Util.null2String(request.getParameter("allaccounts")).trim();

    String userIds = "";
    if(cyxx.contains("3")){
        JSONArray rtnArray = QueryUtil.doQuery("uf_sws_nbcy_dt3","cydx","mainid",billId);
        for(int i=0; i<rtnArray.size(); i++){
            JSONObject rtnObj = rtnArray.getJSONObject(i);
            String cydx = rtnObj.getStr("cydx");
            if((","+allAccounts+",").contains(","+cydx+",")){
                userIds += "," + cydx;
            }
        }
    }

    if(!userIds.equals("")){
        userIds = userIds.substring(1);
    }

    Map result = new HashMap();
    result.put("userIds", userIds);
    JSONObject jo = JSONUtil.parseObj(result);
    out.println(jo);
%>