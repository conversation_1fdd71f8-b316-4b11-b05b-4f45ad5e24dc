<%@ page import="java.util.*" %>
<%@ page import="com.weaver.formmodel.mobile.manager.MobileUserInit" %>
<%@ page import="weaver.hrm.User" %>
<%@ page import="weaver.toolbox.json.*"%>
<%@ page import="weaver.toolbox.db.recordset.HrmUtil" %>
<jsp:useBean id="log" class="weaver.general.BaseBean" scope="page" />

<%
    response.setContentType("application/json;charset=UTF-8");

    User user = MobileUserInit.getUser(request, response);
    if(user == null){
        return;
    }

    String allAccounts = HrmUtil.getUserAllAccountsByUserId(user.getUID()+"");

    Map result = new HashMap();
    result.put("allAccounts", allAccounts);
    JSONObject jo = JSONUtil.parseObj(result);
    out.println(jo);
%>