<%@ page import="java.util.*" %>
<%@ page import="weaver.general.*" %>
<%@ page import="weaver.hrm.User" %>
<%@ page import="com.weaver.formmodel.mobile.manager.MobileUserInit" %>
<%@ page import="weaver.toolbox.json.*"%>
<%@ page import="weaver.toolbox.core.date.DateUtil" %>
<%@ page import="weaver.toolbox.db.recordset.HrmUtil" %>
<%@ page import="weaver.toolbox.db.recordset.ExecuteUtil" %>
<%@ page import="weaver.toolbox.db.recordset.QueryUtil" %>
<jsp:useBean id="log" class="weaver.general.BaseBean" scope="page" />

<%
    response.setContentType("application/json;charset=UTF-8");

    String id = Util.null2String(request.getParameter("id")).trim();
    String qrnr = Util.null2String(request.getParameter("qrnr")).trim();
    String isFirst = Util.null2String(request.getParameter("isFirst")).trim();

    User user = MobileUserInit.getUser(request, response);
    if(user == null){
        return;
    }
    String allAccounts = HrmUtil.getUserAllAccountsByUserId(user.getUID()+"");
    String today = DateUtil.today();
    String now   = DateUtil.present().substring(0,5);

    String qry     = "SELECT qrnr,qrrq1,qrsj1 FROM uf_sws_nbcy_dt3 WHERE cydx IN ("+allAccounts+") AND mainid="+id;
    weaver.toolbox.json.JSONObject rtn = QueryUtil.doQuerySingleLine(qry,"","qrnr,qrrq1,qrsj1");
    String qrrq1   = rtn.getStr("qrrq1");
    String qrsj1   = rtn.getStr("qrsj1");

    String sql;
    if(isFirst.equals("0")){
        sql = "UPDATE uf_sws_nbcy_dt3 SET zt=2, qrrq1='"+today+"', qrsj1='"+now+"', qrnr='"+qrnr+"' WHERE mainid="+id+" AND cydx IN ("+allAccounts+")";
    }else{
        sql = "UPDATE uf_sws_nbcy_dt3 SET zt=2, qrrq1='"+today+"', qrsj1='"+now+"', qrnr=qrnr || '("+qrrq1+" "+qrsj1.substring(0,5)+")' || '<br>' || '--------------------------------------' || '<br>' || '"+qrnr+"' WHERE mainid="+id+" AND cydx IN ("+allAccounts+")";
    }
    log.writeLog("sql ===> ",sql);
    boolean res = ExecuteUtil.executeSql(sql);

    //2023-06-06添加逻辑，如果此时开封时间没值，则赋值当前时间给开封时间
    sql = "UPDATE uf_sws_nbcy_dt3 set kfsj = '"+now+"' where mainid="+id+" AND kfsj is null AND cydx IN ("+allAccounts+")";
    boolean res2 = ExecuteUtil.executeSql(sql);
    log.writeLog("sql update kfsj ===> ",sql);


    Map result = new HashMap();
    result.put("res", res);
    JSONObject jo = JSONUtil.parseObj(result);
    out.println(jo);
%>