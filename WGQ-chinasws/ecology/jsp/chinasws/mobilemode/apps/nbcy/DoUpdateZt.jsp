<%@ page import="java.util.*" %>
<%@ page import="com.weaver.formmodel.mobile.manager.MobileUserInit" %>
<%@ page import="weaver.toolbox.json.*"%>
<%@ page import="weaver.general.Util" %>
<%@ page import="weaver.hrm.User" %>
<%@ page import="weaver.toolbox.db.recordset.HrmUtil" %>
<%@ page import="weaver.toolbox.db.recordset.ExecuteUtil" %>
<%@ page import="weaver.toolbox.core.date.DateUtil" %>

<%
    response.setContentType("application/json;charset=UTF-8");

    User user = MobileUserInit.getUser(request, response);
    if(user == null){
        return;
    }

    String allAccounts = HrmUtil.getUserAllAccountsByUserId(user.getUID()+"");

    String id   = Util.null2String(request.getParameter("id")).trim();
    String zt   = Util.null2String(request.getParameter("zt")).trim();
    String kfsj = Util.null2String(request.getParameter("kfsj")).trim();
    String sfyy = Util.null2String(request.getParameter("sfyy")).trim();

    String yy = sfyy.contains("6") ? "已阅" : "";
    String sql;
    if(sfyy.contains("6")){
        String today = DateUtil.today();
        String now   = DateUtil.present().substring(0,5);
        sql = "UPDATE uf_sws_nbcy_dt3 SET zt='"+zt+"', kfsj='"+kfsj+"', qrnr='"+yy+"',qrrq1='"+today+"', qrsj1='"+now+"' WHERE mainid="+id +" AND cydx IN ("+allAccounts+")";
    }else{
        sql = "UPDATE uf_sws_nbcy_dt3 SET zt='"+zt+"', kfsj='"+kfsj+"', qrnr='"+yy+"' WHERE mainid="+id +" AND cydx IN ("+allAccounts+")";
    }
    boolean res = ExecuteUtil.executeSql(sql);

    Map result = new HashMap();
    result.put("res", res);
    JSONObject jo = JSONUtil.parseObj(result);
    out.println(jo);
%>