<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />
<%@ page import="java.util.HashMap" %>
<%@ page import="java.util.Map" %>
<%@ page import="java.util.ArrayList" %>
<%@ page import="weaver.general.Util" %>
<%@ page import="weaver.toolbox.json.*"%>
<%@ page import="weaver.hrm.User" %>
<%@ page import="weaver.hrm.HrmUserVarify" %>
<%@ page import="weaver.toolbox.db.recordset.QueryUtil" %>
<%@ page import="weaver.toolbox.db.recordset.HrmUtil" %>
<%@ page import="weaver.toolbox.db.recordset.ExecuteUtil" %>
<%@ page import="com.weaver.formmodel.mobile.manager.MobileUserInit" %>
<jsp:useBean id="log" class="weaver.general.BaseBean" scope="page" />

<%
    response.setContentType("application/json;charset=UTF-8");

    String id    = Util.null2String(request.getParameter("id")).trim();
    //回收标识
    String hsbs  = Util.null2String(request.getParameter("hsbs")).trim();
    //删除标识
    String scbs  = Util.null2String(request.getParameter("scbs")).trim();
    //收藏标识
    String socbs = Util.null2String(request.getParameter("socbs")).trim();

    User user = HrmUserVarify.getUser(request, response);
    if(user == null){
        user = MobileUserInit.getUser(request, response);
    }
    int userId = user.getUID();

    log.writeLog("userId ===> ",userId);

    ArrayList sqlArray = new ArrayList();

    //判断当前用户是否为发起人
    //如果是则更新主表状态；不是则更新明细表3状态
    String fqr = QueryUtil.doQueryFieldValue("uf_sws_nbcy","fqr","id",id);
    log.writeLog("fqr ===> ",fqr);

    if(fqr.equals(userId+"")){

        if(!scbs.equals("")){
            //这个删除针对的是草稿的删除，直接主从全删
            if(scbs.equals("2")){
                String sqlm  = "delete from uf_sws_nbcy     where id    =" + id;
                String sqld1 = "delete from uf_sws_nbcy_dt1 where mainid=" + id;
                String sqld2 = "delete from uf_sws_nbcy_dt3 where mainid=" + id;
                sqlArray.add(sqlm);
                sqlArray.add(sqld1);
                sqlArray.add(sqld2);
            }else{
                String sqlm = "update uf_sws_nbcy set scbs='"+scbs+"' where id=" + id;
                sqlArray.add(sqlm);
            }
        }

        //回收
        if(!hsbs.equals("")){
            String sqlm = "update uf_sws_nbcy set hsbs='"+hsbs+"' where id=" + id;
            sqlArray.add(sqlm);
        }

        //收藏
        if(!socbs.equals("")){
            String sqlm = "update uf_sws_nbcy set socbs='"+socbs+"' where id=" + id;
            sqlArray.add(sqlm);
        }

    }else{

        //回收时候需要处理所有
        if(!hsbs.equals("")){
            String allAccounts = HrmUtil.getUserAllAccountsByUserId(userId+"");
            String sql = "update uf_sws_nbcy_dt3 set hsbs='"+hsbs+"'  where mainid="+id+" and cydx in ("+allAccounts+")";
            sqlArray.add(sql);
        }

        if(!scbs.equals("")){
            String sql = "update uf_sws_nbcy_dt3 set scbs='"+scbs+"'  where mainid="+id+" and cydx="+userId;
            sqlArray.add(sql);
        }

        if(!socbs.equals("")){
            String sql = "update uf_sws_nbcy_dt3 set socbs='"+socbs+"' where mainid="+id+" and cydx="+userId;
            sqlArray.add(sql);
        }

    }

    log.writeLog("sqlArray ===> ",sqlArray);

    boolean res = ExecuteUtil.executeBatchSqlWithTrans(sqlArray);

    log.writeLog("res ===> ",res);

    Map result = new HashMap();
    result.put("res", res);
    JSONObject jo = JSONUtil.parseObj(result);
    out.println(jo);
%>