<%@ page import="java.util.*" %>
<%@ page import="weaver.toolbox.json.*"%>
<%@ page import="weaver.toolbox.db.recordset.HrmUtil" %>
<%@ page import="weaver.hrm.User" %>
<%@ page import="weaver.hrm.HrmUserVarify" %>
<%@ page import="com.weaver.formmodel.mobile.manager.MobileUserInit" %>

<%
    response.setContentType("application/json;charset=UTF-8");
    User user = HrmUserVarify.getUser(request, response);
    if(user == null){
        user = MobileUserInit.getUser(request, response);
    }
    int userId = user.getUID();

    String allAccounts = HrmUtil.getUserAllAccountsByUserId(userId+"");

    Map result = new HashMap();
    result.put("allAccounts", allAccounts);
    JSONObject jo = JSONUtil.parseObj(result);
    out.println(jo);
%>