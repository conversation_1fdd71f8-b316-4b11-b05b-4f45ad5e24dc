<%@ page import="java.util.*" %>
<%@ page import="weaver.toolbox.json.*"%>
<%@ page import="weaver.toolbox.db.recordset.ExecuteUtil" %>
<%@ page import="weaver.toolbox.core.date.DateUtil" %>
<%@ page import="weaver.general.Util" %>

<%
    response.setContentType("application/json;charset=UTF-8");

    String billId      = Util.null2String(request.getParameter("billid")).trim();
    String allAccounts = Util.null2String(request.getParameter("allaccounts")).trim();
    String kfsj        = Util.null2String(request.getParameter("kfsj")).trim();
    String sfyy        = Util.null2String(request.getParameter("sfyy")).trim();

    //开封
    String zt  = "1";
    String yy  = "";
    String sql;
    if(sfyy.contains("6")){
        String today = DateUtil.today();
        String now   = DateUtil.present().substring(0,5);
        //已阅
        zt = "2";
        yy = "已阅";
        sql = "UPDATE uf_sws_nbcy_dt3 SET zt='"+zt+"', kfsj='"+kfsj+"', qrnr='"+yy+"',qrrq1='"+today+"', qrsj1='"+now+"' " +
              "WHERE (zt=0 OR zt IS NULL) AND mainid="+billId+" AND cydx IN ("+allAccounts+")";
    }else{
        sql = "UPDATE uf_sws_nbcy_dt3 SET zt='"+zt+"', kfsj='"+kfsj+"', qrnr='"+yy+"' " +
              "WHERE (zt=0 OR zt IS NULL) AND mainid="+billId+" AND cydx IN ("+allAccounts+")";
    }

    boolean res = ExecuteUtil.executeSql(sql);

    Map result = new HashMap();
    result.put("res", res);
    JSONObject jo = JSONUtil.parseObj(result);
    out.println(jo);
%>