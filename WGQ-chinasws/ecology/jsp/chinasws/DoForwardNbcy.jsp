<%@ page import="java.util.*" %>
<%@ page import="weaver.toolbox.json.*"%>
<%@ page import="weaver.general.*" %>
<%@ page import="weaver.hrm.User" %>
<%@ page import="weaver.hrm.HrmUserVarify" %>
<%@ page import="weaver.toolbox.core.date.DateUtil" %>
<%@ page import="weaver.toolbox.db.recordset.QueryUtil" %>
<%@ page import="weaver.toolbox.modeform.ModeFormUtil" %>
<%@ page import="weaver.formmode.setup.ModeRightInfo" %>
<%@ page import="com.weaver.formmodel.mobile.manager.MobileUserInit" %>
<%@ page import="weaver.toolbox.db.Entity" %>
<%@ page import="weaver.toolbox.core.convert.Convert" %>
<%@ page import="weaver.toolbox.doc.DocUtil" %>
<%@ page import="weaver.toolbox.core.util.StrUtil" %>
<%@ page import="weaver.toolbox.db.sql.SqlBuilder" %>
<%@ page import="weaver.toolbox.db.recordset.ExecuteUtil" %>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />
<jsp:useBean id="log" class="weaver.general.BaseBean" scope="page" />

<%!
    public static int copyModeData(int userId, String fromDataId, String tableName, String copyFields,
                                   String attachmentFields, JSONObject... detailTableInfoArray) {
        BaseBean log = new BaseBean();

        String FORM_MODE_FIELDS = "id,formmodeid,modedatacreater,modedatacreatertype,modedatacreatedate,modedatacreatetime,modeuuid";

        int newModeDataId;

        // 需要复制的字段，有文本字段和附件字段组成；如果不设置字段，则复制该表的所有字段
        String queryFields;

        if (copyFields.equals("")) {
            queryFields = QueryUtil.getTableFields(tableName);
        } else {
            queryFields = copyFields + ",formmodeid,modedatacreater";
        }

        // 被复制的对象
        JSONObject dataObject = QueryUtil.doQuerySingleLine(tableName, queryFields, "id", fromDataId);
        log.writeLog("dataObject ===> ",dataObject);

        // int oldDataCreatorId = dataObject.getInt("modedatacreater");
        Entity data = new Entity();

        data.setTableName(tableName);

        // 遍历所有查询字段
        String[] queryFieldsArray = queryFields.split(",");
        String   formModeId       = "";

        for (int i = 0; i < queryFieldsArray.length; i++) {
            String queryField = queryFieldsArray[i];

            if (queryField.toLowerCase().equals("formmodeid")) {
                formModeId = dataObject.getStr(queryField);
            } else {
                if (!FORM_MODE_FIELDS.contains(queryField.toLowerCase())) {
                    if (attachmentFields.toLowerCase().contains(queryField.toLowerCase())) {

                        // 处理多附件
                        String   newDocIds   = "";
                        String   docIds      = dataObject.getStr(queryField);
                        String[] docIdsArray = docIds.split(",");

                        for (int a = 0; a < docIdsArray.length; a++) {
                            int docId = Convert.toInt(docIdsArray[a], -1);

                            // int newDocId = DocUtil.copyDocWithFile(docId,false,oldDataCreatorId);
                            int newDocId = DocUtil.copyDocWithFile(docId, false, 1);

                            String updateSql = "UPDATE DOCDETAIL SET isdomaindoc=((SELECT isdomaindoc FROM DOCDETAIL WHERE id = "+docId+")) WHERE id = "+newDocId;
                            log.writeLog("isdomaindoc updateSql ===> ",updateSql);
                            ExecuteUtil.executeSql(updateSql);

                            newDocIds += "," + newDocId;
                        }

                        newDocIds = StrUtil.subAfter(newDocIds, ',', false);
                        data.set(queryField, newDocIds);
                    } else {
                        log.writeLog("queryField ===> ",dataObject.getStr(queryField));
                        data.set(queryField, dataObject.getStr(queryField));
                    }
                }
            }
        }

        // 插入建模数据
        newModeDataId = ModeFormUtil.insertModeData(data, Convert.toInt(formModeId, -1), userId);

        // 处理从表信息
        // 从表字段不需要同步
        String detailFields = "id,mainid";

        for (int k = 0; k < detailTableInfoArray.length; k++) {
            JSONObject detailTableInfo        = detailTableInfoArray[k];
            String     detailTableName        = tableName + "_" + detailTableInfo.getStr("detailTableName");
            String     detailCopyFields       = detailTableInfo.getStr("detailCopyFields");
            String     detailAttachmentFields = detailTableInfo.getStr("detailAttachmentFields").toLowerCase();

            // 需要复制的字段，有文本字段和附件字段组成；如果不设置字段，则复制该表的所有字段
            String queryDetailFields;

            if (detailCopyFields.equals("")) {
                queryDetailFields = QueryUtil.getTableFields(detailTableName);
            } else {
                queryDetailFields = detailCopyFields;
            }

            // 被复制的明细数组
            JSONArray detailDataArray = QueryUtil.doQuery(detailTableName, "", "mainid", fromDataId);

            if (detailDataArray.size() > 0) {
                Entity detailData = new Entity();

                detailData.setTableName(detailTableName);

                // mainId
                detailData.set("mainid", "?");

                // 遍历所有明细查询字段
                String[] queryDetailFieldsArray = queryDetailFields.split(",");

                for (int i = 0; i < queryDetailFieldsArray.length; i++) {
                    String queryDetailField = queryDetailFieldsArray[i];

                    if (!detailFields.contains(queryDetailField.toLowerCase())) {
                        detailData.set(queryDetailField, "?");
                    }
                }

                // 生成新增明细表语句
                SqlBuilder insertBuilder = SqlBuilder.create().insert(detailData, true);
                String             insertSql     = insertBuilder.build();
                List<List<Object>> sqlParamsList = new ArrayList<List<Object>>();

                for (int j = 0; j < detailDataArray.size(); j++) {
                    JSONObject   detailDataObj = detailDataArray.getJSONObject(j);
                    List<Object> param         = new ArrayList<Object>();

                    // mainId
                    param.add(newModeDataId);

                    for (int d = 0; d < queryDetailFieldsArray.length; d++) {
                        String queryDetilField = queryDetailFieldsArray[d];

                        if (!detailFields.contains(queryDetilField.toLowerCase())) {
                            if (detailAttachmentFields.contains(queryDetilField.toLowerCase())) {

                                // 处理多附件
                                String   newDocIds   = "";
                                String   docIds      = detailDataObj.getStr(queryDetilField);
                                String[] docIdsArray = docIds.split(",");

                                for (int a = 0; a < docIdsArray.length; a++) {
                                    int docId = Convert.toInt(docIdsArray[a], -1);

                                    // int newDocId = DocUtil.copyDocWithFile(docId,oldDataCreatorId);
                                    int newDocId = DocUtil.copyDocWithFile(docId, false, 1);

                                    String updateSql = "UPDATE DOCDETAIL SET isdomaindoc=((SELECT isdomaindoc FROM DOCDETAIL WHERE id = "+docId+")) WHERE id = "+newDocId;
                                    log.writeLog("isdomaindoc updateSql ===> ",updateSql);
                                    ExecuteUtil.executeSql(updateSql);

                                    newDocIds += "," + newDocId;
                                }

                                newDocIds = StrUtil.subAfter(newDocIds, ',', false);
                                param.add(newDocIds);
                            } else {
                                param.add(detailDataObj.getStr(queryDetilField));
                            }
                        }
                    }

                    sqlParamsList.add(param);
                }

                ExecuteUtil.executeBatchSql(insertSql, sqlParamsList);
            }
        }

        return newModeDataId;
    }
%>

<%
response.setContentType("application/json;charset=UTF-8");
String id = Util.null2String(request.getParameter("id")).trim();

boolean res = false;
int dataId = -1;
//检查这条数据是否可以转发
boolean canForward;
String cyxx = QueryUtil.doQueryFieldValue("uf_sws_nbcy","cyxx","id",id);
if(cyxx.contains("5")){
    canForward = true;
}else{
    canForward = false;
    res = true;
}

if(canForward){
    User user = HrmUserVarify.getUser(request, response);
    log.writeLog("user getUID ===> ",user.getUID());
    log.writeLog("user getUserDepartment ===> ",user.getUserDepartment());
    if(user == null){
        user = MobileUserInit.getUser(request, response);
    }
    int userId = user.getUID();

    //生成一条原传阅数据的副本，这条副本数据作为转发数据里点击看到的原文
    //也就是说，原传阅数据内容改变了，转发的数据看到的原转发数据还是转发时的状态

    JSONObject d1Obj = new JSONObject();
    d1Obj.put("detailTableName","dt1");
    d1Obj.put("detailCopyFields","");
    //因为涉及到文档权限控制，需要保留转发前的文档id，所以附件把id复制过来就好了，不需要额外新建
    d1Obj.put("detailAttachmentFields","fjmc");
//    d1Obj.put("detailAttachmentFields","");

    JSONObject d2Obj = new JSONObject();
    d2Obj.put("detailTableName","dt3");
    d2Obj.put("detailCopyFields","");
    d2Obj.put("detailAttachmentFields","");

    //生成原传阅数据的副本
    int copyDataId = copyModeData(userId,id,"uf_sws_nbcy","cybt,cynr,wcqx,fjxz,cyxx,cycy,fqr,fqsj","",d1Obj,d2Obj);
    String sql1 = "update uf_sws_nbcy set sfbzfcy='1' where id="+copyDataId;
    boolean res1 = rs.execute(sql1);

    //添加共享
    ModeRightInfo moderightinfo = new ModeRightInfo();
    moderightinfo.setNewRight(true);
    moderightinfo.editModeDataShare(userId, 501, copyDataId);

    //生成评论数据的副本
    JSONArray replyArray = QueryUtil.doQuery("uf_reply","id","rqid",id);
    for(int i=0; i<replyArray.size(); i++){
        String replyId = replyArray.getJSONObject(i).getStr("id");
        int newReplyId = copyModeData(userId,replyId,"uf_reply","","rattach");
        String sql11 = "update uf_reply set rqid='"+copyDataId+"' where id="+newReplyId;
        rs.execute(sql11);
    }

    //生成转发数据
    String now = DateUtil.now();
    dataId = copyModeData(userId,id,"uf_sws_nbcy","cybt,cynr,cycy","",d1Obj);
    String updateDt1 = "update uf_sws_nbcy_dt1 set fjscr='"+user.getUID()+"',fjscsj='"+now+"' where mainid="+dataId;
    rs.execute(updateDt1);

    int bm = user.getUserDepartment();
    String today = DateUtil.today();
    String qryZsjbm = "select (select id from hrmdepartment where supdepid=0 start with id=a.id connect by prior supdepid=id) as zsjbm from hrmdepartment a where id="+bm;
    log.writeLog("qryZsjbm ===> ",qryZsjbm);
    String zsjbm = QueryUtil.doQueryFieldValue(qryZsjbm,"zsjbm");
    log.writeLog("zsjbm ===> ",zsjbm);
    String sql2 = "update uf_sws_nbcy set hsbs='0',modedatastatus='1',cybt = 'FW:' || cybt,fqr="+userId+",cyxx='4,5',fjxz='2',fqsj='"+today+"',ycynr='"+copyDataId+"',bm='"+bm+"',zsjbm='"+zsjbm+"' where id="+dataId;
    boolean res2 = rs.execute(sql2);

    res = res1 && res2;
}

Map result = new HashMap();
result.put("res", res);
result.put("dataId", dataId);
result.put("canForward", canForward);
JSONObject jo = JSONUtil.parseObj(result);
out.println(jo);
%>