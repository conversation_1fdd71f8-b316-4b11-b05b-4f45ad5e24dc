package wgqzc.workflow;

import weaver.conn.RecordSet;
import weaver.formmode.setup.ModeRightInfo;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.workflow.WorkflowComInfo;

import java.util.HashMap;
import java.util.Map;

public class ProcMeetingAction extends BaseBean implements Action {
    @Override
    public String execute(RequestInfo request) {
        String retStr = Action.SUCCESS;
        RequestManager rm = request.getRequestManager();
        writeLog(this.getClass().getName() + " -- ActionDemo xxxx ");
        try {
            WorkflowComInfo workflowComInfo = new WorkflowComInfo();
            int requestid = Util.getIntValue(request.getRequestid());   //requestid
            int cjr = Util.getIntValue(request.getCreatorid()); //创建人
            String requestname = Util.null2String(request.getRequestManager().getRequestname());    //标题
            int workflowid = Util.getIntValue(request.getWorkflowid()); //流程id
            int formid = Util.getIntValue(workflowComInfo.getFormId("" + workflowid));  //表单id
            String maintablename = "formtable_main_" + (Math.abs(formid));  //表名
            int wfstatus = request.getRequestManager().getCreater(); //（代码执行时）当前节点
            writeLog("requestid = " + requestid);   //统一日志类
            writeLog("workflowid = " + workflowid);
            writeLog("formid = " + formid);
            String sql = "";
            RecordSet rs = new RecordSet(); //数据库操作类
            //--------业务逻辑 BEGIN---------
            //主表字段
            String bt="";
            String sqr="";
            String bzsm="";
            String sqlx="";
            String cx="";
            String mj="";
            String ssbm="";
            String zsjbm="";
            String gxdx="";


            String sqlxString ="";
            String cxString="";
            String mjString="";

            String xgfj="";
            String xgxwjdwd="";
            String fujian="";
             //取主表数据
            Property[] properties = request.getMainTableInfo().getProperty();// 获取表单主字段信息
            for (int i = 0; i < properties.length; i++) {
                String field = properties[i].getName();// 主字段名称
                if("bt".equalsIgnoreCase(field)){
                    bt=Util.null2String(properties[i].getValue());
                }
                if("sqr".equalsIgnoreCase(field)){
                    sqr=Util.null2String(properties[i].getValue());
                }
                if("bzsm".equalsIgnoreCase(field)){
                    bzsm=Util.null2String(properties[i].getValue());
                }
                if("sqlx".equalsIgnoreCase(field)){
                    sqlx=Util.null2String(properties[i].getValue());
                    if("0".equals(sqlx)){
                        sqlxString="专业室输出审批";
                    }else if("1".equals(sqlx)){
                        sqlxString="集中输出审批";
                    }else if("2".equals(sqlx)){
                        sqlxString="切割指令审批";
                    }
                }
                if("cx".equalsIgnoreCase(field)){
                    cx=Util.null2String(properties[i].getValue());
                    if("0".equals(cx)){
                        cxString="民船";
                    }else if("1".equals(cx)){
                        cxString="邮轮";
                    }else if("2".equals(cx)){
                        cxString="海工";
                    }
                }
                if("mj".equalsIgnoreCase(field)){
                    mj=Util.null2String(properties[i].getValue());
                    if("0".equals(mj)){
                        mjString="核心商密";
                    }else if("1".equals(mj)){
                        mjString="普通商密";
                    }else if("2".equals(mj)){
                        mjString="内部资料";
                    }
                }
                if("ssbm".equalsIgnoreCase(field)){
                    ssbm=Util.null2String(properties[i].getValue());
                }
                if("zsjbm".equalsIgnoreCase(field)){
                    zsjbm=Util.null2String(properties[i].getValue());
                }
                if("gxdx".equalsIgnoreCase(field)){
                    gxdx=Util.null2String(properties[i].getValue());
                }
                if("xgfj".equalsIgnoreCase(field)){
                    xgfj=Util.null2String(properties[i].getValue());
                }
                if("xgxwjdwd".equalsIgnoreCase(field)){
                    xgxwjdwd=Util.null2String(properties[i].getValue());
                }

                writeLog("name===========>"+field);
            }
            String cynr="【申请类型】"+sqlxString+" <br/>"+"【船型】"+cxString+" <br/>"+
                    "【密级】"+mjString+" <br/>"+"【用途说明】"+bzsm+"<br/>";
            String formmodeid="501";
            String fjxz="2";
            String cyxx="4,5";
            sql = "insert into uf_sws_nbcy(requestid,formmodeid,modedatacreater,modedatacreatertype,modedatacreatedate," +
                    "modedatacreatetime,modedatastatus,cybt,fqr,fqsj,cynr,fjxz,cyxx,ycsj,bm,zsjbm) values(" +requestid+","+
                    formmodeid+","+sqr+",0,to_char(sysdate,'yyyy-mm-dd'),to_char(sysdate,'hh24:mi:ss'),0,'【文件共享】"+requestname+"','"+sqr+"',to_char(sysdate,'yyyy-mm-dd'),'"
                    +cynr+"','"+ fjxz+"','"+cyxx+"',to_char(sysdate,'hh24:mi'),'"+ssbm+"','"+zsjbm+"')";
            writeLog("into uf_sws_nbcy ------ " + sql);
            rs.execute(sql);

            sql="select id from uf_sws_nbcy where requestid="+requestid;
            writeLog("select sql======"+sql);
            rs.execute(sql);
            String id="";
            if(rs.next()){
                id= rs.getString("id");
            }
            //如果有附件，明细增加一行，如果没有附件，不增加明细
            if(!"".equals(xgfj)){
                fujian=xgfj;
            }else{
                fujian=xgxwjdwd;
            }
            if(!"".equals(fujian)){
                String detailsql="insert into uf_sws_nbcy_dt1(mainid,fjmc,fjscr,fjscsj) values("+id+",'"+fujian+"','"+sqr+"',to_char(sysdate,'yyyy-mm-dd hh24:mi'))";
                rs.execute(detailsql);
                writeLog("insert uf_sws_nbcy_dt1====="+detailsql);
            }

            String[] hrmmembersArray=gxdx.split(",");
            for (int i = 0; i < hrmmembersArray.length; i++) {
                Map<String, String> map = queryPeopInfo(hrmmembersArray[i]);
                String detail3sql="insert into uf_sws_nbcy_dt3(mainid,cydx,rypx,rygh,zt) values("+id+",'"+hrmmembersArray[i]+"','"+map.get("dsporder")+"','"+map.get("workcode")+"','0')";
                rs.execute(detail3sql);
                writeLog("success insert uf_sws_nbcy and uf_sws_nbcy_dt1");
            }
            //权限重构
            //主表权限重构
            String billsql = "select id,modedatacreater,formmodeid from uf_sws_nbcy where requestid='"+requestid+"'";
            rs.execute(billsql);
            while (rs.next()) {
                int billid = Util.getIntValue(rs.getString("id"));
                int modedatacreater = Util.getIntValue(rs.getString("modedatacreater"));
                int modeid = Util.getIntValue(rs.getString("formmodeid"));
                ModeRightInfo ModeRightInfo = new ModeRightInfo();
                ModeRightInfo.rebuildModeDataShareByEdit(modedatacreater, modeid, billid);
            }
            writeLog("success insert into uf_sws_nbcy_dt3 ");
        } catch (Exception ex) {
            // 手动报错处理，挂节点后附加操作，可以阻碍流程提交
            if (rm != null) {
                // rm.setMessage("message 错误");
                rm.setMessagecontent("插入会议建模数据失败, 请联系系统管理员处理."); //设置错误提示，显示在流程表单上部
                retStr = Action.FAILURE_AND_CONTINUE;   //返回此标示可阻止流程提交下去
            }

            writeLog(ex);
        }

        return retStr;
    }
    public static Map<String,String> queryPeopInfo(String peopid){
        RecordSet rs = new RecordSet();
        String sql="select dsporder,workcode,lastname from hrmresource where id="+peopid;
        rs.execute(sql);
        Map<String,String> map=new HashMap();
        if(rs.next()){
            map.put("dsporder",rs.getString("dsporder"));
            map.put("workcode",rs.getString("workcode"));
            map.put("lastname",rs.getString("lastname"));
        }
        return map;
    }
}

