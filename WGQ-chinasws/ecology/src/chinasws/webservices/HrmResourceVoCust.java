package chinasws.webservices;

/**
 * HrmResource entity.
 *
 * <AUTHOR> Persistence Tools
 */

public class HrmResourceVoCust implements java.io.Serializable {

	private String swshrguid; // 上游HR系统标识
	private String pinyinlastname;
	private String ecology_pinyin_search;
	private String swssigleaccount;	//更新时，标记是否为单岗位仅更新swshrguid

	// Fields
	private Integer id;

	private String subcompanyid1; // 分部

	private String departmentid; // 部门

	private String workcode; // 编号

	private String lastname; // 姓名

	private String loginid; // 系统账号

	private String password; // 密码

	private String seclevel; // 安全级别

	private String sex; // 性别

	private String jobtitle; // 岗位

	private String jobactivityid; // 职务

	private String jobgroupid; // 职务类型

	private String jobcall; // 职称

	private String joblevel; // 职级

	private String jobactivitydesc;// 职责描述

	private String managerid; // 直接上级

	private String assistantid; // 助理

	private String status; // 状态 eg:正式、试用等

	private String locationid; // 办公地点

	private String workroom; // 办公室

	private String telephone; // 办公电话

	private String mobile; // 移动电话

	private String mobilecall; // 其他电话

	private String fax; // 传真

	private String email; // 电子邮件

	private String systemlanguage;// 系统语言 默认7

	private String birthday; // 生日

	private String folk; // 名族

	private String nativeplace; // 籍贯

	private String regresidentplace; // 户口

	private String certificatenum; // 身份证号

	private String maritalstatus; // 婚姻状况

	private String policy; // 政治面貌

	private String bememberdate; // 入团日期

	private String bepartydate; // 入党日期

	private String islabouunion; // 是否是工会会员

	private String educationlevel; // 学历

	private String degree; // 学位

	private String healthinfo; // 健康状况

	private String height; // 身高

	private Integer weight; // 体重

	private String residentplace; // 居住地

	private String homeaddress; // 家庭住址

	private String tempresidentnumber; // 暂住证号码

	public String getSubcompanyid1() {
		return subcompanyid1;
	}

	public void setSubcompanyid1(String subcompanyid1) {
		this.subcompanyid1 = subcompanyid1;
	}

	public String getDepartmentid() {
		return departmentid;
	}

	public void setDepartmentid(String departmentid) {
		this.departmentid = departmentid;
	}

	public String getWorkcode() {
		return workcode;
	}

	public void setWorkcode(String workcode) {
		this.workcode = workcode;
	}

	public String getLastname() {
		return lastname;
	}

	public void setLastname(String lastname) {
		this.lastname = lastname;
	}

	public String getLoginid() {
		return loginid;
	}

	public void setLoginid(String loginid) {
		this.loginid = loginid;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getSeclevel() {
		return seclevel;
	}

	public void setSeclevel(String seclevel) {
		this.seclevel = seclevel;
	}

	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	public String getJobtitle() {
		return jobtitle;
	}

	public void setJobtitle(String jobtitle) {
		this.jobtitle = jobtitle;
	}

	public String getJobactivityid() {
		return jobactivityid;
	}

	public void setJobactivityid(String jobactivityid) {
		this.jobactivityid = jobactivityid;
	}

	public String getJobcall() {
		return jobcall;
	}

	public void setJobcall(String jobcall) {
		this.jobcall = jobcall;
	}

	public String getJoblevel() {
		return joblevel;
	}

	public void setJoblevel(String joblevel) {
		this.joblevel = joblevel;
	}

	public String getJobactivitydesc() {
		return jobactivitydesc;
	}

	public void setJobactivitydesc(String jobactivitydesc) {
		this.jobactivitydesc = jobactivitydesc;
	}

	public String getManagerid() {
		return managerid;
	}

	public void setManagerid(String managerid) {
		this.managerid = managerid;
	}

	public String getAssistantid() {
		return assistantid;
	}

	public void setAssistantid(String assistantid) {
		this.assistantid = assistantid;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getLocationid() {
		return locationid;
	}

	public void setLocationid(String locationid) {
		this.locationid = locationid;
	}

	public String getWorkroom() {
		return workroom;
	}

	public void setWorkroom(String workroom) {
		this.workroom = workroom;
	}

	public String getTelephone() {
		return telephone;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getMobilecall() {
		return mobilecall;
	}

	public void setMobilecall(String mobilecall) {
		this.mobilecall = mobilecall;
	}

	public String getFax() {
		return fax;
	}

	public void setFax(String fax) {
		this.fax = fax;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getSystemlanguage() {
		return systemlanguage;
	}

	public void setSystemlanguage(String systemlanguage) {
		this.systemlanguage = systemlanguage;
	}

	public String getBirthday() {
		return birthday;
	}

	public void setBirthday(String birthday) {
		this.birthday = birthday;
	}

	public String getFolk() {
		return folk;
	}

	public void setFolk(String folk) {
		this.folk = folk;
	}

	public String getNativeplace() {
		return nativeplace;
	}

	public void setNativeplace(String nativeplace) {
		this.nativeplace = nativeplace;
	}

	public String getRegresidentplace() {
		return regresidentplace;
	}

	public void setRegresidentplace(String regresidentplace) {
		this.regresidentplace = regresidentplace;
	}

	public String getCertificatenum() {
		return certificatenum;
	}

	public void setCertificatenum(String certificatenum) {
		this.certificatenum = certificatenum;
	}

	public String getMaritalstatus() {
		return maritalstatus;
	}

	public void setMaritalstatus(String maritalstatus) {
		this.maritalstatus = maritalstatus;
	}

	public String getPolicy() {
		return policy;
	}

	public void setPolicy(String policy) {
		this.policy = policy;
	}

	public String getBememberdate() {
		return bememberdate;
	}

	public void setBememberdate(String bememberdate) {
		this.bememberdate = bememberdate;
	}

	public String getBepartydate() {
		return bepartydate;
	}

	public void setBepartydate(String bepartydate) {
		this.bepartydate = bepartydate;
	}

	public String getIslabouunion() {
		return islabouunion;
	}

	public void setIslabouunion(String islabouunion) {
		this.islabouunion = islabouunion;
	}

	public String getEducationlevel() {
		return educationlevel;
	}

	public void setEducationlevel(String educationlevel) {
		this.educationlevel = educationlevel;
	}

	public String getDegree() {
		return degree;
	}

	public void setDegree(String degree) {
		this.degree = degree;
	}

	public String getHealthinfo() {
		return healthinfo;
	}

	public void setHealthinfo(String healthinfo) {
		this.healthinfo = healthinfo;
	}

	public String getHeight() {
		return height;
	}

	public void setHeight(String height) {
		this.height = height;
	}

	public Integer getWeight() {
		return weight;
	}

	public void setWeight(Integer weight) {
		this.weight = weight;
	}

	public String getResidentplace() {
		return residentplace;
	}

	public void setResidentplace(String residentplace) {
		this.residentplace = residentplace;
	}

	public String getHomeaddress() {
		return homeaddress;
	}

	public void setHomeaddress(String homeaddress) {
		this.homeaddress = homeaddress;
	}

	public String getTempresidentnumber() {
		return tempresidentnumber;
	}

	public void setTempresidentnumber(String tempresidentnumber) {
		this.tempresidentnumber = tempresidentnumber;
	}

	public String getJobgroupid() {
		return jobgroupid;
	}

	public void setJobgroupid(String jobgroupid) {
		this.jobgroupid = jobgroupid;
	}

//	private String nationality;    //国家
//	private String resourcetype;   //用户类别
	private String startdate; // 合同开始日期
	private String enddate; // 合同结束日期

//	private Integer costcenterid;  //所属成本中心
//	private String bankid1;        //工资银行
//	private String accountid1;     //工资账号
//	private Integer resourceimageid;//照片位置
	private String datefield1;
	private String datefield2;
	private String datefield3;
	private String datefield4;
	private String datefield5;
	private Float numberfield1;
	private Float numberfield2;
	private Float numberfield3;
	private Float numberfield4;
	private Float numberfield5;
	private String textfield1;
	private String textfield2;
	private String textfield3;
	private String textfield4;
	private String textfield5;
	private Short tinyintfield1;
	private Short tinyintfield2;
	private Short tinyintfield3;
	private Short tinyintfield4;
	private Short tinyintfield5;

	private String baseFieldsValue = "";

	private String baseFields = "";

	private String personFieldsValue = "";

	private String personFields = "";

	private String workFieldsValue = "";

	private String workFields = "";

//
//
	private String usekind; // 用工性质
//	private String accumfundaccount;
//	private String birthplace;
//
//	private String residentphone;
//	private String residentpostcode;
//	private String extphone;
//	private String managerstr;
//
	private String probationenddate;
//	private Integer countryid;
//	private String passwdchgdate;
//	private Integer needusb;
//	private String serial;
//	private String account;
//	private String lloginid;
//	private Integer needdynapass;
//	private Integer srcfrom;
//	private Integer srcContacterid;
//	private String ncpkcode;
	private String dsporder;

	public String getDsporder() {
		return dsporder;
	}

	public void setDsporder(String dsporder) {
		this.dsporder = dsporder;
	}
//	private Integer passwordstate;
//	private Double spaceSize;
//	private Integer accounttype;
//	private Integer belongto;
//	private String isusedactylogram;
//	private String dactylogram;
//	private String assistantdactylogram;
//	private String loginnet;
//	private Integer passwordlock;
//	private Integer sumpasswordwrong;
//	private String oldpassword1;
//	private String oldpassword2;
//	private String messagerurl;
//	private String msgStyle;
//
//	// Constructors
//
//	/** default constructor */

	public String getDatefield1() {
		return datefield1;
	}

	public void setDatefield1(String datefield1) {
		this.datefield1 = datefield1;
	}

	public String getDatefield2() {
		return datefield2;
	}

	public void setDatefield2(String datefield2) {
		this.datefield2 = datefield2;
	}

	public String getDatefield3() {
		return datefield3;
	}

	public void setDatefield3(String datefield3) {
		this.datefield3 = datefield3;
	}

	public String getDatefield4() {
		return datefield4;
	}

	public void setDatefield4(String datefield4) {
		this.datefield4 = datefield4;
	}

	public String getDatefield5() {
		return datefield5;
	}

	public void setDatefield5(String datefield5) {
		this.datefield5 = datefield5;
	}

	public Float getNumberfield1() {
		return numberfield1;
	}

	public void setNumberfield1(Float numberfield1) {
		this.numberfield1 = numberfield1;
	}

	public Float getNumberfield2() {
		return numberfield2;
	}

	public void setNumberfield2(Float numberfield2) {
		this.numberfield2 = numberfield2;
	}

	public Float getNumberfield3() {
		return numberfield3;
	}

	public void setNumberfield3(Float numberfield3) {
		this.numberfield3 = numberfield3;
	}

	public Float getNumberfield4() {
		return numberfield4;
	}

	public void setNumberfield4(Float numberfield4) {
		this.numberfield4 = numberfield4;
	}

	public Float getNumberfield5() {
		return numberfield5;
	}

	public void setNumberfield5(Float numberfield5) {
		this.numberfield5 = numberfield5;
	}

	public String getTextfield1() {
		return textfield1;
	}

	public void setTextfield1(String textfield1) {
		this.textfield1 = textfield1;
	}

	public String getTextfield2() {
		return textfield2;
	}

	public void setTextfield2(String textfield2) {
		this.textfield2 = textfield2;
	}

	public String getTextfield3() {
		return textfield3;
	}

	public void setTextfield3(String textfield3) {
		this.textfield3 = textfield3;
	}

	public String getTextfield4() {
		return textfield4;
	}

	public void setTextfield4(String textfield4) {
		this.textfield4 = textfield4;
	}

	public String getTextfield5() {
		return textfield5;
	}

	public void setTextfield5(String textfield5) {
		this.textfield5 = textfield5;
	}

	public Short getTinyintfield1() {
		return tinyintfield1;
	}

	public void setTinyintfield1(Short tinyintfield1) {
		this.tinyintfield1 = tinyintfield1;
	}

	public Short getTinyintfield2() {
		return tinyintfield2;
	}

	public void setTinyintfield2(Short tinyintfield2) {
		this.tinyintfield2 = tinyintfield2;
	}

	public Short getTinyintfield3() {
		return tinyintfield3;
	}

	public void setTinyintfield3(Short tinyintfield3) {
		this.tinyintfield3 = tinyintfield3;
	}

	public Short getTinyintfield4() {
		return tinyintfield4;
	}

	public void setTinyintfield4(Short tinyintfield4) {
		this.tinyintfield4 = tinyintfield4;
	}

	public Short getTinyintfield5() {
		return tinyintfield5;
	}

	public void setTinyintfield5(Short tinyintfield5) {
		this.tinyintfield5 = tinyintfield5;
	}

	public String getPersonFields() {
		return personFields;
	}

	public void setPersonFields(String personFields) {
		this.personFields = personFields;
	}

	public String getWorkFields() {
		return workFields;
	}

	public void setWorkFields(String workFields) {
		this.workFields = workFields;
	}

	public String getPersonFieldsValue() {
		return personFieldsValue;
	}

	public void setPersonFieldsValue(String personFieldsValue) {
		this.personFieldsValue = personFieldsValue;
	}

	public String getWorkFieldsValue() {
		return workFieldsValue;
	}

	public void setWorkFieldsValue(String workFieldsValue) {
		this.workFieldsValue = workFieldsValue;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getBaseFields() {
		return baseFields;
	}

	public void setBaseFields(String baseFields) {
		this.baseFields = baseFields;
	}

	public String getBaseFieldsValue() {
		return baseFieldsValue;
	}

	public void setBaseFieldsValue(String baseFieldsValue) {
		this.baseFieldsValue = baseFieldsValue;
	}

	public String getStartdate() {
		return startdate;
	}

	public void setStartdate(String startdate) {
		this.startdate = startdate;
	}

	public String getEnddate() {
		return enddate;
	}

	public void setEnddate(String enddate) {
		this.enddate = enddate;
	}

	public String getUsekind() {
		return usekind;
	}

	public void setUsekind(String usekind) {
		this.usekind = usekind;
	}

	public String getProbationenddate() {
		return probationenddate;
	}

	public void setProbationenddate(String probationenddate) {
		this.probationenddate = probationenddate;
	}

	public String getSwshrguid() {
		return swshrguid;
	}

	public void setSwshrguid(String swshrguid) {
		this.swshrguid = swshrguid;
	}

	public String getPinyinlastname() {
		return pinyinlastname;
	}

	public void setPinyinlastname(String pinyinlastname) {
		this.pinyinlastname = pinyinlastname;
	}

	public String getEcology_pinyin_search() {
		return ecology_pinyin_search;
	}

	public void setEcology_pinyin_search(String ecology_pinyin_search) {
		this.ecology_pinyin_search = ecology_pinyin_search;
	}

	public String getSwssigleaccount() {
		return swssigleaccount;
	}

	public void setSwssigleaccount(String swssigleaccount) {
		this.swssigleaccount = swssigleaccount;
	}

	private String accounttype;

	public String getAccounttype() {
		return accounttype;
	}

	public void setAccounttype(String accounttype) {
		this.accounttype = accounttype;
	}
}
