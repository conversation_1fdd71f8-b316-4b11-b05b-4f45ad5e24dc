package chinasws.webservices;

import javax.jws.WebMethod;
import javax.jws.WebService;

/**
 * <AUTHOR> 
 */

@WebService
public interface HrmServiceCust {

	/**
	 * 人员 -- 兼容多部门多岗位
	 *
	 * @param ipaddress 调用接口的IP地址
	 * @param xmlData   人员信息的xml
	 * @return String 成功:1, 失败:0, 无权限调用:2
	 */
	@WebMethod(operationName = "SynHrmResource", action = "urn:chinasws.webservices.HrmServiceCust.SynHrmResource")
	public String SynHrmResource(String ipaddress, String xmlData) throws Exception;

}
