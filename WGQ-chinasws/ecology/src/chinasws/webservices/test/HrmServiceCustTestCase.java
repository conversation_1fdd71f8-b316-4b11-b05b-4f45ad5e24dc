/**
 * HrmServiceCustTestCase.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package chinasws.webservices.test;

public class HrmServiceCustTestCase extends junit.framework.TestCase {
    public HrmServiceCustTestCase(String name) {
        super(name);
    }

    public void testHrmServiceCustHttpPortWSDL() throws Exception {
        javax.xml.rpc.ServiceFactory serviceFactory = javax.xml.rpc.ServiceFactory.newInstance();
        java.net.URL url = new java.net.URL(new HrmServiceCustLocator().getHrmServiceCustHttpPortAddress() + "?WSDL");
        javax.xml.rpc.Service service = serviceFactory.createService(url, new HrmServiceCustLocator().getServiceName());
        assertTrue(service != null);
    }

    public void test1HrmServiceCustHttpPortSynHrmResource() throws Exception {
        HrmServiceCustHttpBindingStub binding;
        try {
            binding = (HrmServiceCustHttpBindingStub)
                          new HrmServiceCustLocator().getHrmServiceCustHttpPort();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null) {
                jre.getLinkedCause().printStackTrace();
            }
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        String value = null;
        value = binding.synHrmResource(new String(), new String());
        // TBD - validate results
    }

}
