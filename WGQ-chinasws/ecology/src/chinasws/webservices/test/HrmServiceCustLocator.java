/**
 * HrmServiceCustLocator.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package chinasws.webservices.test;

public class HrmServiceCustLocator extends org.apache.axis.client.Service implements HrmServiceCust {

    public HrmServiceCustLocator() {
    }


    public HrmServiceCustLocator(org.apache.axis.EngineConfiguration config) {
        super(config);
    }

    public HrmServiceCustLocator(String wsdlLoc, javax.xml.namespace.QName sName) throws javax.xml.rpc.ServiceException {
        super(wsdlLoc, sName);
    }

    // Use to get a proxy class for HrmServiceCustHttpPort
    private String HrmServiceCustHttpPort_address = "http://************/services/HrmServiceCust";

    @Override
    public String getHrmServiceCustHttpPortAddress() {
        return HrmServiceCustHttpPort_address;
    }

    // The WSDD service name defaults to the port name.
    private String HrmServiceCustHttpPortWSDDServiceName = "HrmServiceCustHttpPort";

    public String getHrmServiceCustHttpPortWSDDServiceName() {
        return HrmServiceCustHttpPortWSDDServiceName;
    }

    public void setHrmServiceCustHttpPortWSDDServiceName(String name) {
        HrmServiceCustHttpPortWSDDServiceName = name;
    }

    @Override
    public HrmServiceCustPortType getHrmServiceCustHttpPort() throws javax.xml.rpc.ServiceException {
       java.net.URL endpoint;
        try {
            endpoint = new java.net.URL(HrmServiceCustHttpPort_address);
        }
        catch (java.net.MalformedURLException e) {
            throw new javax.xml.rpc.ServiceException(e);
        }
        return getHrmServiceCustHttpPort(endpoint);
    }

    @Override
    public HrmServiceCustPortType getHrmServiceCustHttpPort(java.net.URL portAddress) throws javax.xml.rpc.ServiceException {
        try {
            HrmServiceCustHttpBindingStub _stub = new HrmServiceCustHttpBindingStub(portAddress, this);
            _stub.setPortName(getHrmServiceCustHttpPortWSDDServiceName());
            return _stub;
        }
        catch (org.apache.axis.AxisFault e) {
            return null;
        }
    }

    public void setHrmServiceCustHttpPortEndpointAddress(String address) {
        HrmServiceCustHttpPort_address = address;
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        try {
            if (HrmServiceCustPortType.class.isAssignableFrom(serviceEndpointInterface)) {
                HrmServiceCustHttpBindingStub _stub = new HrmServiceCustHttpBindingStub(new java.net.URL(HrmServiceCustHttpPort_address), this);
                _stub.setPortName(getHrmServiceCustHttpPortWSDDServiceName());
                return _stub;
            }
        }
        catch (Throwable t) {
            throw new javax.xml.rpc.ServiceException(t);
        }
        throw new javax.xml.rpc.ServiceException("There is no stub implementation for the interface:  " + (serviceEndpointInterface == null ? "null" : serviceEndpointInterface.getName()));
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(javax.xml.namespace.QName portName, Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        if (portName == null) {
            return getPort(serviceEndpointInterface);
        }
        String inputPortName = portName.getLocalPart();
        if ("HrmServiceCustHttpPort".equals(inputPortName)) {
            return getHrmServiceCustHttpPort();
        }
        else  {
            java.rmi.Remote _stub = getPort(serviceEndpointInterface);
            ((org.apache.axis.client.Stub) _stub).setPortName(portName);
            return _stub;
        }
    }

    public javax.xml.namespace.QName getServiceName() {
        return new javax.xml.namespace.QName("http://localhost/services/HrmServiceCust", "HrmServiceCust");
    }

    private java.util.HashSet ports = null;

    public java.util.Iterator getPorts() {
        if (ports == null) {
            ports = new java.util.HashSet();
            ports.add(new javax.xml.namespace.QName("http://localhost/services/HrmServiceCust", "HrmServiceCustHttpPort"));
        }
        return ports.iterator();
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(String portName, String address) throws javax.xml.rpc.ServiceException {
        
if ("HrmServiceCustHttpPort".equals(portName)) {
            setHrmServiceCustHttpPortEndpointAddress(address);
        }
        else 
{ // Unknown Port Name
            throw new javax.xml.rpc.ServiceException(" Cannot set Endpoint Address for Unknown Port" + portName);
        }
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(javax.xml.namespace.QName portName, String address) throws javax.xml.rpc.ServiceException {
        setEndpointAddress(portName.getLocalPart(), address);
    }

}
