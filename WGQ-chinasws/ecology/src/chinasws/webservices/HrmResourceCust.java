package chinasws.webservices;

/**
 * HrmResource entity.
 *
 * <AUTHOR> Persistence Tools
 */

public class HrmResourceCust implements java.io.Serializable {

    private String swshrguid;
    private String pinyinlastname;
    private String ecology_pinyin_search;
    private String swssigleaccount;

    // Fields
    private Integer id;
    private String loginid;
    private String password;
    private String lastname;
    private String sex;
    private String birthday;
    private Integer nationality;
    private Integer systemlanguage;
    private String maritalstatus;
    private String telephone;
    private String mobile;
    private String mobilecall;
    private String email;
    private Integer locationid;
    private String workroom;
    private String homeaddress;
    private String resourcetype;
    private String startdate;
    private String enddate;
    private Integer jobtitle;
    private String jobactivitydesc;
    private Short joblevel;
    private Short seclevel;
    private Integer departmentid;
    private Integer subcompanyid1;
    private Integer costcenterid;
    private Integer managerid;
    private Integer assistantid;
    private Integer bankid1;
    private String accountid1;
    private Integer resourceimageid;
    private Integer createrid;
    private String createdate;
    private Integer lastmodid;
    private String lastmoddate;
    private String lastlogindate;
    private String datefield1;
    private String datefield2;
    private String datefield3;
    private String datefield4;
    private String datefield5;
    private Float numberfield1;
    private Float numberfield2;
    private Float numberfield3;
    private Float numberfield4;
    private Float numberfield5;
    private String textfield1;
    private String textfield2;
    private String textfield3;
    private String textfield4;
    private String textfield5;
    private Short tinyintfield1;
    private Short tinyintfield2;
    private Short tinyintfield3;
    private Short tinyintfield4;
    private Short tinyintfield5;
    private String certificatenum;
    private String nativeplace;
    private Integer educationlevel;
    private String bememberdate;
    private String bepartydate;
    private String workcode;
    private String regresidentplace;
    private String healthinfo;
    private String residentplace;
    private String policy;
    private String degree;
    private String height;
    private Integer usekind;
    private Integer jobcall;
    private String accumfundaccount;
    private String birthplace;
    private String folk;
    private String residentphone;
    private String residentpostcode;
    private String extphone;
    private String managerstr;
    private Integer status;
    private String fax;
    private String islabouunion;
    private Integer weight;
    private String tempresidentnumber;
    private String probationenddate;
    private Integer countryid;
    private String passwdchgdate;
    private Integer needusb;
    private String serial;
    private String account;
    private String lloginid;
    private Integer needdynapass;
    private Integer srcfrom;
    private Integer srcContacterid;
    private String ncpkcode;
    private String dsporder;
    private Integer passwordstate;
    private Double spaceSize;
    private Integer accounttype;
    private Integer belongto;
    private String isusedactylogram;
    private String dactylogram;
    private String assistantdactylogram;
    private String loginnet;
    private Integer passwordlock;
    private Integer sumpasswordwrong;
    private String oldpassword1;
    private String oldpassword2;
    private String messagerurl;
    private String msgStyle;

    // Constructors

    /**
     * default constructor
     */
    public HrmResourceCust() {
    }

    /**
     * full constructor
     */
    public HrmResourceCust(String loginid, String password, String lastname, String sex, String birthday,
                           Integer nationality, Integer systemlanguage, String maritalstatus, String telephone, String mobile,
                           String mobilecall, String email, Integer locationid, String workroom, String homeaddress,
                           String resourcetype, String startdate, String enddate, Integer jobtitle, String jobactivitydesc,
                           Short joblevel, Short seclevel, Integer departmentid, Integer subcompanyid1, Integer costcenterid,
                           Integer managerid, Integer assistantid, Integer bankid1, String accountid1, Integer resourceimageid,
                           Integer createrid, String createdate, Integer lastmodid, String lastmoddate, String lastlogindate,
                           String datefield1, String datefield2, String datefield3, String datefield4, String datefield5,
                           Float numberfield1, Float numberfield2, Float numberfield3, Float numberfield4, Float numberfield5,
                           String textfield1, String textfield2, String textfield3, String textfield4, String textfield5,
                           Short tinyintfield1, Short tinyintfield2, Short tinyintfield3, Short tinyintfield4, Short tinyintfield5,
                           String certificatenum, String nativeplace, Integer educationlevel, String bememberdate, String bepartydate,
                           String workcode, String regresidentplace, String healthinfo, String residentplace, String policy,
                           String degree, String height, Integer usekind, Integer jobcall, String accumfundaccount, String birthplace,
                           String folk, String residentphone, String residentpostcode, String extphone, String managerstr,
                           Integer status, String fax, String islabouunion, Integer weight, String tempresidentnumber,
                           String probationenddate, Integer countryid, String passwdchgdate, Integer needusb, String serial,
                           String account, String lloginid, Integer needdynapass, Integer srcfrom, Integer srcContacterid,
                           String ncpkcode, String dsporder, Integer passwordstate, Double spaceSize, Integer accounttype,
                           Integer belongto, String isusedactylogram, String dactylogram, String assistantdactylogram, String loginnet,
                           Integer passwordlock, Integer sumpasswordwrong, String oldpassword1, String oldpassword2,
                           String messagerurl, String msgStyle) {
        this.loginid = loginid;
        this.password = password;
        this.lastname = lastname;
        this.sex = sex;
        this.birthday = birthday;
        this.nationality = nationality;
        this.systemlanguage = systemlanguage;
        this.maritalstatus = maritalstatus;
        this.telephone = telephone;
        this.mobile = mobile;
        this.mobilecall = mobilecall;
        this.email = email;
        this.locationid = locationid;
        this.workroom = workroom;
        this.homeaddress = homeaddress;
        this.resourcetype = resourcetype;
        this.startdate = startdate;
        this.enddate = enddate;
        this.jobtitle = jobtitle;
        this.jobactivitydesc = jobactivitydesc;
        this.joblevel = joblevel;
        this.seclevel = seclevel;
        this.departmentid = departmentid;
        this.subcompanyid1 = subcompanyid1;
        this.costcenterid = costcenterid;
        this.managerid = managerid;
        this.assistantid = assistantid;
        this.bankid1 = bankid1;
        this.accountid1 = accountid1;
        this.resourceimageid = resourceimageid;
        this.createrid = createrid;
        this.createdate = createdate;
        this.lastmodid = lastmodid;
        this.lastmoddate = lastmoddate;
        this.lastlogindate = lastlogindate;
        this.datefield1 = datefield1;
        this.datefield2 = datefield2;
        this.datefield3 = datefield3;
        this.datefield4 = datefield4;
        this.datefield5 = datefield5;
        this.numberfield1 = numberfield1;
        this.numberfield2 = numberfield2;
        this.numberfield3 = numberfield3;
        this.numberfield4 = numberfield4;
        this.numberfield5 = numberfield5;
        this.textfield1 = textfield1;
        this.textfield2 = textfield2;
        this.textfield3 = textfield3;
        this.textfield4 = textfield4;
        this.textfield5 = textfield5;
        this.tinyintfield1 = tinyintfield1;
        this.tinyintfield2 = tinyintfield2;
        this.tinyintfield3 = tinyintfield3;
        this.tinyintfield4 = tinyintfield4;
        this.tinyintfield5 = tinyintfield5;
        this.certificatenum = certificatenum;
        this.nativeplace = nativeplace;
        this.educationlevel = educationlevel;
        this.bememberdate = bememberdate;
        this.bepartydate = bepartydate;
        this.workcode = workcode;
        this.regresidentplace = regresidentplace;
        this.healthinfo = healthinfo;
        this.residentplace = residentplace;
        this.policy = policy;
        this.degree = degree;
        this.height = height;
        this.usekind = usekind;
        this.jobcall = jobcall;
        this.accumfundaccount = accumfundaccount;
        this.birthplace = birthplace;
        this.folk = folk;
        this.residentphone = residentphone;
        this.residentpostcode = residentpostcode;
        this.extphone = extphone;
        this.managerstr = managerstr;
        this.status = status;
        this.fax = fax;
        this.islabouunion = islabouunion;
        this.weight = weight;
        this.tempresidentnumber = tempresidentnumber;
        this.probationenddate = probationenddate;
        this.countryid = countryid;
        this.passwdchgdate = passwdchgdate;
        this.needusb = needusb;
        this.serial = serial;
        this.account = account;
        this.lloginid = lloginid;
        this.needdynapass = needdynapass;
        this.srcfrom = srcfrom;
        this.srcContacterid = srcContacterid;
        this.ncpkcode = ncpkcode;
        this.dsporder = dsporder;
        this.passwordstate = passwordstate;
        this.spaceSize = spaceSize;
        this.accounttype = accounttype;
        this.belongto = belongto;
        this.isusedactylogram = isusedactylogram;
        this.dactylogram = dactylogram;
        this.assistantdactylogram = assistantdactylogram;
        this.loginnet = loginnet;
        this.passwordlock = passwordlock;
        this.sumpasswordwrong = sumpasswordwrong;
        this.oldpassword1 = oldpassword1;
        this.oldpassword2 = oldpassword2;
        this.messagerurl = messagerurl;
        this.msgStyle = msgStyle;
    }

    // Property accessors

    public Integer getId() {
        return this.id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLoginid() {
        return this.loginid;
    }

    public void setLoginid(String loginid) {
        this.loginid = loginid;
    }

    public String getPassword() {
        return this.password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getLastname() {
        return this.lastname;
    }

    public void setLastname(String lastname) {
        this.lastname = lastname;
    }

    public String getSex() {
        return this.sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getBirthday() {
        return this.birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public Integer getNationality() {
        return this.nationality;
    }

    public void setNationality(Integer nationality) {
        this.nationality = nationality;
    }

    public Integer getSystemlanguage() {
        return this.systemlanguage;
    }

    public void setSystemlanguage(Integer systemlanguage) {
        this.systemlanguage = systemlanguage;
    }

    public String getMaritalstatus() {
        return this.maritalstatus;
    }

    public void setMaritalstatus(String maritalstatus) {
        this.maritalstatus = maritalstatus;
    }

    public String getTelephone() {
        return this.telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getMobile() {
        return this.mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMobilecall() {
        return this.mobilecall;
    }

    public void setMobilecall(String mobilecall) {
        this.mobilecall = mobilecall;
    }

    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getLocationid() {
        return this.locationid;
    }

    public void setLocationid(Integer locationid) {
        this.locationid = locationid;
    }

    public String getWorkroom() {
        return this.workroom;
    }

    public void setWorkroom(String workroom) {
        this.workroom = workroom;
    }

    public String getHomeaddress() {
        return this.homeaddress;
    }

    public void setHomeaddress(String homeaddress) {
        this.homeaddress = homeaddress;
    }

    public String getResourcetype() {
        return this.resourcetype;
    }

    public void setResourcetype(String resourcetype) {
        this.resourcetype = resourcetype;
    }

    public String getStartdate() {
        return this.startdate;
    }

    public void setStartdate(String startdate) {
        this.startdate = startdate;
    }

    public String getEnddate() {
        return this.enddate;
    }

    public void setEnddate(String enddate) {
        this.enddate = enddate;
    }

    public Integer getJobtitle() {
        return this.jobtitle;
    }

    public void setJobtitle(Integer jobtitle) {
        this.jobtitle = jobtitle;
    }

    public String getJobactivitydesc() {
        return this.jobactivitydesc;
    }

    public void setJobactivitydesc(String jobactivitydesc) {
        this.jobactivitydesc = jobactivitydesc;
    }

    public Short getJoblevel() {
        return this.joblevel;
    }

    public void setJoblevel(Short joblevel) {
        this.joblevel = joblevel;
    }

    public Short getSeclevel() {
        return this.seclevel;
    }

    public void setSeclevel(Short seclevel) {
        this.seclevel = seclevel;
    }

    public Integer getDepartmentid() {
        return this.departmentid;
    }

    public void setDepartmentid(Integer departmentid) {
        this.departmentid = departmentid;
    }

    public Integer getSubcompanyid1() {
        return this.subcompanyid1;
    }

    public void setSubcompanyid1(Integer subcompanyid1) {
        this.subcompanyid1 = subcompanyid1;
    }

    public Integer getCostcenterid() {
        return this.costcenterid;
    }

    public void setCostcenterid(Integer costcenterid) {
        this.costcenterid = costcenterid;
    }

    public Integer getManagerid() {
        return this.managerid;
    }

    public void setManagerid(Integer managerid) {
        this.managerid = managerid;
    }

    public Integer getAssistantid() {
        return this.assistantid;
    }

    public void setAssistantid(Integer assistantid) {
        this.assistantid = assistantid;
    }

    public Integer getBankid1() {
        return this.bankid1;
    }

    public void setBankid1(Integer bankid1) {
        this.bankid1 = bankid1;
    }

    public String getAccountid1() {
        return this.accountid1;
    }

    public void setAccountid1(String accountid1) {
        this.accountid1 = accountid1;
    }

    public Integer getResourceimageid() {
        return this.resourceimageid;
    }

    public void setResourceimageid(Integer resourceimageid) {
        this.resourceimageid = resourceimageid;
    }

    public Integer getCreaterid() {
        return this.createrid;
    }

    public void setCreaterid(Integer createrid) {
        this.createrid = createrid;
    }

    public String getCreatedate() {
        return this.createdate;
    }

    public void setCreatedate(String createdate) {
        this.createdate = createdate;
    }

    public Integer getLastmodid() {
        return this.lastmodid;
    }

    public void setLastmodid(Integer lastmodid) {
        this.lastmodid = lastmodid;
    }

    public String getLastmoddate() {
        return this.lastmoddate;
    }

    public void setLastmoddate(String lastmoddate) {
        this.lastmoddate = lastmoddate;
    }

    public String getLastlogindate() {
        return this.lastlogindate;
    }

    public void setLastlogindate(String lastlogindate) {
        this.lastlogindate = lastlogindate;
    }

    public String getDatefield1() {
        return this.datefield1;
    }

    public void setDatefield1(String datefield1) {
        this.datefield1 = datefield1;
    }

    public String getDatefield2() {
        return this.datefield2;
    }

    public void setDatefield2(String datefield2) {
        this.datefield2 = datefield2;
    }

    public String getDatefield3() {
        return this.datefield3;
    }

    public void setDatefield3(String datefield3) {
        this.datefield3 = datefield3;
    }

    public String getDatefield4() {
        return this.datefield4;
    }

    public void setDatefield4(String datefield4) {
        this.datefield4 = datefield4;
    }

    public String getDatefield5() {
        return this.datefield5;
    }

    public void setDatefield5(String datefield5) {
        this.datefield5 = datefield5;
    }

    public Float getNumberfield1() {
        return this.numberfield1;
    }

    public void setNumberfield1(Float numberfield1) {
        this.numberfield1 = numberfield1;
    }

    public Float getNumberfield2() {
        return this.numberfield2;
    }

    public void setNumberfield2(Float numberfield2) {
        this.numberfield2 = numberfield2;
    }

    public Float getNumberfield3() {
        return this.numberfield3;
    }

    public void setNumberfield3(Float numberfield3) {
        this.numberfield3 = numberfield3;
    }

    public Float getNumberfield4() {
        return this.numberfield4;
    }

    public void setNumberfield4(Float numberfield4) {
        this.numberfield4 = numberfield4;
    }

    public Float getNumberfield5() {
        return this.numberfield5;
    }

    public void setNumberfield5(Float numberfield5) {
        this.numberfield5 = numberfield5;
    }

    public String getTextfield1() {
        return this.textfield1;
    }

    public void setTextfield1(String textfield1) {
        this.textfield1 = textfield1;
    }

    public String getTextfield2() {
        return this.textfield2;
    }

    public void setTextfield2(String textfield2) {
        this.textfield2 = textfield2;
    }

    public String getTextfield3() {
        return this.textfield3;
    }

    public void setTextfield3(String textfield3) {
        this.textfield3 = textfield3;
    }

    public String getTextfield4() {
        return this.textfield4;
    }

    public void setTextfield4(String textfield4) {
        this.textfield4 = textfield4;
    }

    public String getTextfield5() {
        return this.textfield5;
    }

    public void setTextfield5(String textfield5) {
        this.textfield5 = textfield5;
    }

    public Short getTinyintfield1() {
        return this.tinyintfield1;
    }

    public void setTinyintfield1(Short tinyintfield1) {
        this.tinyintfield1 = tinyintfield1;
    }

    public Short getTinyintfield2() {
        return this.tinyintfield2;
    }

    public void setTinyintfield2(Short tinyintfield2) {
        this.tinyintfield2 = tinyintfield2;
    }

    public Short getTinyintfield3() {
        return this.tinyintfield3;
    }

    public void setTinyintfield3(Short tinyintfield3) {
        this.tinyintfield3 = tinyintfield3;
    }

    public Short getTinyintfield4() {
        return this.tinyintfield4;
    }

    public void setTinyintfield4(Short tinyintfield4) {
        this.tinyintfield4 = tinyintfield4;
    }

    public Short getTinyintfield5() {
        return this.tinyintfield5;
    }

    public void setTinyintfield5(Short tinyintfield5) {
        this.tinyintfield5 = tinyintfield5;
    }

    public String getCertificatenum() {
        return this.certificatenum;
    }

    public void setCertificatenum(String certificatenum) {
        this.certificatenum = certificatenum;
    }

    public String getNativeplace() {
        return this.nativeplace;
    }

    public void setNativeplace(String nativeplace) {
        this.nativeplace = nativeplace;
    }

    public Integer getEducationlevel() {
        return this.educationlevel;
    }

    public void setEducationlevel(Integer educationlevel) {
        this.educationlevel = educationlevel;
    }

    public String getBememberdate() {
        return this.bememberdate;
    }

    public void setBememberdate(String bememberdate) {
        this.bememberdate = bememberdate;
    }

    public String getBepartydate() {
        return this.bepartydate;
    }

    public void setBepartydate(String bepartydate) {
        this.bepartydate = bepartydate;
    }

    public String getWorkcode() {
        return this.workcode;
    }

    public void setWorkcode(String workcode) {
        this.workcode = workcode;
    }

    public String getRegresidentplace() {
        return this.regresidentplace;
    }

    public void setRegresidentplace(String regresidentplace) {
        this.regresidentplace = regresidentplace;
    }

    public String getHealthinfo() {
        return this.healthinfo;
    }

    public void setHealthinfo(String healthinfo) {
        this.healthinfo = healthinfo;
    }

    public String getResidentplace() {
        return this.residentplace;
    }

    public void setResidentplace(String residentplace) {
        this.residentplace = residentplace;
    }

    public String getPolicy() {
        return this.policy;
    }

    public void setPolicy(String policy) {
        this.policy = policy;
    }

    public String getDegree() {
        return this.degree;
    }

    public void setDegree(String degree) {
        this.degree = degree;
    }

    public String getHeight() {
        return this.height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public Integer getUsekind() {
        return this.usekind;
    }

    public void setUsekind(Integer usekind) {
        this.usekind = usekind;
    }

    public Integer getJobcall() {
        return this.jobcall;
    }

    public void setJobcall(Integer jobcall) {
        this.jobcall = jobcall;
    }

    public String getAccumfundaccount() {
        return this.accumfundaccount;
    }

    public void setAccumfundaccount(String accumfundaccount) {
        this.accumfundaccount = accumfundaccount;
    }

    public String getBirthplace() {
        return this.birthplace;
    }

    public void setBirthplace(String birthplace) {
        this.birthplace = birthplace;
    }

    public String getFolk() {
        return this.folk;
    }

    public void setFolk(String folk) {
        this.folk = folk;
    }

    public String getResidentphone() {
        return this.residentphone;
    }

    public void setResidentphone(String residentphone) {
        this.residentphone = residentphone;
    }

    public String getResidentpostcode() {
        return this.residentpostcode;
    }

    public void setResidentpostcode(String residentpostcode) {
        this.residentpostcode = residentpostcode;
    }

    public String getExtphone() {
        return this.extphone;
    }

    public void setExtphone(String extphone) {
        this.extphone = extphone;
    }

    public String getManagerstr() {
        return this.managerstr;
    }

    public void setManagerstr(String managerstr) {
        this.managerstr = managerstr;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getFax() {
        return this.fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getIslabouunion() {
        return this.islabouunion;
    }

    public void setIslabouunion(String islabouunion) {
        this.islabouunion = islabouunion;
    }

    public Integer getWeight() {
        return this.weight;
    }

    public void setWeight(Integer weight) {
        this.weight = weight;
    }

    public String getTempresidentnumber() {
        return this.tempresidentnumber;
    }

    public void setTempresidentnumber(String tempresidentnumber) {
        this.tempresidentnumber = tempresidentnumber;
    }

    public String getProbationenddate() {
        return this.probationenddate;
    }

    public void setProbationenddate(String probationenddate) {
        this.probationenddate = probationenddate;
    }

    public Integer getCountryid() {
        return this.countryid;
    }

    public void setCountryid(Integer countryid) {
        this.countryid = countryid;
    }

    public String getPasswdchgdate() {
        return this.passwdchgdate;
    }

    public void setPasswdchgdate(String passwdchgdate) {
        this.passwdchgdate = passwdchgdate;
    }

    public Integer getNeedusb() {
        return this.needusb;
    }

    public void setNeedusb(Integer needusb) {
        this.needusb = needusb;
    }

    public String getSerial() {
        return this.serial;
    }

    public void setSerial(String serial) {
        this.serial = serial;
    }

    public String getAccount() {
        return this.account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getLloginid() {
        return this.lloginid;
    }

    public void setLloginid(String lloginid) {
        this.lloginid = lloginid;
    }

    public Integer getNeeddynapass() {
        return this.needdynapass;
    }

    public void setNeeddynapass(Integer needdynapass) {
        this.needdynapass = needdynapass;
    }

    public Integer getSrcfrom() {
        return this.srcfrom;
    }

    public void setSrcfrom(Integer srcfrom) {
        this.srcfrom = srcfrom;
    }

    public Integer getSrcContacterid() {
        return this.srcContacterid;
    }

    public void setSrcContacterid(Integer srcContacterid) {
        this.srcContacterid = srcContacterid;
    }

    public String getNcpkcode() {
        return this.ncpkcode;
    }

    public void setNcpkcode(String ncpkcode) {
        this.ncpkcode = ncpkcode;
    }

    public String getDsporder() {
        return this.dsporder;
    }

    public void setDsporder(String dsporder) {
        this.dsporder = dsporder;
    }

    public Integer getPasswordstate() {
        return this.passwordstate;
    }

    public void setPasswordstate(Integer passwordstate) {
        this.passwordstate = passwordstate;
    }

    public Double getSpaceSize() {
        return this.spaceSize;
    }

    public void setSpaceSize(Double spaceSize) {
        this.spaceSize = spaceSize;
    }

    public Integer getAccounttype() {
        return this.accounttype;
    }

    public void setAccounttype(Integer accounttype) {
        this.accounttype = accounttype;
    }

    public Integer getBelongto() {
        return this.belongto;
    }

    public void setBelongto(Integer belongto) {
        this.belongto = belongto;
    }

    public String getIsusedactylogram() {
        return this.isusedactylogram;
    }

    public void setIsusedactylogram(String isusedactylogram) {
        this.isusedactylogram = isusedactylogram;
    }

    public String getDactylogram() {
        return this.dactylogram;
    }

    public void setDactylogram(String dactylogram) {
        this.dactylogram = dactylogram;
    }

    public String getAssistantdactylogram() {
        return this.assistantdactylogram;
    }

    public void setAssistantdactylogram(String assistantdactylogram) {
        this.assistantdactylogram = assistantdactylogram;
    }

    public String getLoginnet() {
        return this.loginnet;
    }

    public void setLoginnet(String loginnet) {
        this.loginnet = loginnet;
    }

    public Integer getPasswordlock() {
        return this.passwordlock;
    }

    public void setPasswordlock(Integer passwordlock) {
        this.passwordlock = passwordlock;
    }

    public Integer getSumpasswordwrong() {
        return this.sumpasswordwrong;
    }

    public void setSumpasswordwrong(Integer sumpasswordwrong) {
        this.sumpasswordwrong = sumpasswordwrong;
    }

    public String getOldpassword1() {
        return this.oldpassword1;
    }

    public void setOldpassword1(String oldpassword1) {
        this.oldpassword1 = oldpassword1;
    }

    public String getOldpassword2() {
        return this.oldpassword2;
    }

    public void setOldpassword2(String oldpassword2) {
        this.oldpassword2 = oldpassword2;
    }

    public String getMessagerurl() {
        return this.messagerurl;
    }

    public void setMessagerurl(String messagerurl) {
        this.messagerurl = messagerurl;
    }

    public String getMsgStyle() {
        return this.msgStyle;
    }

    public void setMsgStyle(String msgStyle) {
        this.msgStyle = msgStyle;
    }

    public String getSwshrguid() {
        return swshrguid;
    }

    public void setSwshrguid(String swshrguid) {
        this.swshrguid = swshrguid;
    }

    public String getPinyinlastname() {
        return pinyinlastname;
    }

    public void setPinyinlastname(String pinyinlastname) {
        this.pinyinlastname = pinyinlastname;
    }

    public String getEcology_pinyin_search() {
        return ecology_pinyin_search;
    }

    public void setEcology_pinyin_search(String ecology_pinyin_search) {
        this.ecology_pinyin_search = ecology_pinyin_search;
    }

    public String getSwssigleaccount() {
        return swssigleaccount;
    }

    public void setSwssigleaccount(String swssigleaccount) {
        this.swssigleaccount = swssigleaccount;
    }
}
