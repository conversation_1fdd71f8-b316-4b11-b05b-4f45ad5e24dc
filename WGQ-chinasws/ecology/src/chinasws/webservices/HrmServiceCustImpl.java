package chinasws.webservices;

import java.util.*;
import java.util.Map.Entry;

import javax.servlet.http.HttpServletRequest;

import com.engine.common.service.impl.HrmCommonServiceImpl;
import weaver.conn.BatchRecordSet;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.join.hrm.in.ImportLog;

import org.codehaus.xfire.transport.http.XFireServletController;
import weaver.toolbox.core.util.StrUtil;
import weaver.toolbox.db.recordset.ExecuteUtil;

/**
 * <AUTHOR> 1. 角色 -- 新增，修改，删除 2. 矩阵 -- 修改
 */
public class HrmServiceCustImpl extends BaseBean implements HrmServiceCust {

    // 存放分部,部门,岗位,人员的javaBean
    private HashMap h_orgInfo = new HashMap();

    // 存放新增的分部,部门,岗位,人员的javaBean
    private HashMap h_orgInfo_add = new HashMap();

    // 存放修改的分部,部门,岗位,人员的javaBean
    private HashMap h_orgInfo_update = new HashMap();

    // 新增分部,部门,岗位,人员list
    private List h_addOrg = new ArrayList();

    // 编辑分部,部门,岗位,人员list
    private List h_updateOrg = new ArrayList();

    // 删除分部,部门,岗位list
    private List h_delOrg = new ArrayList();

    private String configip = "," + getPropValue("HrmWebserviceIP", "ipaddress") + ","; // 调用接口的iP地址范围

    @Override
    public String SynHrmResource(String ipaddress, String xmlData) throws Exception {
        writeLog("HrmServiceCustImpl SynHrmResource -------");

        ipaddress = getClientIpXfire();
        String result = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><result>";
        if (("," + configip + ",").indexOf("," + ipaddress + ",") < 0) {
            writeLog("IP" + ipaddress + "不在设定的范围内,无权限访问!");
            result += "<message value=\"2\">IP不在设定的范围内,无权限访问</message></result>";
            return result;
        }

        ParseXmlCust parseXml = new ParseXmlCust();
        try {
            writeLog("xmlData -- 开始解析");
            parseXml.parseHrmResource(xmlData);
            this.h_orgInfo_add = parseXml.getH_orgInfo_add();
            writeLog("h_orgInfo_add size -- " + this.h_orgInfo_add.size());
            writeLog("h_orgInfo_add -- " + this.h_orgInfo_add.toString());
            this.h_orgInfo_update = parseXml.getH_orgInfo_update();
            writeLog("h_orgInfo_update size -- " + this.h_orgInfo_update.size());
            writeLog("h_orgInfo_update -- " + this.h_orgInfo_update.toString());
            this.h_delOrg = parseXml.getH_delOrg();
            writeLog("h_delOrg size -- " + this.h_delOrg.size());
            writeLog("h_delOrg -- " + this.h_delOrg.toString());

            HrmImportProcessCust importProcess = new HrmImportProcessCust();

            // region 处理新建数据
            List addresultList = importProcess.processMap("swshrguid", this.h_orgInfo_add, "add");
            ImportLog addlog;
            String errlogRecord = "";
            String sulogrecord = "";
            for (int j = 0; j < addresultList.size(); j++) {
                addlog = (ImportLog) addresultList.get(j);
                if (addlog.getStatus().equals("失败")) {
                    errlogRecord += (addlog.getWorkCode() != null ? addlog.getWorkCode() : "") + "|"
                            + (addlog.getLastname() != null ? addlog.getLastname() : "") + "|" + addlog.getOperation()
                            + "|" + addlog.getStatus() + "|" + addlog.getReason() + ",";
                }
            }
            //失败
            if (!"".equals(errlogRecord)) {
                result += "<message value=\"0\">" + errlogRecord + "</message>";
            }

            for (int k = 0; k < addresultList.size(); k++) {
                addlog = (ImportLog) addresultList.get(k);
                if (addlog.getStatus().equals("成功")) {
                    sulogrecord += (addlog.getWorkCode() != null ? addlog.getWorkCode() : "") + "|"
                            + (addlog.getLastname() != null ? addlog.getLastname() : "") + "|" + addlog.getOperation()
                            + "|" + addlog.getStatus() + "|" + addlog.getReason() + ",";
                }
            }
            //成功
            if (!"".equals(sulogrecord)) {
                result += "<message value=\"1\">" + sulogrecord + "</message>";
            }
            // endregion 处理新建数据

            // region 处理更新数据
            List editresultList = importProcess.processMap("swshrguid", this.h_orgInfo_update, "update");
            ImportLog editlog;
            String errlogRecord1 = "";
            String sulogrecord1 = "";
            for (int s = 0; s < editresultList.size(); s++) {
                editlog = (ImportLog) editresultList.get(s);
                if (editlog.getStatus().equals("失败")) {
                    errlogRecord1 += (editlog.getWorkCode() != null ? editlog.getWorkCode() : "") + "|"
                            + (editlog.getLastname() != null ? editlog.getLastname() : "") + "|"
                            + editlog.getOperation() + "|" + editlog.getStatus() + "|" + editlog.getReason() + ",";
                }
            }
            //失败
            if (!"".equals(errlogRecord1)) {
                result += "<message value=\"0\">" + errlogRecord1 + "</message>";
            }

            for (int h = 0; h < editresultList.size(); h++) {
                editlog = (ImportLog) editresultList.get(h);
                if (editlog.getStatus().equals("成功")) {
                    sulogrecord1 += (editlog.getWorkCode() != null ? editlog.getWorkCode() : "") + "|"
                            + (editlog.getLastname() != null ? editlog.getLastname() : "") + "|"
                            + editlog.getOperation() + "|" + editlog.getStatus() + "|" + editlog.getReason() + ",";
                }
            }
            //成功
            if (!"".equals(sulogrecord1)) {
                result += "<message value=\"1\">" + sulogrecord1 + "</message>";
            }
            // endregion 处理更新数据

            // region 处理人员拼音搜索问题
            //导入类中已实现新数据的处理，此次将拼音为空的历史数据进行处理
            writeLog("--------处理人员拼音搜索问题 历史数据------");
            RecordSet rs = new RecordSet();
            String sql = "select id,lastname,pinyinlastname,ecology_pinyin_search from hrmresource where pinyinlastname is null or ecology_pinyin_search is null";
            //查询拼音为空的人员
            rs.execute(sql);
            String idTemp;
            String pinyinTemp;
            RecordSet pinyinRS = new RecordSet();
            while (rs.next()) {
                idTemp = Util.null2String(rs.getString("id"));
                pinyinTemp = Util.null2String(rs.getString("lastname"));
                pinyinTemp = new HrmCommonServiceImpl().generateQuickSearchStr(pinyinTemp);
                sql = "update HrmResource set pinyinlastname = '" + pinyinTemp + "', ecology_pinyin_search = '" + pinyinTemp + "' where id =" + idTemp;
                pinyinRS.execute(sql);
                //writeLog("处理人员拼音搜索问题 历史数据 sql -- " + sql);
            }
            // endregion 处理人员拼音搜索问题

            // region 处理离职数据
            String errlogRecord2 = "";
            String sulogrecord2  = "";
            if (this.h_delOrg.size() > 0) {
                ArrayList sqlList = new ArrayList();
                for (int s = 0; s < this.h_delOrg.size(); s++) {
                    String sid = h_delOrg.get(s).toString();
                    if(sid.contains("|")){
                        // 单个账号离职
                        String sqlTemp = "update hrmresource set status=5 where swshrguid = '{}'";
                        sql     = StrUtil.format(sqlTemp, h_delOrg.get(s));
                    }else{
                        // 主账号离职
                        String sqlTemp = "update hrmresource set status=5 where swshrguid like '{}|%'";
                               sql     = StrUtil.format(sqlTemp, h_delOrg.get(s));
                    }
                    sqlList.add(sql);

                    sulogrecord2  += h_delOrg.get(s) + "|离职成功,";
                    errlogRecord2 += h_delOrg.get(s) + "|离职失败,";
                }
                try {
                    ExecuteUtil.executeBatchSqlWithTrans(sqlList);
                    writeLog("离职人员 sql " + sqlList);

                    result += "<message value=\"1\">" + sulogrecord2 + "</message>";
                } catch (Exception e) {
                    writeLog("离职人员 异常 -- ");
                    writeLog(e);

                    result += "<message value=\"0\">" + errlogRecord2 + "</message>";
                }
            }
            // endregion 处理离职数据

            // region 主账号密码同步到次账号
            Map<String, String> workcodeMap = new HashMap<>();

            Iterator iter = this.h_orgInfo_add.values().iterator();
            while(iter.hasNext()){
                HrmResourceVoCust hrmvo = (HrmResourceVoCust) iter.next();

                String workcode = hrmvo.getWorkcode();
                if(!workcodeMap.containsKey(workcode)){
                    workcodeMap.put(workcode, workcode);
                }
            }

            Iterator iter2 = this.h_orgInfo_update.values().iterator();
            while(iter2.hasNext()){
                HrmResourceVoCust hrmvo = (HrmResourceVoCust) iter2.next();

                String workcode = hrmvo.getWorkcode();
                if(!workcodeMap.containsKey(workcode)){
                    workcodeMap.put(workcode, workcode);
                }
            }

            for(String key : workcodeMap.keySet()) {
                String updatePwd = StrUtil.format("UPDATE hrmresource SET password = (SELECT password FROM hrmresource WHERE workcode = '{}' AND accounttype = 0) WHERE workcode='{}' AND accounttype = 1", key, key);
                ExecuteUtil.executeSql(updatePwd);
            }
            // endregion 主账号密码同步到次账号

            // region 2021-10-19 次账号，关联上主账号信息 tangss
            for(String key : workcodeMap.keySet()) {
                String updateBelongto = StrUtil.format("UPDATE hrmresource SET belongto = (SELECT id FROM hrmresource WHERE workcode = '{}' AND accounttype = 0) WHERE workcode='{}' AND accounttype = 1", key, key);
                writeLog("次账号，关联上主账号信息 sql -- " + updateBelongto);
                ExecuteUtil.executeSql(updateBelongto);
            }
            // endregion 2021-10-19 次账号，关联上主账号信息 tangss

            // 所有主账号的belongto字段置为null
            String updateBelongto = "UPDATE hrmresource SET belongto = null WHERE accounttype = 0";
            ExecuteUtil.executeSql(updateBelongto);

        }catch (Exception e){
            writeLog(e);
            result += "<message value=\"0\">数据异常</message>";
        }
        result += "</result>";

        writeLog("result -- "+ result);
        return result;
    }

    /**
     * XFIRE获取客户端IP地址
     *
     * @return
     * <AUTHOR>
     */
    public String getClientIpXfire() {
        String ip = "";
        try {
            HttpServletRequest request = XFireServletController.getRequest();
            ip = getRemoteAddress(request);
            // writeLog("getClientIpXfire==IP=" + ip);
            return ip;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 在JSP里，获取客户端的IP地址的方法是：request.getRemoteAddr()，这种方法在大部分情况下都是有效的。
     * 但是在通过了Apache,Squid等反向代理软件就不能获取到客户端的真实IP地址了
     * 经过代理以后，由于在客户端和服务之间增加了中间层，因此服务器无法直接拿到客户端的IP，服务器端应用也无法直接通过转发请求的地址返回给客户端。
     * 但是在转发请求的HTTP头信息中，增加了X－FORWARDED－FOR信息。用以跟踪原有的客户端IP地址和原来客户端请求的服务器地址
     * 如果通过了多级反向代理的话，X-Forwarded-For的值并不止一个，而是一串IP值,
     * 取X-Forwarded-For中第一个非unknown的有效IP字符串。
     */
    private String getRemoteAddress(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
            ip = request.getRemoteAddr();
        }
        if ((ip.indexOf(",") >= 0)) {
            ip = ip.substring(0, ip.indexOf(","));
        }
        return ip;
    }
}
