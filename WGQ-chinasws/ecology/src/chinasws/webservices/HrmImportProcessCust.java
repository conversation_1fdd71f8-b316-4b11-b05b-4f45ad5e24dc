/**
 * Title:        人员数据解析适配器
 * Company:      泛微软件
 *
 * @author: 冯拥兵
 * @version: 1.0
 * create date : 2010-6-2
 * modify log:
 * Description:  对人员数据Map集合进行解析，并添加到数据库
 */

package chinasws.webservices;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.ArrayList;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.http.HttpSession;

import ln.LN;

import org.json.JSONObject;

import weaver.common.StringUtil;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.general.BaseBean;
import weaver.general.GCONST;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.hrm.common.DbFunctionUtil;
import weaver.hrm.company.DepartmentComInfo;
import weaver.hrm.company.SubCompanyComInfo;
import weaver.hrm.definedfield.HrmFieldManager;
import weaver.hrm.job.JobActivitiesComInfo;
import weaver.hrm.job.JobGroupsComInfo;
import weaver.hrm.job.JobTitlesComInfo;
import weaver.hrm.job.UseKindComInfo;
import weaver.hrm.location.LocationComInfo;
import weaver.hrm.passwordprotection.manager.HrmResourceManager;
import weaver.hrm.resource.ResourceComInfo;
import weaver.hrm.settings.ChgPasswdReminder;
import weaver.hrm.settings.RemindSettings;
import weaver.interfaces.hrm.HrmServiceManager;
import weaver.join.hrm.in.IHrmImportProcess;
import weaver.join.hrm.in.ImportLog;
import weaver.rtx.OrganisationCom;
import weaver.systeminfo.SystemEnv;
import weaver.systeminfo.setting.HrmUserSettingManager;
import weaver.interfaces.email.CoreMailAPI;
import weaver.toolbox.db.recordset.ExecuteUtil;

public class HrmImportProcessCust extends BaseBean implements IHrmImportProcess {

    // private RecordSet recordSet=new RecordSet();

    private Map<String, Integer> keyMap = new HashMap<String, Integer>(); // 看数据库是否有重复，将数据库现有的数据查出<id,workcode_lastname>

    private Map<String, Integer> certificateNums = new HashMap<String, Integer>();// 已存在的身份证

    private Map<String, JSONObject> baseTypeMap; // 基础信息自定义字段数据库类型与字段名称映射<fieldName,dbtype>

    private Map<String, JSONObject> personTypeMap; // 个人信息自定义字段数据库类型与字段名称映射<fieldName,dbtype>

    private Map<String, JSONObject> workTypeMap; // 工作信息自定义字段数据库类型与字段名称映射

    private String date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());

    private String logFile = ""; // 日志文件名

    // private StaticObj staticObj=StaticObj.getInstance();

    private CoreMailAPI coremailapi = null; // 添加到CoreMail

    private OrganisationCom rtxService = null; // 添加到rtx

    private int userlanguage = 7; // 登录语言

    char separator = Util.getSeparator();

    LN license = new LN(); // license

    String keyField = "";

    private int scCount = 0;

    private HrmResourceVoCust vo = null;

    private String multilanguage;

    private Map<String, Integer> sysLanguage = null;

    private Map<String, Integer> jobcallMap = null;
    private Map<String, Integer> locationMap = null;
    private Map<String, Integer> usekindMap = null;
    private Map<String, Integer> educationlevelMap = null;

    private int cnLanguageId = 7;

    public HrmImportProcessCust() {
        final int F_Y = 0;
        final int F_N = 1;
        int type = F_N;
        LN license = new LN();
        license.InLicense();
        type = StringUtil.parseToInt(license.getScType(), F_N);
        scCount = StringUtil.parseToInt(license.getScCount(), 0);
        scCount = type == F_Y ? (scCount < 0 ? 0 : scCount) : 0;
        RecordSet recordSet = new RecordSet();

        String sql = "select multilanguage,(select id from syslanguage where language='简体中文' or language='中文') as cnLanguageId from license ";
        recordSet.execute(sql);
        recordSet.next();
        multilanguage = recordSet.getString("multilanguage");
        cnLanguageId = recordSet.getInt(2);

        sysLanguage = new HashMap<String, Integer>();
        recordSet.execute("select id,language from syslanguage");
        while (recordSet.next()) {
            sysLanguage.put(recordSet.getString(2), recordSet.getInt(1));
        }

        educationlevelMap = new HashMap<String, Integer>();
        recordSet.execute("select id,name from HrmEducationLevel");
        while (recordSet.next()) {
            educationlevelMap.put(recordSet.getString(2), recordSet.getInt(1));
        }

        jobcallMap = new HashMap<String, Integer>();
        recordSet.execute("select id,name from HrmJobCall");
        while (recordSet.next()) {
            jobcallMap.put(recordSet.getString(2), recordSet.getInt(1));
        }

        locationMap = new HashMap<String, Integer>();
        recordSet.execute("select id,locationname from HrmLocations where countryid=1");
        while (recordSet.next()) {
            locationMap.put(recordSet.getString(2), recordSet.getInt(1));
        }

        usekindMap = new HashMap<String, Integer>();
        recordSet.execute("select id,name from HrmUseKind");
        while (recordSet.next()) {
            usekindMap.put(recordSet.getString(2), recordSet.getInt(1));
        }
    }

    /**
     * 对人员数据进行解析，并添加到数据库
     *
     * @param keyField   重复性验证字段
     * @param hrMap      人员数据Map集合
     * @param importType 导入类型 添加|更新 add|update
     * @return List
     */
    @Override
    public List processMap(String keyField, Map hrMap, String importType) {// 增加同步锁，防止同时多次调用
        return processMap(keyField, hrMap, importType, null);
    }

    /**
     * 对人员数据进行解析，并添加到数据库
     *
     * @param keyField   重复性验证字段
     * @param hrMap      人员数据Map集合
     * @param importType 导入类型 添加|更新 add|update
     * @return List
     */
    public List processMap(String keyField, Map hrMap, String importType, HttpSession session) { // 增加同步锁，防止同时多次调用
        List<ImportLog> resultList = new ArrayList<ImportLog>(); // 导入结果
        RecordSet recordSet = new RecordSet();

        try {

            String subCompanyName = ""; // 分部名称
            String departmentName = ""; // 部门名称
            int subcompanyid1 = 0; // 分部Id
            int departmentid = 0; // 部门id
            String key = ""; // workcode_lastname 组合
            getKeyMap(keyField); // 获取重复性验证字段Map
            this.keyField = keyField;
            int id = 0;
            if (session != null) {
                session.setAttribute("importStatus", "importing");
            }
            rtxService = new OrganisationCom();
            coremailapi = CoreMailAPI.getInstance();

            final ResourceComInfo resourcecominfo = new ResourceComInfo();
            DepartmentComInfo departmentComInfo = new DepartmentComInfo();
            SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
            JobActivitiesComInfo jobActivitiesComInfo = new JobActivitiesComInfo();
            JobTitlesComInfo jobTitlesComInfo = new JobTitlesComInfo();
            JobGroupsComInfo jobGroupsComInfo = new JobGroupsComInfo();
            LocationComInfo locationComInfo = new LocationComInfo();
            UseKindComInfo useKindComInfo = new UseKindComInfo();

            Set keySet = hrMap.keySet();
            Object keyArray[] = keySet.toArray();

            Class hrmClass = HrmResourceCust.class;
            Class voClass = HrmResourceVoCust.class;

            String field = "pinyinlastname,ecology_pinyin_search,dsporder,swshrguid,id,"
                    + "subcompanyid1,departmentid,workcode,lastname,"
                    + "loginid,password,seclevel,sex,jobtitle,jobcall,joblevel,"
                    + "jobactivitydesc,managerid,assistantid,status,locationid,workroom,"
                    + "telephone,mobile,mobilecall,fax,email,"
                    + "systemlanguage,birthday,folk,nativeplace,regresidentplace,"
                    + "certificatenum,maritalstatus,policy,bememberdate,bepartydate,"
                    + "islabouunion,educationlevel,degree,healthinfo,height,weight,"
                    + "usekind,startdate,enddate,probationenddate,"// add by lvyi
                    + "residentplace,homeaddress,tempresidentnumber,"
                    + "datefield1,datefield2,datefield3,datefield4,datefield5,"
                    + "textfield1,textfield2,textfield3,textfield4,textfield5,"
                    + "numberfield1,numberfield2,numberfield3,numberfield4,numberfield5,"
                    + "tinyintfield1,tinyintfield2,tinyintfield3,tinyintfield4,tinyintfield5";

            String fields[] = field.split(",");

            User user = null;
            if (session != null) {
                user = (User) session.getAttribute("weaver_user@bean");
            }

            int createrid = 1; // 创建者id
            int lastmodid = 1; // 最后修改者id
            if (user != null) {
                createrid = user.getUID();
                lastmodid = user.getUID();
            }

            String createdate = date; // 创建时间
            String lastmoddate = date; // 最后修改时间
            String lastlogindate = date; // 最后登录时间

            Object obj;
            HrmResourceCust hrm;
            for (int i = 0; i < keyArray.length; i++) {
                obj = keyArray[i];
                vo = (HrmResourceVoCust) hrMap.get(obj);
                key = (String) obj;
                hrm = new HrmResourceCust();

                try { // 异常处理

                    if (importType.equals("add")) {

                        if (keyMap.get(key) != null) {
                            resultList.add(createLog(vo, "创建", "失败#01", SystemEnv.getHtmlLabelName(83520, userlanguage)));
                            if (session != null) {
                                session.setAttribute("resultList", resultList);
                            }
                            continue;
                        }
                        
                        // 添加人员信息 license 人员上限判断
                        if (keyMap.get(key) == null) {
                            if (license.CkHrmnum() >= 0) {
                                resultList.add(
                                        createLog(vo, "创建", "失败#02", SystemEnv.getHtmlLabelName(83522, userlanguage)));
                                if (session != null) {
                                    session.setAttribute("resultList", resultList);
                                }
                                continue;
                            }
                        }

                        // hr系统标识
                        hrm.setSwshrguid(vo.getSwshrguid());

                        // 分部Id
                        String subCompanyNames = vo.getSubcompanyid1();
                        String tempSubCompanyName = "";
                        if (subCompanyNames != null && !subCompanyNames.equals("")) {
                            if (!subCompanyNames.equals(subCompanyName)) {
                                tempSubCompanyName = subCompanyName;
                                subCompanyName = subCompanyNames;
                                subcompanyid1 = getSubCompanyId(subCompanyName);
                                //writeLog("getSubCompanyId 297 -- " + subcompanyid1);
                            }
                            if (subcompanyid1 == -9) {
                                resultList.add(createLog(vo, "创建", "失败#03", SystemEnv.getErrorMsgName(56, userlanguage)));
                                if (session != null) {
                                    session.setAttribute("resultList", resultList);
                                }
                                continue;
                            }
                            if (subcompanyid1 == -2) {
                                resultList.add(
                                        createLog(vo, "创建", "失败#04", SystemEnv.getHtmlLabelName(126274, userlanguage)));
                                if (session != null) {
                                    session.setAttribute("resultList", resultList);
                                }
                                continue;
                            }
                            if (subcompanyid1 == -1) {
                                resultList.add(
                                        createLog(vo, "创建", "失败#05", SystemEnv.getHtmlLabelName(83524, userlanguage)));
                                if (session != null){
                                    session.setAttribute("resultList", resultList);
                                }
                                continue;
                            }
                            if (subcompanyid1 == 0) {
                                resultList.add(
                                        createLog(vo, "创建", "失败#06", SystemEnv.getHtmlLabelName(83536, userlanguage)));
                                if (session != null) {
                                    session.setAttribute("resultList", resultList);
                                }
                                continue;
                            }
                            if (StringUtil.parseToInt(license.getConcurrentFlag(), 0) != 1) {
                                if (new HrmResourceManager().noMore(String.valueOf(subcompanyid1))) {
                                    resultList.add(createLog(vo, "创建", "失败#07", SystemEnv.getHtmlLabelName(83525, userlanguage)));
                                    if (session != null) {
                                        session.setAttribute("resultList", resultList);
                                    }
                                    continue;
                                }
                            }
                            hrm.setSubcompanyid1(new Integer(subcompanyid1));
                        } else {
                            resultList.add(createLog(vo, "创建", "失败#08", SystemEnv.getHtmlLabelName(83526, userlanguage)));
                            if (session != null) {
                                session.setAttribute("resultList", resultList);
                            }
                            continue;
                        }

                        // 部门id
                        String departmentNames = vo.getDepartmentid();
                        if (departmentNames != null) {
                            if (!subCompanyNames.equals(tempSubCompanyName)
                                    || !departmentNames.equals(departmentName)) {
                                departmentName = departmentNames;
                                departmentid = getDeptId(departmentName, subcompanyid1);
                            }
                            if (departmentid == 0) {
                                resultList.add(
                                        createLog(vo, "创建", "失败#09", SystemEnv.getHtmlLabelName(83537, userlanguage)));
                                if (session != null){
                                    session.setAttribute("resultList", resultList);
                                }
                                continue;
                            }
                            if (departmentid == -2) {
                                resultList.add(
                                        createLog(vo, "创建", "失败#10", SystemEnv.getHtmlLabelName(126275, userlanguage)));
                                if (session != null){
                                    session.setAttribute("resultList", resultList);
                                }
                                continue;
                            }
                            hrm.setDepartmentid(new Integer(departmentid));
                        } else {
                            resultList.add(createLog(vo, "创建", "失败#11", SystemEnv.getHtmlLabelName(83527, userlanguage)));
                            if (session != null) {
                                session.setAttribute("resultList", resultList);
                            }
                            continue;
                        }

                        if (vo.getLastname() == null) {
                            resultList.add(createLog(vo, "创建", "失败#12", SystemEnv.getHtmlLabelName(83529, userlanguage)));
                            if (session != null) {
                                session.setAttribute("resultList", resultList);
                            }
                            continue;
                        }

                        // 岗位id
                        //writeLog("vo:岗位 -- " + com.alibaba.fastjson.JSONObject.toJSONString(vo));
                        if (vo.getJobtitle() != null && vo.getJobactivityid() != null && vo.getJobgroupid() != null) {
                            int jobtitle = getJobTitles(vo.getJobtitle(), vo.getJobactivityid(), vo.getJobgroupid());
                            hrm.setJobtitle(new Integer(jobtitle));
                        } else {
                            resultList.add(createLog(vo, "创建", "失败#13", SystemEnv.getHtmlLabelName(83531, userlanguage)));
                            if (session != null) {
                                session.setAttribute("resultList", resultList);
                            }
                            continue;
                        }

                        // 上级id
                        if (vo.getManagerid() != null) {
                            int managerid = 0; // 上级Id
                            String managerstr = ""; // 所有上级
                            Map managerInfo = getManagerIdAndStr("", vo.getManagerid(), keyField);
                            if (managerInfo != null) {
                                managerid = managerInfo.get("managerid") != null
                                        ? ((Integer) managerInfo.get("managerid")).intValue()
                                        : 0;
                                managerstr = (String) (managerInfo.get("managerstr") != null
                                        ? managerInfo.get("managerstr")
                                        : "");
                            }
                            hrm.setManagerid(new Integer(managerid));
                            hrm.setManagerstr(managerstr);

                            // 如果vo.getManagerid()有值，但manageid未找到，说明填写有误
                            if (!vo.getManagerid().equals("") && managerid == 0) {
                                resultList.add(
                                        createLog(vo, "创建", "失败#14", SystemEnv.getHtmlLabelName(83532, userlanguage)));
                                if (session != null) {
                                    session.setAttribute("resultList", resultList);
                                }
                                continue;
                            }
                        } else {

                            hrm.setManagerid(new Integer(0));
                            hrm.setManagerstr("");
                        }

                        // 助理id
                        if (vo.getAssistantid() != null) {
                            int assistantid = 0;
                            assistantid = getAssistantid(vo.getAssistantid(), keyField);
                            hrm.setAssistantid(new Integer(assistantid));
                            if (!vo.getAssistantid().equals("") && assistantid == 0) {
                                resultList.add(createLog(vo, "创建", "失败#15", SystemEnv.getHtmlLabelName(24678, userlanguage)));
                                if (session != null) {
                                    session.setAttribute("resultList", resultList);
                                }
                                continue;
                            }
                        }

                        // 办公地点
                        if (vo.getLocationid() != null) {
                            int locationid = getLocationid(vo.getLocationid());
                            hrm.setLocationid(new Integer(locationid));
                        } else {
                            resultList.add(createLog(vo, "创建", "失败#16", SystemEnv.getHtmlLabelName(83533, userlanguage)));
                            if (session != null) {
                                session.setAttribute("resultList", resultList);
                            }
                            continue;
                        }
                        // 邮箱
                        if (vo.getEmail() != null) {
                            if (!"".equals(vo.getEmail())) {
                                Pattern pattern = Pattern.compile(
                                        "^([a-zA-Z0-9_\\-\\.]+)@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.)|(([a-zA-Z0-9\\-]+\\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\\]?)$");
                                Matcher matcher = pattern.matcher(vo.getEmail());
                                if (!matcher.matches()) {
                                    resultList.add(createLog(vo, "创建", "失败#17", SystemEnv.getHtmlLabelName(24570, userlanguage)));
                                    if (session != null) {
                                        session.setAttribute("resultList", resultList);
                                    }
                                    continue;
                                }
                            }
                        }

                        // 性别，为空或其他情况统一为0(男)
                        String sex = "女".equals(vo.getSex()) ? "1" : "0";
                        hrm.setSex(sex);

                        // 职称
                        if (vo.getJobcall() != null) {
                            int jobcall = getJobcall(vo.getJobcall());
                            hrm.setJobcall(new Integer(jobcall));
                        }
                        // 系统语言，默认简体中文
                        if (vo.getSystemlanguage() != null) {
                            int systemlanguage = getSystemlanguage(vo.getSystemlanguage());

                            if (systemlanguage == -1) { // 只能选择简体中文|Englisth|繁体中文
                                resultList.add(
                                        createLog(vo, "创建", "失败#18", SystemEnv.getHtmlLabelName(83534, userlanguage)));
                                if (session != null) {
                                    session.setAttribute("resultList", resultList);
                                }
                                continue;
                            }
                            if (systemlanguage == 0) { // 系统不支持多语言
                                resultList.add(
                                        createLog(vo, "创建", "失败#19", SystemEnv.getHtmlLabelName(84810, userlanguage)));
                                if (session != null) {
                                    session.setAttribute("resultList", resultList);
                                }
                                continue;
                            }
                            hrm.setSystemlanguage(new Integer(systemlanguage));
                        } else {
                            hrm.setSystemlanguage(new Integer(7));
                        }

                        // 婚姻状况，如果不是以下选项，则默认为未婚
                        String maritalstatus = "已婚".equals(vo.getMaritalstatus()) ? "1"
                                : "离异".equals(vo.getMaritalstatus()) ? "2" : "0";
                        hrm.setMaritalstatus(maritalstatus);

                        // 员工状态
                        if (!"".equals(Util.null2String(vo.getStatus()))) {
                            int status = getStatus(vo.getStatus());
                            hrm.setStatus(new Integer(status));
                        } else {
                            hrm.setStatus(new Integer(1));
                        }

                        // 学历
                        if (vo.getEducationlevel() != null) {
                            int educationlevel = getEducationlevel(vo.getEducationlevel());
                            hrm.setEducationlevel(new Integer(educationlevel));
                        }

                        // 工会会员,默认为是
                        String islabouunion = "是".equals(vo.getIslabouunion()) ? "1" : "0";
                        hrm.setIslabouunion(islabouunion);

                        // 健康状况
                        if (vo.getHealthinfo() != null) {
                            String healthinfo = getHealthinfo(vo.getHealthinfo());
                            hrm.setHealthinfo(healthinfo);
                        } else {
                            hrm.setHealthinfo("0");
                        }

                        // 安全等级
                        if (isInteger(vo.getSeclevel())) {
                            int seclevel = vo.getSeclevel().equals("") ? 0 : Integer.parseInt(vo.getSeclevel());
                            hrm.setSeclevel(new Short((short) (seclevel)));
                        } else {
                            hrm.setSeclevel(new Short((short) (0)));
                        }

                        // 职级
                        if (isInteger(vo.getJoblevel())) {
                            int joblevel = vo.getJoblevel().equals("") ? 0 : Integer.parseInt(vo.getJoblevel());
                            hrm.setJoblevel(new Short((short) joblevel));
                        } else {
                            hrm.setJoblevel(new Short((short) 0));
                        }
                        // 用工性质
                        if (vo.getUsekind() != null) {
                            int usekind = getUseKind(vo.getUsekind());
                            hrm.setUsekind(usekind);
                        } else {
                            hrm.setUsekind(0);
                        }
                        // 如果新增导入的时候，登陆名不为空，需要校验一下当前的license
                        if (vo.getLoginid() != null) {
                            license.reloadLicenseInfo();
                            if (license.CkHrmnum() >= 0) {
                                resultList.add(createLog(vo, "创建", "失败#20", SystemEnv.getHtmlLabelName(83522, userlanguage)));
                                if (session != null) {
                                    session.setAttribute("resultList", resultList);
                                }
                                continue;
                            }
                        }

                        // id，非自动增长
                        recordSet.executeProc("HrmResourceMaxId_Get", "");
                        recordSet.next();
                        id = recordSet.getInt(1);
                        hrm.setId(new Integer(id));

                        // 密码 加密
                        String password_tmp = Util.null2String(vo.getPassword()).trim();
                        if ("".equals(password_tmp)) {
                            password_tmp = "1";
                        }
                        String password = Util.getEncrypt(password_tmp);
                        hrm.setPassword(password);

                        boolean flag = true;

                        /* 添加人员信息 */
                        String insertStr = "insert into HrmResource(";
                        String insertFields = "";
                        String insertValues = "";

                        for (int k = 0; k < fields.length; k++) {
                            Field hrmField = hrmClass.getDeclaredField(fields[k]);
                            Field voField = voClass.getDeclaredField(fields[k]);

                            String hrmFieldType = hrmField.getType().getName();
                            String voFieldType = voField.getType().getName();

                            hrmField.setAccessible(true);
                            voField.setAccessible(true);

                            // 首先取hrm对象中的数据，没有去vo中的
                            if (hrmField.get(hrm) != null) {
                                if (hrmFieldType.endsWith("String")) {
                                    insertFields = insertFields + fields[k] + ",";
                                    insertValues = insertValues + "'" + hrmField.get(hrm) + "',";
                                } else if (hrmFieldType.endsWith("Integer") || hrmFieldType.endsWith("Short")
                                        || hrmFieldType.endsWith("Float")) {
                                    insertFields = insertFields + fields[k] + ",";
                                    insertValues = insertValues + "" + hrmField.get(hrm) + ",";
                                }
                            } else if (voField.get(vo) != null) {
                                if (voFieldType.endsWith("String")) {
                                    insertFields = insertFields + fields[k] + ",";
                                    insertValues = insertValues + "'" + voField.get(vo) + "',";
                                } else if (hrmFieldType.endsWith("Integer") || hrmFieldType.endsWith("Short")
                                        || hrmFieldType.endsWith("Float")) {
                                    insertFields = insertFields + fields[k] + ",";
                                    insertValues = insertValues + "" + voField.get(vo) + ",";
                                }
                            }
                        }
                        ChgPasswdReminder cpr = new ChgPasswdReminder();
                        RemindSettings hrmsettings = cpr.getRemindSettings();

                        //账号类型
                        String accountType = "1".equals(vo.getResidentplace()) ? "0" : "1";

                        insertStr = insertStr + insertFields
                                + "createrid,createdate,lastmodid,lastmoddate,lastlogindate,managerstr,accounttype,mobileshowtype ,"
                                + DbFunctionUtil.getInsertColumnSql() + ") values(" + insertValues + createrid + ",'"
                                + createdate + "'," + lastmodid + ",'" + lastmoddate + "','" + lastlogindate + "','"
                                + hrm.getManagerstr() + "'," + accountType + ","
                                + Util.getIntValue(hrmsettings.getMobileShowTypeDefault(), 0) + " ,"
                                + DbFunctionUtil.getInsertColumnValueSql(recordSet.getDBType(), lastmodid) + ")";

                        boolean resourceInsertFlag = true;
                        if (!execSql(insertStr)) {// 添加人员信息
                            flag = false;
                            resourceInsertFlag = false;
                        }
                        if (resourceInsertFlag) {// 仅当人员插入成功后才进行自定义字段操作
                            if (!updateBaseData(vo.getBaseFields(), vo.getBaseFieldsValue(), id)) // 添加基础字段信息
                            {
                                flag = false;
                            }
                            if (!updatePersonData(vo.getPersonFields(), vo.getPersonFieldsValue(), id)) // 添加个人字段信息
                            {
                                flag = false;
                            }
                            if (!updateWorkData(vo.getWorkFields(), vo.getWorkFieldsValue(), id)) // 添加工作字段信息
                            {
                                flag = false;
                            }
                        }

                        /* 添加人员缓存，人员默认按id显示顺序,HrmResource_Trigger_Insert 人员共享 入职维护项目状态 */
                        if (flag) {
                            // --
                            HrmUserSettingManager.checkUserSettingInit(id);

                            // 人员显示顺序
                            //String taxissql = ("update HrmResource set dsporder = " + id + " where id = " + id);
                            //recordSet.executeSql(taxissql);

                            // 触发器
                            String para = "" + id + separator + hrm.getManagerid() + separator + hrm.getDepartmentid()
                                    + separator + hrm.getSubcompanyid1() + separator + hrm.getSeclevel() + separator
                                    + hrm.getManagerstr();
                            recordSet.executeProc("HrmResource_Trigger_Insert", para);

                            // 共享信息
                            /*
                             * String p_para = "" + id + separator + hrm.getDepartmentid() + separator +
                             * hrm.getSubcompanyid1() + separator + hrm.getManagerid() + separator +
                             * hrm.getSeclevel() + separator + hrm.getManagerstr() + separator + "0" +
                             * separator + "0" + separator + "0" + separator + "0" + separator + "0" +
                             * separator + "0"; recordSet.executeProc("HrmResourceShare", p_para);
                             */

                            // 入职维护状态
                            String sql_1 = ("insert into HrmInfoStatus (itemid,hrmid,status) values(1," + id + ",1)");
                            recordSet.executeSql(sql_1);
                            String sql_2 = ("insert into HrmInfoStatus (itemid,hrmid) values(2," + id + ")");
                            recordSet.executeSql(sql_2);
                            String sql_3 = ("insert into HrmInfoStatus (itemid,hrmid) values(3," + id + ")");
                            recordSet.executeSql(sql_3);

                            String sql_10 = ("insert into HrmInfoStatus (itemid,hrmid) values(10," + id + ")");
                            recordSet.executeSql(sql_10);

                        }

                        /* 写日志 */
                        if (flag) {
                            resultList.add(createLog(vo, "创建", "成功", ""));
                            if (session != null) {
                                session.setAttribute("resultList", resultList);
                            }
                            if (!"".equals(vo.getLoginid())) {
                                rtxService.addUser(id);// 添加到rtx
                                coremailapi.synUser("" + id);// 添加到CoreMail
                            }
                        } else {
                            resultList.add(createLog(vo, "创建", "失败#21", ""));
                            if (session != null) {
                                session.setAttribute("resultList", resultList);
                            }
                        }

                    } else { // 更新人员

                        writeLog("key ===> " + key);

                        if (keyMap.get(key) == null) {
                            // 如果是更新，并且没有找到swshrguid，说明是主账号更换了岗位
                            // 直接数据库查找主账号是否存在
                            RecordSet recordSet1 = new RecordSet();
                            String hrid = key.split("\\|")[0] + "|";
                            String qry = "select id,swshrguid from hrmresource where (accounttype is null or accounttype = 0) and loginid like '" + hrid + "%'";
                            recordSet1.execute(qry);
                            if(recordSet1.next()){
                                int userid = recordSet1.getInt("id");
                                keyMap.put(key, userid);
                            }else{
                                resultList.add(createLog(vo, "更新", "失败#30", SystemEnv.getHtmlLabelName(83535, userlanguage)));
                                if (session != null) {
                                    session.setAttribute("resultList", resultList);
                                }

                                continue;
                            }
                        }

                        final String hrmId = String.valueOf(keyMap.get(key));

                        writeLog("hrmId ===> " +hrmId);

                        // 分部id
                        String subCompanyNames = vo.getSubcompanyid1();
                        writeLog("subCompanyNames ===> " + subCompanyNames);
                        boolean issameSub = true;
                        if (!"".equals(Util.null2String(subCompanyNames))) {
                            if (!subCompanyNames.equals(subCompanyName)) {
                                subCompanyName = subCompanyNames;
                                subcompanyid1 = getSubCompanyId(subCompanyName);
                                writeLog("getSubCompanyId ===> " + subcompanyid1);
                                issameSub = false;

                            }

                            if (subcompanyid1 == -9) {
                                resultList.add(createLog(vo, "更新", "失败#31", SystemEnv.getErrorMsgName(56, userlanguage)));
                                if (session != null){
                                    session.setAttribute("resultList", resultList);
                                }
                                continue;
                            }

                            if (subcompanyid1 == -2) {
                                resultList.add(createLog(vo, "创建", "失败#32", SystemEnv.getHtmlLabelName(126274, userlanguage)));
                                if (session != null){
                                    session.setAttribute("resultList", resultList);
                                }
                                continue;
                            }

                            if (subcompanyid1 == 0 || subcompanyid1 == -1) {
                                resultList.add(createLog(vo, "更新", "失败#33",
                                        subcompanyid1 == 0 ? SystemEnv.getHtmlLabelName(83536, userlanguage)
                                                : SystemEnv.getHtmlLabelName(83524, userlanguage)));
                                if (session != null){
                                    session.setAttribute("resultList", resultList);
                                }
                                continue;
                            }

                            // qc139575 在验证并发非并发之前需要先CkHrmnum
                            license.CkHrmnum();
                            if (StringUtil.parseToInt(license.getConcurrentFlag(), 0) != 1) {
                                if (!issameSub && new HrmResourceManager().noMore(String.valueOf(subcompanyid1))) {
                                    resultList.add(createLog(vo, "更新", "失败#34", SystemEnv.getHtmlLabelName(83525, userlanguage)));
                                    if (session != null){
                                        session.setAttribute("resultList", resultList);
                                    }
                                    continue;
                                }
                            }
                            hrm.setSubcompanyid1(new Integer(subcompanyid1));
                        } else {
                            String subCompanyID = resourcecominfo.getSubCompanyID(hrmId);
                            int intSubcompanyid1 = new Integer(Integer.parseInt(subCompanyID));
                            if (intSubcompanyid1 == -9) {
                                resultList.add(createLog(vo, "更新", "失败#35", SystemEnv.getErrorMsgName(56, userlanguage)));
                                if (session != null){
                                    session.setAttribute("resultList", resultList);
                                }
                                continue;
                            }

                            if (intSubcompanyid1 == -2) {
                                resultList.add(createLog(vo, "创建", "失败#36", SystemEnv.getHtmlLabelName(126274, userlanguage)));
                                if (session != null){
                                    session.setAttribute("resultList", resultList);
                                }
                                continue;
                            }

                            if (intSubcompanyid1 == 0 || intSubcompanyid1 == -1) {
                                resultList.add(createLog(vo, "更新", "失败#37",
                                        intSubcompanyid1 == 0 ? SystemEnv.getHtmlLabelName(83536, userlanguage)
                                                : SystemEnv.getHtmlLabelName(83524, userlanguage)));
                                if (session != null){
                                    session.setAttribute("resultList", resultList);
                                }
                                continue;
                            }

                            hrm.setSubcompanyid1(intSubcompanyid1);
                            String subIdStr = subCompanyComInfo.getAllSupCompany(subCompanyID);
                            subCompanyNames = "";
                            if (!subIdStr.equals("")) {
                                String subIds[] = subIdStr.split(",");
                                for (int n = 0; n < subIds.length; n++){
                                    subCompanyNames = subCompanyNames + subCompanyComInfo.getSubCompanyname(subIds[n]) + ">";
                                }
                            }
                            vo.setSubcompanyid1(subCompanyNames + subCompanyComInfo.getSubCompanyname(subCompanyID));

                        }

                        // 部门id
                        String departmentNames = vo.getDepartmentid();
                        writeLog("departmentNames ===> ",departmentNames);
                        if (!"".equals(Util.null2String(departmentNames))) { // 部门存在时
                            if (!issameSub || !departmentNames.equals(departmentName)) {
                                departmentName = departmentNames;
                                departmentid = getDeptId(departmentName, subcompanyid1);

                                if (departmentid == 0) {
                                    resultList.add(
                                            createLog(vo, "更新", "失败#38", SystemEnv.getHtmlLabelName(83537, userlanguage)));
                                    if (session != null){
                                        session.setAttribute("resultList", resultList);
                                    }
                                    continue;
                                }
                                if (departmentid == -2) {
                                    resultList.add(createLog(vo, "更新", "失败#39",
                                            SystemEnv.getHtmlLabelName(126275, userlanguage)));
                                    if (session != null){
                                        session.setAttribute("resultList", resultList);
                                    }
                                    continue;
                                }
                            } else { // add sjh 50102需要考虑不同公司名称相同部门架构情况 此时部门ID是不同的
                                departmentid = getDeptId(departmentName, subcompanyid1);
                                if (departmentid == 0) {
                                    resultList.add(
                                            createLog(vo, "更新", "失败#40", SystemEnv.getHtmlLabelName(83537, userlanguage)));
                                    if (session != null){
                                        session.setAttribute("resultList", resultList);
                                    }
                                    continue;
                                }
                                if (departmentid == -2) {
                                    resultList.add(createLog(vo, "更新", "失败#41",
                                            SystemEnv.getHtmlLabelName(126275, userlanguage)));
                                    if (session != null){
                                        session.setAttribute("resultList", resultList);
                                    }
                                    continue;
                                }
                            }
                            hrm.setDepartmentid(new Integer(departmentid));
                        } else { // 部门不存在
                            String departmentID = resourcecominfo.getDepartmentID(hrmId);
                            hrm.setDepartmentid(new Integer(Integer.parseInt(departmentID)));
                            departmentNames = "";
                            String supdeptid = departmentComInfo.getDepartmentsupdepid(departmentID);
                            while (!supdeptid.equals("")) {
                                departmentNames = departmentNames + departmentComInfo.getDepartmentname(supdeptid)
                                        + ",";
                                supdeptid = departmentComInfo.getDepartmentsupdepid(supdeptid);
                            }
                            String deptNamesArray[] = departmentNames.split(",");
                            departmentNames = "";
                            for (int n = deptNamesArray.length - 1; n >= 0; n--) {
                                departmentNames = departmentNames + deptNamesArray[n] + ">";
                            }
                            departmentNames = departmentNames + departmentComInfo.getDepartmentname(departmentID);
                            vo.setDepartmentid(departmentNames);
                        }

                        // 岗位id
                        if ("".equals(Util.null2String(vo.getJobtitle()))
                                && (!"".equals(Util.null2String(vo.getJobactivityid()))
                                || !"".equals(Util.null2String(vo.getJobgroupid())))) {
                            resultList.add(createLog(vo, "更新", "失败#42", SystemEnv.getHtmlLabelName(83544, userlanguage)));
                            if (session != null){
                                session.setAttribute("resultList", resultList);
                            }
                            continue;
                        }
                        if (!"".equals(Util.null2String(vo.getJobtitle()))) {
                            String jobtitle = "";
                            String jobactivityid = "";
                            String jobactivityname = "";
                            String jobgroupid = "";
                            String jobgroupname = "";
                            if (vo.getJobactivityid() == null) {
                                jobtitle = resourcecominfo.getJobTitle(hrmId);
                                jobactivityid = jobTitlesComInfo.getJobactivityid(jobtitle);
                                jobactivityname = jobActivitiesComInfo.getJobActivitiesname(jobactivityid);
                            } else{
                                jobactivityname = vo.getJobactivityid();
                            }

                            if (vo.getJobgroupid() == null) {
                                jobtitle = resourcecominfo.getJobTitle(hrmId);
                                jobactivityid = jobTitlesComInfo.getJobactivityid(jobtitle);
                                jobgroupid = jobActivitiesComInfo.getJobgroupid(jobactivityid);
                                jobgroupname = jobGroupsComInfo.getJobGroupsname(jobgroupid);
                            } else {
                                jobgroupname = vo.getJobgroupid();
                            }
                            int jobtitleid = getJobTitles(vo.getJobtitle(), jobactivityname, jobgroupname);
                            hrm.setJobtitle(new Integer(jobtitleid));
                        }

                        // 上级id
                        String managerstr = ""; // 所有上级
                        String oldmanagerstr = resourcecominfo.getManagersIDs(hrmId);// 原来的所有上级序列
                        if (!"".equals(Util.null2String(vo.getManagerid()))) {
                            int managerid = 0; // 上级Id

                            Map managerInfo = getManagerIdAndStr(hrmId, vo.getManagerid(), keyField);
                            if (managerInfo != null) {
                                managerid = managerInfo.get("managerid") != null
                                        ? ((Integer) managerInfo.get("managerid")).intValue()
                                        : 0;
                                managerstr = (String) (managerInfo.get("managerstr") != null
                                        ? managerInfo.get("managerstr")
                                        : "");
                            }

                            // 如果vo.getManagerid()有值，但manageid未找到，说明填写有误
                            if (vo.getManagerid() != null && !vo.getManagerid().equals("") && managerid == 0) {
                                resultList.add(
                                        createLog(vo, "更新", "失败#43", SystemEnv.getHtmlLabelName(83532, userlanguage)));
                                if (session != null){
                                    session.setAttribute("resultList", resultList);
                                }
                                continue;
                            }
                            hrm.setManagerid(new Integer(managerid));
                            hrm.setManagerstr(managerstr);
                        } else {
                            if (vo.getManagerid() == null) {
                                hrm.setManagerid(
                                        new Integer(StringUtil.parseToInt(resourcecominfo.getManagerID(hrmId), 0)));
                                hrm.setManagerstr(resourcecominfo.getManagersIDs(hrmId));
                                managerstr = resourcecominfo.getManagersIDs(hrmId);
                                vo.setManagerid(resourcecominfo.getManagerID(hrmId));
                            } else {
                                hrm.setManagerid(new Integer(0));
                                hrm.setManagerstr("");
                            }
                        }
                        // 助理id
                        if (!"".equals(Util.null2String(vo.getAssistantid()))) {
                            int assistantid = 0;
                            assistantid = getAssistantid(vo.getAssistantid(), keyField);
                            if (vo.getAssistantid() != null && !vo.getAssistantid().equals("") && assistantid == 0) {
                                resultList.add(createLog(vo, "更新", "失败#44", SystemEnv.getHtmlLabelName(24678, userlanguage)));
                                if (session != null) {
                                    session.setAttribute("resultList", resultList);
                                }
                                continue;
                            }
                            hrm.setAssistantid(new Integer(assistantid));
                        }
                        // 办公地点
                        if (!"".equals(Util.null2String(vo.getLocationid()))) {
                            int locationid = getLocationid(vo.getLocationid());
                            hrm.setLocationid(new Integer(locationid));
                        }

                        if (vo.getEmail() != null) {
                            if (!"".equals(vo.getEmail())) {
                                Pattern pattern = Pattern.compile(
                                        "^([a-zA-Z0-9_\\-\\.]+)@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.)|(([a-zA-Z0-9\\-]+\\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\\]?)$");
                                Matcher matcher = pattern.matcher(vo.getEmail());
                                if (!matcher.matches()) {
                                    resultList.add(createLog(vo, "更新", "失败#45", SystemEnv.getHtmlLabelName(24570, userlanguage)));
                                    if (session != null) {
                                        session.setAttribute("resultList", resultList);
                                    }
                                    continue;
                                }
                            }
                        }

                        // 职称
                        if (!"".equals(Util.null2String(vo.getJobcall()))) {
                            int jobcall = getJobcall(vo.getJobcall());
                            hrm.setJobcall(new Integer(jobcall));
                        }
                        // 系统语言，默认简体中文
                        if (!"".equals(Util.null2String(vo.getSystemlanguage()))) {
                            int systemlanguage = getSystemlanguage(vo.getSystemlanguage());

                            if (systemlanguage == -1) { // 只能选择简体中文|Englisth|繁体中文
                                resultList.add(createLog(vo, "更新", "失败#46", SystemEnv.getHtmlLabelName(83534, userlanguage)));
                                if (session != null){
                                    session.setAttribute("resultList", resultList);
                                }
                                continue;
                            }
                            // 系统不支持多语言
                            if (systemlanguage == 0) {
                                resultList.add(createLog(vo, "更新", "失败#47", SystemEnv.getHtmlLabelName(84810, userlanguage)));
                                if (session != null){
                                    session.setAttribute("resultList", resultList);
                                }
                                continue;
                            }
                            hrm.setSystemlanguage(new Integer(systemlanguage));
                        } else {
                            hrm.setSystemlanguage(new Integer(7));
                        }

                        // 性别，为空或其他情况统一为0(男)
                        if (!"".equals(Util.null2String(vo.getSex()))) {
                            String sex = vo.getSex().equals("女") ? "1" : "0";
                            hrm.setSex(sex);
                        }

                        // 婚姻状况，如果不是以下选项，则默认为未婚
                        if (!"".equals(Util.null2String(vo.getMaritalstatus()))) {
                            String maritalstatus = vo.getMaritalstatus().equals("已婚") ? "1"
                                    : vo.getMaritalstatus().equals("离异") ? "2" : "0";
                            hrm.setMaritalstatus(maritalstatus);
                        }

                        // 员工状态
                        writeLog("vo.getStatus() ===> " + vo.getStatus());
                        if (!"".equals(Util.null2String(vo.getStatus()))) {
                            int status = getStatus(vo.getStatus());
                            hrm.setStatus(new Integer(status));
                        } else {
                            hrm.setStatus(1);
                        }

                        // 学历
                        if (!"".equals(Util.null2String(vo.getEducationlevel()))) {
                            int educationlevel = getEducationlevel(vo.getEducationlevel());
                            hrm.setEducationlevel(new Integer(educationlevel));
                        }

                        // 工会会员,默认为是
                        if (!"".equals(Util.null2String(vo.getIslabouunion()))) {
                            String islabouunion = vo.getIslabouunion().equals("是") ? "1" : "0";
                            hrm.setIslabouunion(islabouunion);
                        }

                        // 健康状况
                        if (!"".equals(Util.null2String(vo.getHealthinfo()))) {
                            String healthinfo = getHealthinfo(vo.getHealthinfo());
                            hrm.setHealthinfo(healthinfo);
                        }

                        // 安全等级
                        if (isInteger(vo.getSeclevel())) {
                            int seclevel = vo.getSeclevel().equals("") ? 0 : Integer.parseInt(vo.getSeclevel());
                            hrm.setSeclevel(new Short((short) (seclevel)));
                        } else {
                            hrm.setSeclevel(new Short(Short.parseShort(resourcecominfo.getSeclevel(hrmId))));
                        }

                        // 职级
                        if (isInteger(vo.getJoblevel())) {
                            int joblevel = vo.getJoblevel().equals("") ? 0 : Integer.parseInt(vo.getJoblevel());
                            hrm.setJoblevel(new Short((short) joblevel));
                        }

                        // 用工性质
                        if (vo.getUsekind() != null) {
                            int usekind = getUseKind(vo.getUsekind());
                            hrm.setUsekind(usekind);
                        }

                        // 更新导入的时候，如果登录名不为空，需要校验一下当前的license
                        if (vo.getLoginid() != null) {
                            license.reloadLicenseInfo();
                            if (license.CkHrmnum() >= 0) {
                                resultList.add(
                                        createLog(vo, "更新", "失败#48", SystemEnv.getHtmlLabelName(83522, userlanguage)));
                                if (session != null) {
                                    session.setAttribute("resultList", resultList);
                                }
                                continue;
                            }
                        }

                        // 密码 加密
                        //sws 更新不变更密码
                        if (!"".equals(Util.null2String(vo.getPassword()))) {
                            String password = !vo.getPassword().equals("") ? Util.getEncrypt(vo.getPassword())
                                    : Util.getEncrypt("1");
                            hrm.setPassword(password);
                        }
                        boolean flag = true;

                        String updateStr = "update HrmResource set ";

                        String tmpstatus = "";
                        // k从1开始，id不是update字段
                        for (int k = 0; k < fields.length; k++) {
                            Field hrmField = hrmClass.getDeclaredField(fields[k]);
                            Field voField = voClass.getDeclaredField(fields[k]);

                            String hrmFieldType = hrmField.getType().getName();
                            String voFieldType = voField.getType().getName();

                            hrmField.setAccessible(true);
                            voField.setAccessible(true);

                            if (hrmField.get(hrm) != null) {
                                writeLog("hrmField 11111111");
                                if (hrmFieldType.endsWith("String")) {
                                    if (fields[k].equals("password")) {
                                        //sws 旧账号不更新密码
                                        writeLog("sws 旧账号不更新密码", keyMap.get(key));
                                    } else {
                                        updateStr = updateStr + fields[k] + "='" + hrmField.get(hrm) + "',";
                                    }
                                } else if (hrmFieldType.endsWith("Integer") || hrmFieldType.endsWith("Short")
                                        || hrmFieldType.endsWith("Float")) {
                                    updateStr = updateStr + fields[k] + "=" + hrmField.get(hrm) + ",";
                                }
                                if (fields[k].equals("status")) {
                                    tmpstatus = Util.null2String(hrmField.get(hrm));
                                }
                            } else if (voField.get(vo) != null) {
                                writeLog("voField 22222222");
                                if (voFieldType.endsWith("String")) {
                                    updateStr = updateStr + fields[k] + "='" + voField.get(vo) + "',";
                                } else if (voFieldType.endsWith("Integer") || voFieldType.endsWith("Short")
                                        || voFieldType.endsWith("Float")) {
                                    updateStr = updateStr + fields[k] + "=" + voField.get(vo) + ",";
                                }
                                if (fields[k].equals("status")) {
                                    tmpstatus = Util.null2String(voField.get(vo));
                                }
                            }
                        }

                        writeLog("updateStr ===> " + updateStr);
                        updateStr = updateStr + " lastmodid=" + lastmodid + ",lastmoddate='" + lastmoddate
                                + "',managerstr='" + hrm.getManagerstr() + "',"
                                + DbFunctionUtil.getUpdateSetSql(new RecordSet().getDBType(), lastmodid) + "  where id="
                                + keyMap.get(key);

                        if (!execSql(updateStr)) {
                            flag = false;
                        }

                        // 如果更新为离职，处理相关数据
                        if (tmpstatus.equals("5") && !resourcecominfo.getStatus("" + keyMap.get(key)).equals("5")) {
                            OrganisationCom orgCom = new OrganisationCom();
                            HrmServiceManager hrmaction = new HrmServiceManager();
                            String tmpid = "";
                            String type = "5";
                            int resourceid = keyMap.get(key);
                            String changedate = "";
                            String changereason = "";
                            String changecontractid = "";
                            String infoman = "";
                            String oldjobtitleid = resourcecominfo.getJobTitle("" + resourceid);
                            String para = "" + resourceid + separator + changedate + separator + changereason
                                    + separator + changecontractid + separator + infoman + separator + oldjobtitleid
                                    + separator + type + separator + "1";
                            recordSet.executeProc("HrmResource_Dismiss", para);
                            recordSet.executeSql("select max(id) from HrmStatusHistory");
                            recordSet.next();
                            tmpid = recordSet.getString(1);

                            // 删除角色成员
                            recordSet.executeSql("delete from hrmrolemembers where resourceid=" + resourceid);
                            // 删除手机版中设置的登录人员
                            recordSet.executeSql(
                                    "delete from PluginLicenseUser where plugintype='mobile' and sharetype='0' and sharevalue='"
                                            + resourceid + "'");
                            // 更新人员未离职，清空账号
                            recordSet.executeSql(
                                    "update HrmResource set status = 5 ,loginid='',password='',account='' where id = "
                                            + resourceid);
                            // 删除自定义组成员
                            recordSet.executeSql("delete hrmgroupmembers where userid=" + resourceid);
                            // 更新变更记录状态为已处理
                            recordSet.executeSql("update HrmStatusHistory set isdispose = 1 where id=" + tmpid);
                            boolean exist = orgCom.checkUser(resourceid);
                            if (exist) {
                                orgCom.deleteUser2(resourceid);
                            }
                            // OA与第三方接口单条数据同步方法开始
                            hrmaction.SynInstantHrmResource("" + resourceid, "3");
                            // OA与第三方接口单条数据同步方法结束
                        }

                        if (!updateBaseData(vo.getBaseFields(), vo.getBaseFieldsValue().trim(),
                                ((Integer) keyMap.get(key)).intValue())) {
                            flag = false;
                        }

                        if (!updatePersonData(vo.getPersonFields(), vo.getPersonFieldsValue().trim(),
                                ((Integer) keyMap.get(key)).intValue())) {
                            flag = false;
                        }

                        if (!updateWorkData(vo.getWorkFields().trim(), vo.getWorkFieldsValue(),
                                ((Integer) keyMap.get(key)).intValue())) {
                            flag = false;
                        }

                        /* update HrmResource_Trigger */
                        if (flag) {
                            recordSet.executeSql("select id from HrmResource_Trigger where id=" + hrmId);
                            if (recordSet.next()) {
                                recordSet.executeSql("update HrmResource_Trigger set managerid=" + hrm.getManagerid()
                                        + ",departmentid=" + hrm.getDepartmentid() + ",subcompanyid1="
                                        + hrm.getSubcompanyid1() + ",seclevel=" + hrm.getSeclevel() + ",managerstr='"
                                        + hrm.getManagerstr() + "' where id=" + hrmId);
                            } else {
                                String para = "" + hrmId + separator + hrm.getManagerid() + separator
                                        + hrm.getDepartmentid() + separator + hrm.getSubcompanyid1() + separator
                                        + hrm.getSeclevel() + separator + hrm.getManagerstr();
                                recordSet.executeProc("HrmResource_Trigger_Insert", para);
                            }
                        }

                        // 更新下级managerstr
                        if (flag && !"".equals(Util.null2String(vo.getManagerid()))) {
                            String temOldmanagerstr = "," + hrmId + oldmanagerstr;
                            temOldmanagerstr = temOldmanagerstr.endsWith(",") ? temOldmanagerstr
                                    : (temOldmanagerstr + ",");
                            String sql = "select id,departmentid,subcompanyid1,managerid,seclevel,managerstr from HrmResource where managerstr like '%"
                                    + temOldmanagerstr + "'";
                            recordSet.executeSql(sql);
                            while (recordSet.next()) {
                                String nowmanagerstr = Util.null2String(recordSet.getString("managerstr"));
                                String resourceid = recordSet.getString("id");
                                // 指定上级为自身的情况，不更新自身上级
                                if (hrmId.equals(resourceid)) {
                                    continue;
                                }
                                String nowmanagerstr2 = "";
                                int index = nowmanagerstr.lastIndexOf(oldmanagerstr);
                                if (index != -1) {
                                    if (!"".equals(managerstr)) {
                                        nowmanagerstr2 = nowmanagerstr.substring(0, index)
                                                + ("".equals(oldmanagerstr) ? managerstr.substring(1) : managerstr);
                                    } else {
                                        nowmanagerstr2 = nowmanagerstr.substring(0, index)
                                                + ("".equals(oldmanagerstr) ? "" : ",");
                                    }

                                }
                                RecordSetTrans rst = new RecordSetTrans();
                                rst.setAutoCommit(false);
                                try {
                                    String para = resourceid + separator + nowmanagerstr2;
                                    rst.executeProc("HrmResource_UpdateManagerStr", para);
                                    rst.commit();
                                } catch (Exception e) {
                                    rst.rollback();
                                    writeLog(e);
                                }

                            }
                        }
                        if (flag) {
                            resultList.add(createLog(vo, "更新", "成功", ""));
                            if (session != null) {
                                session.setAttribute("resultList", resultList);
                            }
                            // qc313055 [80][90][缺陷]IM集成-解决人员导入更新人员信息时，无账号列时，IM同步会进行删除操作的问题 ---start
                            RecordSet rst = new RecordSet();
                            if (vo.getLoginid() != null) {

                                // 此时resourcecominfo对象还没有更新，可以直接取
                                if (!"".equals(vo.getLoginid())) {
                                    if (!rtxService.checkUser(Integer.parseInt(hrmId))) {
                                        rtxService.addUser(Integer.parseInt(hrmId));// 更新人员rtx
                                    }
                                    coremailapi.synUser(hrmId);// 更新到CoreMail
                                } else {
                                    rst.executeSql("select * from HrmResource where id='" + hrmId + "'");
                                    String loginid = "";
                                    if (rst.next()) {
                                        loginid = rst.getString("loginid");
                                    }
                                    if (loginid != null && !loginid.equals("")) {
                                        if (!rtxService.checkUser(Integer.parseInt(hrmId))) {
                                            rtxService.addUser(Integer.parseInt(hrmId));// 更新人员rtx
                                        }
                                    } else {
                                        rtxService.deleteUser2(Integer.parseInt(hrmId));
                                    }

                                }
                            } else {
                                rst.executeSql("select * from HrmResource where id='" + hrmId + "'");
                                String loginid = "";
                                if (rst.next()) {
                                    loginid = rst.getString("loginid");
                                }
                                if (loginid != null && !loginid.equals("")) {
                                    if (!rtxService.checkUser(Integer.parseInt(hrmId))) {
                                        rtxService.addUser(Integer.parseInt(hrmId));// 更新人员rtx
                                    }
                                } else {
                                    rtxService.deleteUser2(Integer.parseInt(hrmId));
                                }
                                // qc313055 [80][90][缺陷]IM集成-解决人员导入更新人员信息时，无账号列时，IM同步会进行删除操作的问题 ---end
                            }
                        } else {
                            resultList.add(createLog(vo, "更新", "失败#49", ""));
                            if (session != null) {
                                session.setAttribute("resultList", resultList);
                            }
                        }
                    }
                } catch (Exception e) {
                    // 数据异常
                    writeLog(e);
                    resultList.add(createLog(vo, "更新", "失败#50", SystemEnv.getHtmlLabelName(83548, userlanguage)));
                    if (session != null) {
                        session.setAttribute("resultList", resultList);
                    }
                }
            }

//			try {
//				new Thread() {// 以下语句执行比较久，改为异步线程处理
//					@Override
//					public void run() {
//						synchronized (this) {
//							RecordSet rs = new RecordSet();
//							try {
//								if (rs.getDBType().equalsIgnoreCase("oracle")) {
//									rs.executeSql(
//											"update HrmSubCompany set tlevel=(select templevel from tempHrmSubCompanyView where tempHrmSubCompanyView.id=HrmSubCompany.id)");
//									rs.executeSql(
//											"update HrmDepartment set tlevel=(select templevel from tempHrmDepartmentView where tempHrmDepartmentView.id=HrmDepartment.id)");
//								} else {
//									rs.executeSql(
//											"update HrmSubCompany set tlevel=(select level from tempHrmSubCompanyView where tempHrmSubCompanyView.id=HrmSubCompany.id)");
//									rs.executeSql(
//											"update HrmDepartment set tlevel=(select level from tempHrmDepartmentView where tempHrmDepartmentView.id=HrmDepartment.id)");
//								}
//							} catch (Exception e1) {
//								writeLog(e1);
//							}
//						}
//					}
//				}.start();
//			} catch (Exception e) {
//				writeLog(e);
//			}
            //上述用法导致链接池耗尽, 换用静态线程控制
            HrmTlevelManager.setUpdate();


//		 endTime = System.currentTimeMillis();
//		 System.out.println("---分部、部门层级更新结束，用时："+(endTime - startTime));
//		 startTime = endTime;
            // 更新人员、分部、部门、职位、岗位、职位类型缓存

            resourcecominfo.removeResourceCache();
            departmentComInfo.removeCompanyCache();
            subCompanyComInfo.removeCompanyCache();
            jobActivitiesComInfo.removeJobActivitiesCache();
            jobTitlesComInfo.removeJobTitlesCache();
            jobGroupsComInfo.removeCompanyCache();
            locationComInfo.removeLocationCache();
            useKindComInfo.removeUseKindCache();
            // 初始化应用分权
            new weaver.hrm.appdetach.AppDetachComInfo().initSubDepAppData();

//			 endTime = System.currentTimeMillis();
//			 System.out.println("---数据缓存清空结束，用时："+(endTime - startTime));
//			 startTime = endTime;
            writeImportLog(resultList);
            if (session != null) {
                session.setAttribute("logFile", logFile);
            }
            return resultList;
        } catch (Exception e) {
            writeLog(e);
            return resultList;
        }

    }

    /**
     * 插入基础自定义字段信息
     *
     * @param baseFild  存储人员自定义数据的字段
     * @param baseValue 自定义数据值
     * @param id        需要添加自定义信息的人员id
     * @return
     */
    public boolean addBaseData(String baseFild, String baseValue, int id) {
        if (baseFild == null || baseFild.equals("")) {
            return true;
        }
        String baseValues[] = baseValue.split(";");
        String baseFields[] = baseFild.split(",");
        String fielddbType = "";
        String sql = "";
        RecordSet recordSet = new RecordSet();
        try {
            if (baseTypeMap == null) {
                baseTypeMap = new HashMap<String, JSONObject>();
                String baseSql = "select t1.fieldid,t1.hrm_fieldlable,t1.ismand,t2.fielddbtype,t2.fieldhtmltype,t2.type,t1.dmlurl from cus_formfield t1, cus_formdict t2 where t1.scope='HrmCustomFieldByInfoType' and t1.scopeid=-1 and t1.fieldid=t2.id order by t1.fieldorder";
                recordSet.execute(baseSql);
                while (recordSet.next()) {
                    String fieldid = Util.null2String(recordSet.getString("fieldid"));
                    String fielddbtype = Util.null2String(recordSet.getString("fielddbtype"));
                    String fieldhtmltype = Util.null2String(recordSet.getString("fieldhtmltype"));
                    String type = Util.null2String(recordSet.getString("type"));
                    String dmlurl = Util.null2String(recordSet.getString("dmlurl"));

                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("fieldid", fieldid);
                    jsonObject.put("fielddbtype", fielddbtype);
                    jsonObject.put("fieldhtmltype", fieldhtmltype);
                    jsonObject.put("type", type);
                    jsonObject.put("dmlurl", dmlurl);
                    baseTypeMap.put("field" + recordSet.getInt("fieldid"), jsonObject);
                }
            }

            sql = "insert into cus_fielddata  ";
            String valueStr = "";
            for (int i = 0; i < baseFields.length; i++) {
                JSONObject jsonObject = baseTypeMap.get(baseFields[i]);
                fielddbType = jsonObject.getString("fielddbtype");
                jsonObject.put("fieldvalue", baseValues[i]);
                String fieldvalue = HrmFieldManager.getReallyFieldvalue(jsonObject);
                if (fielddbType.startsWith("char") || fielddbType.startsWith("varchar")
                        || fielddbType.startsWith("text")) {
                    valueStr = valueStr + "," + "'" + (!fieldvalue.equals("") ? fieldvalue : "") + "'";
                } else {
                    valueStr = valueStr + "," + (!fieldvalue.equals("") ? fieldvalue : "NULL");
                }
            }
            valueStr = valueStr.substring(1);
            sql += "(scope,scopeid,id," + baseFild + ") values('HrmCustomFieldByInfoType'," + -1 + "," + id + ","
                    + valueStr + ")";
        } catch (Exception e) {
            writeLog(e);
        }
        return recordSet.execute(sql);
    }

    /**
     * 更新基础自定义字段
     *
     * @param baseFild  存储人员自定义数据的字段
     * @param baseValue 自定义数据值
     * @param id        需要添加自定义信息的人员id
     * @return
     */
    public boolean updateBaseData(String baseFild, String baseValue, int id) {
        if (baseFild == null || baseFild.equals("")) {
            return true;
        }

        // 检查cus_fielddata表中是否存在，对应id人员的基础自定义信息，如果不存在则向数据库添加
        RecordSet recordSet = new RecordSet();
        String checkbaseInfo = "select id from cus_fielddata where scope='HrmCustomFieldByInfoType' and scopeid=-1 and id=" + id;
        recordSet.execute(checkbaseInfo);
        if (!recordSet.next()) {
            return addBaseData(baseFild, baseValue, id);
        }

        String baseValues[] = baseValue.split(";");
        String baseFields[] = baseFild.split(",");
        String fielddbType = "";
        String sql = "";
        try {
            if (baseTypeMap == null) {
                baseTypeMap = new HashMap<String, JSONObject>();
                String personSql = "select t1.fieldid,t1.hrm_fieldlable,t1.ismand,t2.fielddbtype,t2.fieldhtmltype,t2.type,t1.dmlurl from cus_formfield t1, cus_formdict t2 where t1.scope='HrmCustomFieldByInfoType' and t1.scopeid=-1 and t1.fieldid=t2.id order by t1.fieldorder";
                recordSet.execute(personSql);
                while (recordSet.next()) {
                    String fieldid = Util.null2String(recordSet.getString("fieldid"));
                    String fielddbtype = Util.null2String(recordSet.getString("fielddbtype"));
                    String fieldhtmltype = Util.null2String(recordSet.getString("fieldhtmltype"));
                    String type = Util.null2String(recordSet.getString("type"));
                    String dmlurl = Util.null2String(recordSet.getString("dmlurl"));

                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("fieldid", fieldid);
                    jsonObject.put("fielddbtype", fielddbtype);
                    jsonObject.put("fieldhtmltype", fieldhtmltype);
                    jsonObject.put("type", type);
                    jsonObject.put("dmlurl", dmlurl);
                    baseTypeMap.put("field" + recordSet.getInt("fieldid"), jsonObject);
                }
            }

            sql = "update cus_fielddata set ";
            String setStr = "";
            for (int i = 0; i < baseFields.length; i++) {
                JSONObject jsonObject = baseTypeMap.get(baseFields[i]);
                fielddbType = jsonObject.getString("fielddbtype");
                jsonObject.put("fieldvalue", baseValues[i]);
                String fieldvalue = HrmFieldManager.getReallyFieldvalue(jsonObject);
                if (fieldvalue.startsWith(",")) {
                    fieldvalue = fieldvalue.substring(1, fieldvalue.length());
                }
                if (fieldvalue.endsWith(",")) {
                    fieldvalue = fieldvalue.substring(0, fieldvalue.length() - 1);
                }
                if (fielddbType.startsWith("char") || fielddbType.startsWith("varchar")
                        || fielddbType.startsWith("text")) {
                    setStr = setStr + "," + baseFields[i] + "='" + (!fieldvalue.equals("") ? fieldvalue : "") + "'";
                } else {
                    setStr = setStr + "," + baseFields[i] + "=" + (!fieldvalue.equals("") ? fieldvalue : "NULL");
                }
            }
            sql += setStr.substring(1) + " where scope='HrmCustomFieldByInfoType' and scopeid=-1 and id=" + id;
        } catch (Exception e) {
            writeLog(e);
        }
        return recordSet.execute(sql);
    }

    /**
     * 插入个人自定义字段信息
     *
     * @param personFild  存储人员自定义数据的字段
     * @param personValue 自定义数据值
     * @param id          需要添加自定义信息的人员id
     * @return
     */
    public boolean addPersonData(String personFild, String personValue, int id) {
        if (personFild == null || personFild.equals("")) {
            return true;
        }
        String personValues[] = personValue.split(";");
        String personFields[] = personFild.split(",");
        String fielddbType = "";
        String sql = "";
        RecordSet recordSet = new RecordSet();
        try {
            if (personTypeMap == null) {
                personTypeMap = new HashMap<String, JSONObject>();
                String personSql = "select t1.fieldid,t1.hrm_fieldlable,t1.ismand,t2.fielddbtype,t2.fieldhtmltype,t2.type,t1.dmlurl from cus_formfield t1, cus_formdict t2 where t1.scope='HrmCustomFieldByInfoType' and t1.scopeid=1 and t1.fieldid=t2.id order by t1.fieldorder";
                recordSet.execute(personSql);
                while (recordSet.next()) {
                    String fieldid = Util.null2String(recordSet.getString("fieldid"));
                    String fielddbtype = Util.null2String(recordSet.getString("fielddbtype"));
                    String fieldhtmltype = Util.null2String(recordSet.getString("fieldhtmltype"));
                    String type = Util.null2String(recordSet.getString("type"));
                    String dmlurl = Util.null2String(recordSet.getString("dmlurl"));

                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("fieldid", fieldid);
                    jsonObject.put("fielddbtype", fielddbtype);
                    jsonObject.put("fieldhtmltype", fieldhtmltype);
                    jsonObject.put("type", type);
                    jsonObject.put("dmlurl", dmlurl);
                    personTypeMap.put("field" + recordSet.getInt("fieldid"), jsonObject);
                }
            }

            sql = "insert into cus_fielddata  ";
            String valueStr = "";
            for (int i = 0; i < personFields.length; i++) {
                JSONObject jsonObject = personTypeMap.get(personFields[i]);
                fielddbType = jsonObject.getString("fielddbtype");
                jsonObject.put("fieldvalue", personValues[i]);
                String fieldvalue = HrmFieldManager.getReallyFieldvalue(jsonObject);
                if (fielddbType.startsWith("char") || fielddbType.startsWith("varchar")
                        || fielddbType.startsWith("text")) {
                    valueStr = valueStr + "," + "'" + (!fieldvalue.equals("") ? fieldvalue : "") + "'";
                } else {
                    valueStr = valueStr + "," + (!fieldvalue.equals("") ? fieldvalue : "NULL");
                }
            }
            valueStr = valueStr.substring(1);
            sql += "(scope,scopeid,id," + personFild + ") values('HrmCustomFieldByInfoType'," + 1 + "," + id + ","
                    + valueStr + ")";
        } catch (Exception e) {
            writeLog(e);
        }
        return recordSet.execute(sql);
    }

    /**
     * 更新个人自定义字段
     *
     * @param personFild  存储人员自定义数据的字段
     * @param personValue 自定义数据值
     * @param id          需要添加自定义信息的人员id
     * @return
     */
    public boolean updatePersonData(String personFild, String personValue, int id) {
        if (personFild == null || personFild.equals("")) {
            return true;
        }
        // 检查cus_fielddata表中是否存在，对应id人员的个人自定义信息，如果不存在则向数据库添加
        String checkWorkInfo = "select id from cus_fielddata where scope='HrmCustomFieldByInfoType' and scopeid=1 and id="
                + id;
        RecordSet recordSet = new RecordSet();
        recordSet.execute(checkWorkInfo);
        if (!recordSet.next()) {
            return addPersonData(personFild, personValue, id);
        }

        String personValues[] = personValue.split(";");
        String personFields[] = personFild.split(",");
        String fielddbType = "";
        String sql = "";
        try {
            if (personTypeMap == null) {
                personTypeMap = new HashMap<String, JSONObject>();
                String personSql = "select t1.fieldid,t1.hrm_fieldlable,t1.ismand,t2.fielddbtype,t2.fieldhtmltype,t2.type,t1.dmlurl from cus_formfield t1, cus_formdict t2 where t1.scope='HrmCustomFieldByInfoType' and t1.scopeid=1 and t1.fieldid=t2.id order by t1.fieldorder";
                recordSet.execute(personSql);
                while (recordSet.next()) {
                    String fieldid = Util.null2String(recordSet.getString("fieldid"));
                    String fielddbtype = Util.null2String(recordSet.getString("fielddbtype"));
                    String fieldhtmltype = Util.null2String(recordSet.getString("fieldhtmltype"));
                    String type = Util.null2String(recordSet.getString("type"));
                    String dmlurl = Util.null2String(recordSet.getString("dmlurl"));

                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("fieldid", fieldid);
                    jsonObject.put("fielddbtype", fielddbtype);
                    jsonObject.put("fieldhtmltype", fieldhtmltype);
                    jsonObject.put("type", type);
                    jsonObject.put("dmlurl", dmlurl);
                    personTypeMap.put("field" + recordSet.getInt("fieldid"), jsonObject);
                }
            }

            sql = "update cus_fielddata set ";
            String setStr = "";
            for (int i = 0; i < personFields.length; i++) {
                JSONObject jsonObject = personTypeMap.get(personFields[i]);
                fielddbType = jsonObject.getString("fielddbtype");
                jsonObject.put("fieldvalue", personValues[i]);
                String fieldvalue = HrmFieldManager.getReallyFieldvalue(jsonObject);
                if (fieldvalue.startsWith(",")) {
                    fieldvalue = fieldvalue.substring(1, fieldvalue.length());
                }
                if (fieldvalue.endsWith(",")) {
                    fieldvalue = fieldvalue.substring(0, fieldvalue.length() - 1);
                }
                if (fielddbType.startsWith("char") || fielddbType.startsWith("varchar")
                        || fielddbType.startsWith("text")) {
                    setStr = setStr + "," + personFields[i] + "='" + (!fieldvalue.equals("") ? fieldvalue : "") + "'";
                } else {
                    setStr = setStr + "," + personFields[i] + "=" + (!fieldvalue.equals("") ? fieldvalue : "NULL");
                }
            }
            sql += setStr.substring(1) + " where scope='HrmCustomFieldByInfoType' and scopeid=1 and id=" + id;
        } catch (Exception e) {
            writeLog(e);
        }
        return recordSet.execute(sql);
    }

    /**
     * 插入工作自定义字段
     *
     * @param workField 存储工作信息自定义数据的字段
     * @param workValue 自定义数据值
     * @param id        需要添加自定义信息的人员id
     * @return
     */
    public boolean addWorkData(String workField, String workValue, int id) {
        if (workField == null || workField.equals("")) {
            return true;
        }
        String workValues[] = workValue.split(";");
        String workFields[] = workField.split(",");
        String fielddbType = "";
        String sql = "";
        RecordSet recordSet = new RecordSet();
        try {
            if (workTypeMap == null) {
                workTypeMap = new HashMap<String, JSONObject>();
                String workSql = "select t1.fieldid,t1.hrm_fieldlable,t1.ismand,t2.fielddbtype,t2.fieldhtmltype,t2.type,t1.dmlurl from cus_formfield t1, cus_formdict t2 where t1.scope='HrmCustomFieldByInfoType' and t1.scopeid=3 and t1.fieldid=t2.id order by t1.fieldorder";
                recordSet.execute(workSql);
                while (recordSet.next()) {
                    String fieldid = Util.null2String(recordSet.getString("fieldid"));
                    String fielddbtype = Util.null2String(recordSet.getString("fielddbtype"));
                    String fieldhtmltype = Util.null2String(recordSet.getString("fieldhtmltype"));
                    String type = Util.null2String(recordSet.getString("type"));
                    String dmlurl = Util.null2String(recordSet.getString("dmlurl"));

                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("fieldid", fieldid);
                    jsonObject.put("fielddbtype", fielddbtype);
                    jsonObject.put("fieldhtmltype", fieldhtmltype);
                    jsonObject.put("type", type);
                    jsonObject.put("dmlurl", dmlurl);
                    workTypeMap.put("field" + recordSet.getInt("fieldid"), jsonObject);
                }
            }
            sql = "insert into cus_fielddata  ";
            String valueStr = "";
            for (int i = 0; i < workFields.length; i++) {
                JSONObject jsonObject = workTypeMap.get(workFields[i]);
                fielddbType = jsonObject.getString("fielddbtype");
                jsonObject.put("fieldvalue", workValues[i]);
                String fieldvalue = HrmFieldManager.getReallyFieldvalue(jsonObject);
                if (fielddbType.startsWith("char") || fielddbType.startsWith("varchar")
                        || fielddbType.startsWith("text")) {
                    valueStr = valueStr + "," + "'" + (!fieldvalue.equals("") ? fieldvalue : "") + "'";
                } else {
                    valueStr = valueStr + "," + (!fieldvalue.equals("") ? fieldvalue : "NULL");
                }
            }
            valueStr = valueStr.substring(1);
            sql += "(scope,scopeid,id," + workField + ") values('HrmCustomFieldByInfoType'," + 3 + "," + id + ","
                    + valueStr + ")";
        } catch (Exception e) {
            writeLog(e);
        }
        return recordSet.execute(sql);
    }

    /**
     * 更新工作自定义字段
     *
     * @param workField 存储工作信息自定义数据的字段
     * @param workValue 自定义数据值
     * @param id        需要添加自定义信息的人员id
     * @return
     */
    public boolean updateWorkData(String workField, String workValue, int id) {
        if (workField == null || workField.equals("")) {
            return true;
        }

        // 检查cus_fielddata表中是否存在，对应id人员的工作自定义信息，如果不存在则向数据库添加
        String checkWorkInfo = "select id from cus_fielddata where scope='HrmCustomFieldByInfoType' and scopeid=3 and id="
                + id;
        RecordSet recordSet = new RecordSet();
        recordSet.execute(checkWorkInfo);
        if (!recordSet.next()) {
            return addWorkData(workField, workValue, id);
        }

        String workValues[] = workValue.split(";");
        String workFields[] = workField.split(",");
        String fielddbType = "";
        String sql = "";
        try {
            if (workTypeMap == null) {
                workTypeMap = new HashMap<String, JSONObject>();
                String workSql = "select t1.fieldid,t1.hrm_fieldlable,t1.ismand,t2.fielddbtype,t2.fieldhtmltype,t2.type,t1.dmlurl from cus_formfield t1, cus_formdict t2 where t1.scope='HrmCustomFieldByInfoType' and t1.scopeid=3 and t1.fieldid=t2.id order by t1.fieldorder";
                recordSet.execute(workSql);
                while (recordSet.next()) {
                    String fieldid = Util.null2String(recordSet.getString("fieldid"));
                    String fielddbtype = Util.null2String(recordSet.getString("fielddbtype"));
                    String fieldhtmltype = Util.null2String(recordSet.getString("fieldhtmltype"));
                    String type = Util.null2String(recordSet.getString("type"));
                    String dmlurl = Util.null2String(recordSet.getString("dmlurl"));

                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("fieldid", fieldid);
                    jsonObject.put("fielddbtype", fielddbtype);
                    jsonObject.put("fieldhtmltype", fieldhtmltype);
                    jsonObject.put("type", type);
                    jsonObject.put("dmlurl", dmlurl);
                    workTypeMap.put("field" + recordSet.getInt("fieldid"), jsonObject);
                }
            }

            sql = "update cus_fielddata set ";
            String setStr = "";
            for (int i = 0; i < workFields.length; i++) {
                JSONObject jsonObject = workTypeMap.get(workFields[i]);
                fielddbType = jsonObject.getString("fielddbtype");
                jsonObject.put("fieldvalue", workValues[i]);
                String fieldvalue = HrmFieldManager.getReallyFieldvalue(jsonObject);
                if (fieldvalue.startsWith(",")) {
                    fieldvalue = fieldvalue.substring(1, fieldvalue.length());
                }
                if (fieldvalue.endsWith(",")) {
                    fieldvalue = fieldvalue.substring(0, fieldvalue.length() - 1);
                }
                if (fielddbType.startsWith("char") || fielddbType.startsWith("varchar")
                        || fielddbType.startsWith("text")) {
                    setStr = setStr + "," + workFields[i] + "='" + (!fieldvalue.equals("") ? fieldvalue : "") + "'";
                } else {
                    setStr = setStr + "," + workFields[i] + "=" + (!fieldvalue.equals("") ? fieldvalue : "NULL");
                }
            }

            sql += setStr.substring(1) + " where scope='HrmCustomFieldByInfoType' and scopeid=3 and id=" + id;
        } catch (Exception e) {
            writeLog(e);
        }
        return recordSet.execute(sql);
    }

    /**
     * 根据分部名称获取分部Id，不存在就创建
     *
     * @param subCompanyName 分部名称 eg：泛微广东>泛微深圳
     * @return
     */
    public int getSubCompanyId(String subCompanyName) {

        String subCompanyNames[] = subCompanyName.split(">");
        if (subCompanyNames != null && subCompanyNames.length >= 10) {
            return -9;
        }
        int currentId = 0;
        int parentId = 0;
        int curCount = 0;
        int iscanceled = 0;

        String currentidsql = "";
        String canceledsql = "";
        String sql = "";
        String sqlInsert = "";
        RecordSet recordSet = new RecordSet();
        for (int i = 0; i < subCompanyNames.length; i++) {
            if (StringUtil.isNull(subCompanyNames[i])) {
                continue;
            }

            sql = "select id from HrmSubCompany where ltrim(rtrim(dbo.convToMultiLang(ltrim(rtrim(subcompanyname)),"
                    + userlanguage + ")))='" + subCompanyNames[i].trim() + "' and supsubcomid=" + parentId;
            if (recordSet.getDBType().equalsIgnoreCase("oracle")) {
                sql = "select id from HrmSubCompany where ltrim(rtrim(convToMultiLang(ltrim(rtrim(subcompanyname)),"
                        + userlanguage + ")))='" + subCompanyNames[i].trim() + "' and supsubcomid=" + parentId;
            }
            currentidsql = sql + " and (canceled is null or canceled != 1)";
            currentId = getResultSetId(currentidsql);

            if (parentId == 0 && currentId == 0) {
                recordSet.executeSql(
                        "select COUNT(id) from HrmSubCompany where supsubcomid = 0 and (canceled is null or canceled != '1')");
                if (recordSet.next()) {
                    curCount = recordSet.getInt(1);
                }
            }

            // added by wcd 2015-02-06 [限制分部数]
            if (currentId == 0) {
                canceledsql = sql + " and canceled='1' ";
                iscanceled = getResultSetId(canceledsql);
                if (iscanceled == 0) {
                } else {
                    currentId = -2;
                    break;
                }
                if (scCount == 0 || curCount < scCount) { // 客户需要处理成分部不存在时不新建
//					sqlInsert = "insert into HrmSubCompany(subcompanyname,subcompanydesc,companyid,supsubcomid ,"
//							+ DbFunctionUtil.getInsertColumnSql() + ") values('" + subCompanyNames[i].trim() + "','"
//							+ subCompanyNames[i].trim() + "',1" + "," + parentId + " ,"
//							+ DbFunctionUtil.getInsertColumnValueSql(recordSet.getDBType(), 1) + ")"; // 创建
//					execSql(sqlInsert);
//					currentId = getResultSetId(sql);
//
//					try {
//						if (parentId > 0) {
//							coremailapi.synOrg("com_" + currentId, subCompanyNames[i].trim(), "parent_org_unit_id=com_"
//									+ parentId + "&org_unit_name=" + subCompanyNames[i].trim(), "0");
//							// testapi.synOrg("com_"+currentId, subCompanyNames[i].trim(), "com_"+parentId,
//							// "0");
//						} else {
//							coremailapi.synOrg("com_" + currentId, subCompanyNames[i].trim(),
//									"org_unit_name=" + subCompanyNames[i].trim(), "0");
//							// testapi.synOrg("com_"+currentId, subCompanyNames[i].trim(), "", "0");
//						}
//					} catch (Exception e) {
//						writeLog(e);
//					}
//
//					sql = "insert into leftmenuconfig (userid,infoid,visible,viewindex,resourceid,resourcetype,locked,lockedbyid,usecustomname,customname,customname_e)  select  distinct  userid,infoid,visible,viewindex,"
//							+ currentId
//							+ ",2,locked,lockedbyid,usecustomname,customname,customname_e from leftmenuconfig where resourcetype=1  and resourceid=1";
//					execSql(sql);
//					sql = "insert into mainmenuconfig (userid,infoid,visible,viewindex,resourceid,resourcetype,locked,lockedbyid,usecustomname,customname,customname_e)  select  distinct  userid,infoid,visible,viewindex,"
//							+ currentId
//							+ ",2,locked,lockedbyid,usecustomname,customname,customname_e from mainmenuconfig where resourcetype=1  and resourceid=1";
//					execSql(sql);
                } else {
                    currentId = -1;
                }
            }
            parentId = currentId;
            if (currentId != -1) {
                rtxService.addSubCompany(parentId); // 同步RTX
            }
        }
        return currentId;
    }

    /**
     * 获取部门id，不存在就创建
     *
     * @param deptNames    部门名称
     * @param subCompanyId 所属分部id
     * @return
     */
    public int getDeptId(String deptNames, int subCompanyId) {

        String deptName[] = deptNames.split(">");
        int currentId = 0;
        int parentId = 0;
        int iscanceled;

        String currentidsql;
        String canceledsql;
        String sql;
        RecordSet recordSet = new RecordSet();
        for (int i = 0; i < deptName.length; i++) {
            if (deptName[i] == null || deptName[i].equals("")) {
                continue;
            }

            sql = "select id from HrmDepartment where subcompanyid1=" + subCompanyId
                    + " and ltrim(rtrim(dbo.convToMultiLang(ltrim(rtrim(departmentname))," + userlanguage + ")))='"
                    + deptName[i].trim() + "' and supdepid=" + parentId;
            if (recordSet.getDBType().equalsIgnoreCase("oracle")) {
                sql = "select id from HrmDepartment where subcompanyid1=" + subCompanyId
                        + " and ltrim(rtrim(convToMultiLang(ltrim(rtrim(departmentname))," + userlanguage + ")))='"
                        + deptName[i].trim() + "' and supdepid=" + parentId;
            }
            currentidsql = sql + " and (canceled  !=1 or canceled is null)";
            writeLog("currentidsql ===> "+currentidsql);
            currentId = getResultSetId(currentidsql);
            writeLog("currentId ===> "+currentId);

            if (currentId == 0) {
                canceledsql = sql + " and canceled='1' ";
                iscanceled = getResultSetId(canceledsql);
                writeLog("iscanceled ===> "+iscanceled);
                if (iscanceled == 0) {
                } else {
                    currentId = -2;
                    break;
                }
            }
            parentId = currentId;

            writeLog("parentId ===> "+parentId);
        }

        return currentId;
    }

    /**
     * 获取岗位id
     *
     * @param jobtitlename    岗位名称
     * @param jobactivityname 职务
     * @param jobgroupname    职务类型
     * @return
     */
    public int getJobTitles(String jobtitlename, String jobactivityname, String jobgroupname) {
        RecordSet rs = new RecordSet();
        RecordSet recordSet = new RecordSet();
        String sqlInsert = "";

        jobgroupname = Util.convertInput2DB4(jobgroupname);
        jobactivityname = Util.convertInput2DB4(jobactivityname);

        /* 获取jobgroupid 职务类别 */
        String selSql = "select id from HrmJobGroups where ltrim(rtrim(dbo.convToMultiLang(ltrim(rtrim(jobgroupname)),"
                + userlanguage + ")))='" + jobgroupname + "'";
        if (recordSet.getDBType().equalsIgnoreCase("oracle")) {
            selSql = "select id from HrmJobGroups where ltrim(rtrim(convToMultiLang(ltrim(rtrim(jobgroupname)),"
                    + userlanguage + ")))='" + jobgroupname + "'";
        }

        int jobgroupid = getResultSetId(selSql);

        if (jobgroupid == 0) {
            if (jobgroupname.length() == 0) {
                selSql = "select max(id) as id from HrmJobGroups";
                jobgroupid = getResultSetId(selSql);
            } else {
                sqlInsert = "insert into HrmJobGroups (jobgroupname,jobgroupremark ,"
                        + DbFunctionUtil.getInsertColumnSql() + ") values('" + jobgroupname + "','" + jobgroupname
                        + "'," + DbFunctionUtil.getInsertColumnValueSql(recordSet.getDBType(), 1) + ")"; // 创建
                execSql(sqlInsert);
                jobgroupid = getResultSetId(selSql);
            }
        }

        /* 获取jobactivityid 职务 */
        selSql = "select id from HrmJobActivities where ltrim(rtrim(dbo.convToMultiLang(ltrim(rtrim(jobactivityname)),"
                + userlanguage + ")))='" + jobactivityname + "' and jobgroupid=" + jobgroupid;
        if (recordSet.getDBType().equalsIgnoreCase("oracle")) {
            selSql = "select id from HrmJobActivities where ltrim(rtrim(convToMultiLang(ltrim(rtrim(jobactivityname)),"
                    + userlanguage + ")))='" + jobactivityname + "' and jobgroupid=" + jobgroupid;
        }

        int jobactivityid = getResultSetId(selSql);

        if (jobactivityid == 0) {
            sqlInsert = "insert into HrmJobActivities (jobactivityname,Jobactivitymark,jobgroupid ,"
                    + DbFunctionUtil.getInsertColumnSql() + ") values('" + jobactivityname + "','" + jobactivityname
                    + "'," + jobgroupid + " ," + DbFunctionUtil.getInsertColumnValueSql(recordSet.getDBType(), 1) + ")"; // 创建
            execSql(sqlInsert);
            jobactivityid = getResultSetId(selSql);
        }

        /* 获取岗位id */
        selSql = "select id from HrmJobTitles where ltrim(rtrim(dbo.convToMultiLang(ltrim(rtrim(jobtitlemark)),"
                + userlanguage + ")))='" + jobtitlename + "' and jobactivityid=" + jobactivityid;
        if (recordSet.getDBType().equalsIgnoreCase("oracle")) {
            selSql = "select id from HrmJobTitles where ltrim(rtrim(convToMultiLang(ltrim(rtrim(jobtitlemark)),"
                    + userlanguage + ")))='" + jobtitlename + "' and jobactivityid=" + jobactivityid;
        }

        int jobtitle = getResultSetId(selSql);

        if (jobtitle == 0) {
            sqlInsert = "insert into HrmJobTitles (jobtitlename,Jobtitlemark,jobactivityid ,"
                    + DbFunctionUtil.getInsertColumnSql() + ") values('" + jobtitlename + "','" + jobtitlename + "',"
                    + jobactivityid + " ," + DbFunctionUtil.getInsertColumnValueSql(recordSet.getDBType(), 1) + ")"; // 创建
            execSql(sqlInsert);
            jobtitle = getResultSetId(selSql);
        } else {
            selSql = "select * from HrmJobTitles where id = '" + jobtitle + "' and jobactivityid = '" + jobactivityid
                    + "' ";
            recordSet.executeSql(selSql);
            if (!recordSet.next()) {// 查询这个名称已存在的岗位，对应的职务是否存在。如果对应职务的岗位不存在，则说明岗位对应的职务和职务类别有更新
                sqlInsert = "update HrmJobTitles set jobactivityid = '" + jobactivityid + "',"
                        + DbFunctionUtil.getUpdateSetSql(rs.getDBType(), 1) + " where id = '" + jobtitle + "' ";
                rs.executeSql(sqlInsert);
            }
        }
        return jobtitle;
    }

    /**
     * 获取直接上级Id和所有上级
     *
     * @param keyFieldValue 上级标记值，eg:编号
     * @param keyField      关键字段
     * @return
     */
    public Map getManagerIdAndStr(String hrmid, String keyFieldValue, String keyField) {

        int managerId = 0;
        String managerstr = "";
        RecordSet recordSet = new RecordSet();
        Map<String, Comparable> managerInfo = new HashMap<String, Comparable>();
        if (!keyFieldValue.equals("")) {
            String selSql = "select id,managerstr from HrmResource where " + keyField + "='" + keyFieldValue + "'";
            recordSet.execute(selSql);
            while (recordSet.next()) {
                managerId = recordSet.getInt("id");
                managerstr = recordSet.getString("managerstr");
                if (!hrmid.equals("" + managerId)) { // 设置上级为自身的情况
                    managerstr = "," + managerId + managerstr;
                } else {
                    managerstr = "," + managerId + ",";
                }

                managerstr = managerstr.endsWith(",") ? managerstr : (managerstr + ",");
                managerInfo.put("managerid", new Integer(managerId));
                managerInfo.put("managerstr", managerstr);
            }
        }
        return managerInfo;
    }

    /**
     * 获取助理id
     *
     * @param keyFieldValue 助理标记
     * @param keyField      关键字段
     * @return
     */
    public int getAssistantid(String keyFieldValue, String keyField) {

        int getAssistantid = 0;
        if (!keyFieldValue.equals("")) {
            String selSql = "select id from HrmResource where " + keyField + "='" + keyFieldValue + "'";
            getAssistantid = getResultSetId(selSql);
        }
        return getAssistantid;
    }

    /**
     * 办公地点id
     *
     * @param locationname 办公地点名称
     * @return
     */
    public int getLocationid(String locationname) {
        int locationid = 0;
        if (!locationname.equals("")) {
            locationid = locationMap.containsKey(locationname) ? locationMap.get(locationname) : 0;
            if (locationid == 0) {
                String insertSql = "insert into HrmLocations(locationname,locationdesc,countryid) values('"
                        + locationname + "','" + locationname + "',1)";
                execSql(insertSql);
                String selSql = "select id from HrmLocations where countryid=1 and locationname='" + locationname + "'";
                locationid = getResultSetId(selSql);
                locationMap.put(locationname, locationid);
            }
        }
        return locationid;
    }

    /**
     * 用工性质id
     *
     * @param usekindname 用工性质名称
     * @return
     */
    public int getUseKind(String usekindname) {
        int usekindid = 0;
        if (!usekindname.equals("")) {
            usekindid = usekindMap.containsKey(usekindname) ? usekindMap.get(usekindname) : 0;
            if (usekindid == 0) {
                String insertSql = "insert into HrmUseKind(name,description) values('" + usekindname + "','"
                        + usekindname + "')";
                execSql(insertSql);
                String selSql = "select id from HrmUseKind where name='" + usekindname + "'";
                usekindid = getResultSetId(selSql);
                usekindMap.put(usekindname, usekindid);
            }
        }
        return usekindid;
    }

    /**
     * 职称
     *
     * @param jobcall 职称名称
     * @return
     */
    public int getJobcall(String jobcall) {

        int jobcalld = 0;
        if (!jobcall.equals("")) {
            jobcalld = jobcallMap.containsKey(jobcall) ? jobcallMap.get(jobcall) : 0;
            if (jobcalld == 0) {
                String insertSql = "insert into HrmJobCall(name) values('" + jobcall + "')";
                execSql(insertSql);
                String selSql = "select id from HrmJobCall where name='" + jobcall + "'";
                jobcalld = getResultSetId(selSql);
                jobcallMap.put(jobcall, jobcalld);
            }
        }
        return jobcalld;
    }

    /**
     * 系统语言
     *
     * @param language 语言 eg：简体中文,English,繁體中文
     * @return
     */
    public int getSystemlanguage(String language) {
        int systemlanguageid = 7; // 系统语言默认为中文

        // 系统只支持三种语言，如果是其他语言，提示输入有误
        if (!(language.equals("English") || language.equals("繁體中文") || language.equals("简体中文")) && language != "") {
            return -1;
        }

        // 如果系统不支持多语言，则返回0
        if ((language.equals("English") || language.equals("繁體中文")) && !multilanguage.toLowerCase().equals("y")) {
            return 0;
        }

        if (!language.equals("")) {
            if (language.equals("简体中文")) {
                systemlanguageid = cnLanguageId;
            } else {
                systemlanguageid = sysLanguage.get(language);
            }
        }
        return systemlanguageid;
    }

    /**
     * 获取员工状态
     *
     * @param status 员工状态值
     * @return
     */
    public int getStatus(String status) {

        int statusid = 1;
        switch (status) {
            case "试用":
                statusid = 0;
                break;
            case "正式":
                statusid = 1;
                break;
            case "临时":
                statusid = 2;
                break;
            case "试用延期":
                statusid = 3;
                break;
            case "解聘":
                statusid = 4;
                break;
            case "离职":
                statusid = 5;
                break;
            case "退休":
                statusid = 6;
                break;
            case "无效":
                statusid = 7;
                break;
            default:
                statusid = 1;
                break;
        }
        return statusid;
    }

    /**
     * 获取学历Id
     *
     * @param educationlevel 学历值
     * @return
     */
    public int getEducationlevel(String educationlevel) {
        int educationlevelid = 0;
        if (!educationlevel.equals("")) {
            educationlevelid = educationlevelMap.containsKey(educationlevel) ? educationlevelMap.get(educationlevel)
                    : 0;
            if (educationlevelid == 0) {
                String insertSql = "insert into HrmEducationLevel(name,description) values('" + educationlevel + "','"
                        + educationlevel + "')";
                execSql(insertSql);
                String selSql = "select id from HrmEducationLevel where name='" + educationlevel + "'";
                educationlevelid = getResultSetId(selSql);
                educationlevelMap.put(educationlevel, educationlevelid);
            }
        }
        return educationlevelid;
    }

    /**
     * 获取身体状况
     *
     * @param healthinfo
     * @return
     */
    public String getHealthinfo(String healthinfo) {

        String healthinfoid = "0";

        switch (healthinfo) {
            case "良好":
                healthinfoid = "1";
                break;
            case "一般":
                healthinfoid = "2";
                break;
            case "较差":
                healthinfoid = "3";
                break;
            default:
                healthinfoid = "0";
                break;
        }

        return healthinfoid;
    }

    /**
     * 获取id与workcode_lastname 键值对，用于对导入数据重复性判断
     *
     * @param keyField key值，重复性验证字段
     */
    public void getKeyMap(String keyField) {
        RecordSet recordSet = new RecordSet();
        String sql = "";
        sql = "select id, certificatenum, ltrim(rtrim(dbo.convToMultiLang(ltrim(rtrim(" + keyField + ")),"
                + userlanguage + "))) as " + keyField + " from HrmResource";
        if (recordSet.getDBType().equalsIgnoreCase("oracle")) {
            sql = "select id, certificatenum, ltrim(rtrim(convToMultiLang(ltrim(rtrim(" + keyField + ")),"
                    + userlanguage + "))) as " + keyField + " from HrmResource";
        }
        recordSet.execute(sql);
        String cerNum = "";
        while (recordSet.next()) {
            cerNum = recordSet.getString("certificatenum");
            keyMap.put(recordSet.getString(keyField), new Integer(recordSet.getInt("id")));
            if (StringUtil.isNotNull(cerNum)) {
                certificateNums.put(StringUtil.vString(cerNum), new Integer(recordSet.getInt("id")));
            }
        }
    }

    /**
     * 写入导入日志
     *
     * @param resultList 导入结果集
     */
    public void writeImportLog(List resultList) {

        if (logFile.equals("")) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");

            String logFile1 = GCONST.getRootPath() + "/log/hrmImportLog";
            logFile1 = logFile1.replace("\\", "/");
            File logFile2 = new File(logFile1);
            if (!logFile2.exists()) {
                logFile2.mkdir();
            }

            logFile = GCONST.getRootPath() + "log" + File.separator + "hrmImportLog" + File.separator + "人员导入_"
                    + dateFormat.format(new Date()) + ".txt";
            logFile = logFile.replace("\\", "/");
            File file = new File(logFile);

            try {
                file.createNewFile();
            } catch (IOException e) {
                writeLog(e);
            }
        }
        try {
            BufferedWriter out = new BufferedWriter(new FileWriter(logFile, true));
            ImportLog log = new ImportLog();

            SimpleDateFormat timeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String logRecord = "导入时间 " + timeFormat.format(new Date()) + "\r\n";
            out.write(logRecord);
            // 优先输出失败结果
            for (int i = 0; i < resultList.size(); i++) {
                log = (ImportLog) resultList.get(i);

                if (log.getStatus().equals("失败")) {
                    switch (this.keyField) {
                        case "workcode":
                            logRecord = log.getWorkCode();
                            break;
                        case "loginid":
                            logRecord = log.getLoginid();
                            break;
                        case "lastname":
                            logRecord = log.getLastname();
                            break;
                    }
                    logRecord = logRecord + "|" + log.getDepartment() + "|" + log.getOperation() + "|" + log.getStatus()
                            + "|" + log.getReason() + "\r\n";
                    out.write(logRecord);
                }
            }

            // 输出成功结果
            for (int i = 0; i < resultList.size(); i++) {
                log = (ImportLog) resultList.get(i);
                if (log.getStatus().equals("成功")) {

                    switch (this.keyField) {
                        case "workcode":
                            logRecord = log.getWorkCode();
                            break;
                        case "loginid":
                            logRecord = log.getLoginid();
                            break;
                        case "lastname":
                            logRecord = log.getLastname();
                            break;
                    }
                    logRecord = logRecord + "|" + log.getDepartment() + "|" + log.getOperation() + "|" + log.getStatus()
                            + "|" + log.getReason() + "\r\n";
                    out.write(logRecord);
                }
            }
            out.close();
        } catch (IOException e) {
            writeLog(e);
        }
    }

    /**
     * 执行插入操作
     *
     * @param sql
     * @return
     */

    public boolean execSql(String sql) {
        RecordSet recordSet = new RecordSet();
        if (recordSet.execute(sql)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获得查询结果Id
     *
     * @param sql 查询语句
     * @return
     */
    public int getResultSetId(String sql) {
        int currentId = 0;
        RecordSet recordSet = new RecordSet();
        recordSet.execute(sql);
        try {
            while (recordSet.next()) {
                currentId = recordSet.getInt("id");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return currentId;
    }

    /**
     * 创建日志对象
     *
     * @param vo        HrmResourceVoCust 人员信息对象
     * @param operation 操作类型 验证|创建|更新
     * @param status    状态 成功|失败
     * @param reason    失败原因
     * @return ImportLog对象
     */
    public ImportLog createLog(HrmResourceVoCust vo, String operation, String status, String reason) {
        ImportLog log = new ImportLog();

        log.setWorkCode(vo.getWorkcode()); // 编号
        log.setLastname(vo.getLastname()); // 用户名
        log.setLoginid(vo.getLoginid()); // 登录名
        log.setOperation(operation); // 操作类型
        if (vo.getSubcompanyid1() != null && vo.getDepartmentid() != null) {
            log.setDepartment(vo.getSubcompanyid1() + ">" + vo.getDepartmentid()); // 分部部门
        } else {
            log.setDepartment("");
        }
        log.setStatus(status); // 状态，成功、失败
        log.setReason(reason); // 原因

        return log;
    }

    // 整数判断
    public static boolean isInteger(String str) {
        if (str == null) {
            return false;
        }
        Pattern pattern = Pattern.compile("[0-9]+");
        return pattern.matcher(str).matches();
    }

    public int getUserlanguage() {
        return userlanguage;
    }

    public void setUserlanguage(int userlanguage) {
        this.userlanguage = userlanguage;
    }

}
