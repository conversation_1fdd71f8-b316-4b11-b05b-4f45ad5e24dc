package chinasws.webservices;

import weaver.conn.RecordSet;

public class HrmTlevelManager {

    private static boolean isUpdate = true ;

    static {
        new Thread(){
            public void run() {
                while(true){
                    if(!isUpdate){
                        isUpdate = true ;
                        HrmTlevelManager.updateTlevel() ;
                    }

                    try {
                        sleep(1000*60*10) ;
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }

            };
        }.start() ;
    }

    private final static void updateTlevel(){
        RecordSet rs = new RecordSet() ;
        String tmpField = "oracle".equals(rs.getDBType()) ? "templevel" : "level" ;
        rs.executeQuery("select id,"+tmpField+" as tlevel from tempHrmSubCompanyView a where not exists (select 1 from HrmSubCompany b where a.id=b.id and a."+tmpField+"=b.tlevel and b.tlevel is not null) and a."+tmpField+" is not null") ;
        RecordSet updateRs = new RecordSet() ;
        while(rs.next()){
            String id = rs.getString("id") ;
            int level = rs.getInt("tlevel") ;
            updateRs.executeUpdate("update HrmSubCompany set tlevel=? where id=?",level,id) ;
        }

        rs.executeQuery("select id,"+tmpField+" as tlevel from tempHrmDepartmentView a where not exists (select 1 from HrmDepartment b where a.id=b.id and a."+tmpField+"=b.tlevel and b.tlevel is not null) and a."+tmpField+" is not null") ;
        while(rs.next()){
            String id = rs.getString("id") ;
            int level = rs.getInt("tlevel") ;
            updateRs.executeUpdate("update HrmDepartment set tlevel=? where id=?",level,id) ;
        }
    }

    public static void setUpdate(){
        isUpdate =  false ;
    }
}
