package chinasws.webservices;

import java.io.StringReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.engine.common.service.impl.HrmCommonServiceImpl;
import org.jdom.Document;
import org.jdom.Element;
import org.jdom.input.SAXBuilder;

import weaver.common.StringUtil;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.webservice.JobTitleBean;
import weaver.hrm.webservice.OrgXmlBean;
import weaver.general.BaseBean;
import weaver.toolbox.db.recordset.ExecuteUtil;
import weaver.toolbox.db.recordset.QueryUtil;
import weaver.toolbox.json.JSONArray;
import weaver.toolbox.json.JSONObject;

/**
 * 解析xml
 *
 * <AUTHOR>
 */
public class ParseXmlCust extends BaseBean{

	private HashMap	h_orgInfo		 = new HashMap();
	private HashMap	h_orgInfo_add	 = new HashMap();
	private HashMap	h_orgInfo_update = new HashMap();
	private HashMap	h_orgInfo_delete = new HashMap();
	private List	h_delOrg		 = new ArrayList();
	private List	h_addOrg		 = new ArrayList();
	private List	h_updateOrg		 = new ArrayList();

	public HashMap getH_orgInfo(){
		return h_orgInfo;
	}

	public void setH_orgInfo(HashMap info){
		h_orgInfo = info;
	}

	public HashMap getH_orgInfo_add(){
		return h_orgInfo_add;
	}

	public void setH_orgInfo_add(HashMap info){
		h_orgInfo_add = info;
	}

	public HashMap getH_orgInfo_update(){
		return h_orgInfo_update;
	}

	public void setH_orgInfo_update(HashMap info){
		h_orgInfo_update = info;
	}

	public List getH_delOrg(){
		return h_delOrg;
	}

	public void setH_delOrg(List org){
		h_delOrg = org;
	}

	public List getH_addOrg(){
		return h_addOrg;
	}

	public void setH_addOrg(List org){
		h_addOrg = org;
	}

	public List getH_updateOrg(){
		return h_updateOrg;
	}

	public void setH_updateOrg(List org){
		h_updateOrg = org;
	}

	public HashMap getH_orgInfo_delete(){
		return h_orgInfo_delete;
	}

	// 全部账号缓存(swshrguid | id)
	Map<String, String> hrmAllCacheMap = new HashMap<>();
	// 全部离职账号缓存(swshrguid | id)
	Map<String, String> hrmLeaveCacheMap = new HashMap<>();

	public void setH_orgInfo_delete(HashMap h_orgInfo_delete){
		this.h_orgInfo_delete = h_orgInfo_delete;
	}

	public String trimNull(Object o){
		if(o != null){
			return Util.toHtmlForWorkflow(((String) o));
		}

		return "";
	}

	public String getTrimNull(Object o){
		if(o != null){
			return Util.toHtmlForWorkflow(((String) o));
		}

		return null;
	}

	/**
	 * 解析分部,部门xml
	 *
	 * @param xmlStr
	 * 
	 * @throws Exception
	 */
	public void parseOrg(String xmlStr) throws Exception{

		xmlStr = StringUtil.vString(xmlStr);
		Document doc = new Document();
		try{
			writeLog("组织架构xml字符串:" + xmlStr);
			SAXBuilder builder = new SAXBuilder();
			doc = builder.build(new StringReader(xmlStr));
		}catch(Exception e){
			writeLog("装载分部,部门XML字符串时发生异常:" + e);
		}

		Element	rootElement	   = doc.getRootElement();
		Element	orglistElement = rootElement.getChild("orglist");

		if(orglistElement != null){
			List orgList = orglistElement.getChildren("org");
			if(orgList != null){
				for(int i = 0; i < orgList.size(); ++i){
					Element orgElement = (Element) orgList.get(i);
					processOrg("", orgElement);
				}
			}
		}
	}

	/**
	 * 解析分部,部门的xml文件之后放于HashMap中
	 *
	 * @param parentId
	 * @param orgElement
	 * 
	 * @throws Exception
	 */
	protected void processOrg(String parentId, Element orgElement) throws Exception{
		if(orgElement == null){
			return;
		}

		// 动作，add：新增，edit：编辑，delete：删除
		String action	   = trimNull(orgElement.getAttributeValue("action"));
		// 分部,部门编码，必须是唯一值
		String code		   = trimNull(orgElement.getChildText("code"));
		// 分部,部门简称
		String shortname   = trimNull(orgElement.getChildText("shortname"));
		// 分部,部门全称
		String fullname	   = trimNull(orgElement.getChildText("fullname"));
		// 所属分部编码
		String org_code	   = trimNull(orgElement.getChildText("org_code"));
		// 上级分部,部门编码
		String parent_code = trimNull(orgElement.getChildText("parent_code"));
		// 封存标识
		String canceled	   = trimNull(orgElement.getChildText("canceled"));
		// 排序值
		String order	   = trimNull(orgElement.getChildText("order"));

		if(action.equalsIgnoreCase("add")){
			this.h_addOrg.add(code);
		}else if(action.equalsIgnoreCase("edit")){
			this.h_updateOrg.add(code);
		}else if(action.equalsIgnoreCase("1")){
			this.h_delOrg.add(code);
		}

		if((this.h_orgInfo.get(code) == null)){
			OrgXmlBean org = new OrgXmlBean();
			org.setAction(action);
			org.setCode(code);
			org.setShortname(shortname);
			org.setFullname(fullname);
			org.setOrg_code(org_code);
			org.setParent_code(parent_code);
			org.setOrder(order);
			org.setCanceled(canceled);
			this.h_orgInfo.put(code, org);
		}

		List orgList = orgElement.getChildren("org");
		if(orgList != null){
			for(int i = 0; i < orgList.size(); ++i){
				Element childOrgElement = (Element) orgList.get(i);
				processOrg(code, childOrgElement);
			}
		}
	}

	/**
	 * 解析岗位xml
	 *
	 * @param xmlStr
	 * 
	 * @throws Exception
	 */
	public void parseJobTitle(String xmlStr) throws Exception{

		xmlStr = StringUtil.vString(xmlStr);
		Document doc = new Document();
		try{
			writeLog("岗位xml字符串:" + xmlStr);
			SAXBuilder builder = new SAXBuilder();
			doc = builder.build(new StringReader(xmlStr));
		}catch(Exception e){
			writeLog("装载岗位XML字符串时发生异常:" + e);
		}

		Element	rootElement			= doc.getRootElement();
		Element	jobtitlelistElement	= rootElement.getChild("jobtitlelist");

		if(jobtitlelistElement != null){
			List jobtitleList = jobtitlelistElement.getChildren("jobtitle");
			if(jobtitleList != null){
				for(int i = 0; i < jobtitleList.size(); ++i){
					Element	jobtitleElement	= (Element) jobtitleList.get(i);
					// 动作，add：新增，edit：编辑，delete：删除
					String	action			= trimNull(jobtitleElement.getAttributeValue("action"));
					// 岗位编码，必须是唯一值
					String	jobtitlecode	= trimNull(jobtitleElement.getChildText("jobtitlecode"));
					// 岗位简称
					String	jobtitlename	= trimNull(jobtitleElement.getChildText("jobtitlename"));
					// 岗位全称
					String	jobtitleremark	= trimNull(jobtitleElement.getChildText("jobtitleremark"));
					// 岗位所属部门
					String	jobtitledept	= trimNull(jobtitleElement.getChildText("jobtitledept"));

					if(action.equalsIgnoreCase("add")){
						this.h_addOrg.add(jobtitlecode);
					}else if(action.equalsIgnoreCase("edit")){
						this.h_updateOrg.add(jobtitlecode);
					}else if(action.equalsIgnoreCase("delete")){
						this.h_delOrg.add(jobtitlecode);
					}

					if((this.h_orgInfo.get(jobtitlecode) == null)){
						JobTitleBean jobxml = new JobTitleBean();
						jobxml.setAction(action);
						jobxml.set_code(jobtitlecode);
						jobxml.set_shortname(jobtitlename);
						jobxml.set_fullname(jobtitleremark);
						jobxml.set_departmentid(jobtitledept);
						this.h_orgInfo.put(jobtitlecode, jobxml);
					}
				}
			}
		}
	}

	/**
	 * 解析人员xml
	 *
	 * @param xmlStr
	 * 
	 * @throws Exception
	 */
	public void parseHrmResource(String xmlStr) throws Exception{
		// 2021-08-01 tangss
		// 推翻之前的同步逻辑，调整逻辑为
		// 1、对于IDM传入的人员[主账号信息]，如果在OA中检查是否存在相同的[工号]，存在则更新该工号的[主账号信息]，不存在则新增主账号
		// 2、对于IDM传入的人员[次账号信息]，如果在OA中存在相同的swshrguid数据则更新对应的[次账号信息]，不存在则新增次账号
		// 3、对于在OA中存在的，且在IDM中未传入的[次账号信息]，则做离职处理
		// 4、对于IDM传入的swshrguid不包含岗位信息的数据，把OA中对应工号所有的账号离职
		// 5、每次更新完数据，次账号的密码都要同步为主账号的密码
		
		// 2021-10-18 tangss
		// 6、新增的次账号，需要关联上主账号信息

		writeLog("parseHrmResource cust -- ");

		RecordSet rs  = new RecordSet();
		String	  sql = "select swshrguid,id,loginid,workcode,accounttype,status from hrmResource where status<=5 order by id desc";
		rs.execute(sql);

		while(rs.next()){
			String userId	   = Util.null2String(rs.getString("id"));
			String swshrguid   = Util.null2String(rs.getString("swshrguid"));
			String status	   = Util.null2String(rs.getString("status"));

			if("5".equals(status)){
				hrmLeaveCacheMap.put(swshrguid, userId);
			}else{
				if(!swshrguid.equals("")){
					// 缓存全部人员
					hrmAllCacheMap.put(swshrguid, userId);
				}
			}
		}

		writeLog("缓存全部人员 hrmAllCacheMap -- " + hrmAllCacheMap.size());

		xmlStr = StringUtil.vString(xmlStr);
		Document doc = new Document();
		try{
			writeLog("人员xml字符串:" + xmlStr);
			SAXBuilder builder = new SAXBuilder();
			doc = builder.build(new StringReader(xmlStr));
		}catch(Exception e){
			writeLog("装载人员XML字符串时发生异常:" + e);
		}

		Element	rootElement	   = doc.getRootElement();
		Element	hrmlistElement = rootElement.getChild("hrmlist");

		if(hrmlistElement != null){
			List hrmList = hrmlistElement.getChildren("hrm");
			if(hrmList != null){
				// 2022-11-20
				// 遍历HR传入的人员列表，根据hrid，去OA中查找同一用户的所有账号进行预处理
				Map<String,String> hrmMap = new HashMap<>();
				for(int i = 0; i < hrmList.size(); ++i){
					Element	hrmElement = (Element) hrmList.get(i);
					String	swshrguid  = trimNull(hrmElement.getChildText("swshrguid"));
					String  hrid	   = swshrguid.split("\\|")[0];

					// 相同的hrid只预处理一次
					if(!hrmMap.containsKey(hrid)){
						hrmMap.put(hrid, hrid);

						// 当前hrid传入的所有账号
						Map<String,Element> hrmElementMap = new HashMap<>();
						for(int j = 0; j < hrmList.size(); j++){
							Element	curHrmElement = (Element) hrmList.get(j);
							String	curSwshrguid  = trimNull(curHrmElement.getChildText("swshrguid"));
							if(!hrmElementMap.containsKey(curSwshrguid)){
								hrmElementMap.put(curSwshrguid, curHrmElement);
							}
						}

						// 从OA中获取相同hrid的所有账号
						String qryAllAccountSql = "select id,swshrguid,accounttype from hrmresource where swshrguid like '" + hrid + "|%'";
						JSONArray allAccountArr = QueryUtil.doQuery(qryAllAccountSql, "", "id,swshrguid,accounttype");

						// 数据预处理
						preprocessData(hrmElementMap, allAccountArr);
					}
				}

				// 处理增删改查逻辑
				for(int i = 0; i < hrmList.size(); ++i){
					Element	hrmElement	   = (Element) hrmList.get(i);
					String	swshrguid	   = trimNull(hrmElement.getChildText("swshrguid"));
					String	residentplace  = trimNull(hrmElement.getChildText("residentplace"));
					String	lastname	   = trimNull(hrmElement.getChildText("lastname"));
					String	lastnamePinyin = new HrmCommonServiceImpl().generateQuickSearchStr(lastname);

					writeLog("swshrguid ==> " + swshrguid);
					if(swshrguid != null){
						swshrguid = swshrguid.trim();
					}

					writeLog("residentplace ==> " + residentplace);

					String action;

					if(!swshrguid.contains("|")){
						// 主账号离职
						this.h_delOrg.add(swshrguid);
						action = "delete";
					}else{
						if(hrmAllCacheMap.containsKey(swshrguid)){
							writeLog("edit 111");
							action = "edit";
						}else{
							writeLog("add 111");
							action = "add";
						}

						// 2021-11-02 tangss 离职账号恢复使用
						if(hrmLeaveCacheMap.containsKey(swshrguid)){
							action = "edit";
						}
					}

					writeLog("action ==> " + action);

					if((this.h_orgInfo.get(swshrguid) == null)){
						HrmResourceVoCust hrmvo = new HrmResourceVoCust();

						hrmvo.setSwshrguid(swshrguid);
						hrmvo.setAccounttype("1".equals(residentplace) ? "0" : "1");
						hrmvo.setSubcompanyid1(getTrimNull(hrmElement.getChildText("subcompany")));
						hrmvo.setDepartmentid(getTrimNull(hrmElement.getChildText("department")));
						hrmvo.setWorkcode(getTrimNull(hrmElement.getChildText("workcode")));
						hrmvo.setLastname(lastname);
						hrmvo.setPinyinlastname(lastnamePinyin);
						hrmvo.setEcology_pinyin_search(lastnamePinyin);
						hrmvo.setLoginid(getTrimNull(hrmElement.getChildText("loginid")));
						hrmvo.setPassword(getTrimNull(hrmElement.getChildText("password")));
						hrmvo.setSeclevel(getTrimNull(hrmElement.getChildText("seclevel")));
						hrmvo.setSex(getTrimNull(hrmElement.getChildText("sex")));
						hrmvo.setJobtitle(getTrimNull(hrmElement.getChildText("jobtitle")));
						hrmvo.setJobactivityid(getTrimNull(hrmElement.getChildText("jobactivityid")));
						hrmvo.setJobgroupid(getTrimNull(hrmElement.getChildText("jobgroupid")));
						hrmvo.setJobcall(getTrimNull(hrmElement.getChildText("jobcall")));
						hrmvo.setJoblevel(getTrimNull(hrmElement.getChildText("joblevel")));
						hrmvo.setJobactivitydesc(getTrimNull(hrmElement.getChildText("jobactivitydesc")));
						hrmvo.setManagerid(getTrimNull(hrmElement.getChildText("managerid")));
						hrmvo.setAssistantid(getTrimNull(hrmElement.getChildText("assistantid")));

						String statusTemp = getTrimNull(hrmElement.getChildText("status"));
						statusTemp = statusTemp.equals("在职") ? "1" : "5";
						hrmvo.setStatus(statusTemp);

						hrmvo.setLocationid(getTrimNull(hrmElement.getChildText("locationid")));
						hrmvo.setWorkroom(getTrimNull(hrmElement.getChildText("workroom")));
						hrmvo.setTelephone(getTrimNull(hrmElement.getChildText("telephone")));
						hrmvo.setMobile(getTrimNull(hrmElement.getChildText("mobile")));
						hrmvo.setMobilecall(getTrimNull(hrmElement.getChildText("mobilecall")));
						hrmvo.setFax(getTrimNull(hrmElement.getChildText("fax")));
						hrmvo.setEmail("");
						hrmvo.setSystemlanguage(getTrimNull(hrmElement.getChildText("systemlanguage")));
						hrmvo.setBirthday(getTrimNull(hrmElement.getChildText("birthday")));
						hrmvo.setFolk(getTrimNull(hrmElement.getChildText("folk")));
						hrmvo.setNativeplace(getTrimNull(hrmElement.getChildText("nativeplace")));
						hrmvo.setResidentplace(getTrimNull(hrmElement.getChildText("residentplace")));
						hrmvo.setCertificatenum(getTrimNull(hrmElement.getChildText("certificatenum")));
						hrmvo.setMaritalstatus(getTrimNull(hrmElement.getChildText("maritalstatus")));
						hrmvo.setPolicy(getTrimNull(hrmElement.getChildText("policy")));
						hrmvo.setBememberdate(getTrimNull(hrmElement.getChildText("bememberdate")));
						hrmvo.setBepartydate(getTrimNull(hrmElement.getChildText("bepartydate")));
						hrmvo.setIslabouunion(getTrimNull(hrmElement.getChildText("islabouunion")));
						hrmvo.setEducationlevel(getTrimNull(hrmElement.getChildText("educationlevel")));
						hrmvo.setDegree(getTrimNull(hrmElement.getChildText("degree")));
						hrmvo.setHealthinfo(getTrimNull(hrmElement.getChildText("healthinfo")));
						hrmvo.setHeight(getTrimNull(hrmElement.getChildText("height")));
						if(hrmElement.getChildText("weight") != null){
							hrmvo.setWeight(new Integer(Util.getIntValue(getTrimNull(hrmElement.getChildText("weight")), 0)));
						}
						hrmvo.setHomeaddress(getTrimNull(hrmElement.getChildText("homeaddress")));
						hrmvo.setTempresidentnumber(getTrimNull(hrmElement.getChildText("tempresidentnumber")));
						hrmvo.setDsporder(getTrimNull(hrmElement.getChildText("dsporder")));

						this.h_orgInfo.put(swshrguid, hrmvo);

						writeLog("hrmvo getStatus ==> " + hrmvo.getStatus());

						if(action.equalsIgnoreCase("add")){
							this.h_orgInfo_add.put(swshrguid, hrmvo);
						}else if(action.equalsIgnoreCase("edit")){
							this.h_orgInfo_update.put(swshrguid, hrmvo);
						}
					}
				}

				rs.execute(sql);
				ArrayList leaveList = getAllLeave(hrmList, rs);
				for(int i = 0; i < leaveList.size(); i++){
					writeLog("h_delOrg new ==> " + leaveList.get(i).toString());
					this.h_delOrg.add(leaveList.get(i).toString());
				}
			}
		}
	}

	private void preprocessData(Map<String, Element> hrmElementMap, JSONArray allAccountArr) {
		// 1：HR传入的账号的hrid在OA中不存在，则不进行预处理
		if(allAccountArr.size() == 0){
			return;
		}

		// 2：HR传入的账号在OA中存在相同的hrid
		//    2.1：HR传入的主账号swshrguid，在OA中不存在，则将OA中主账号的swshrguid更新为HR传入的swshrguid，同时备份原swshrguid到outkey字段
		//    2.2：HR传入的主账号swshrguid，在OA中存在且也是主账号，则不进行预处理
		//    2.3：HR传入的主账号swshrguid，在OA中存在且是次账号
		//        2.3.1：HR中只传入一个账号，则将OA中的主账号swshrguid更新为HR传入的swshrguid，同时备份原swshrguid到outkey字段，再将OA中的次账号的swshrguid设置为作废状态(加上#invalid)
		//        2.3.2：主次对调的情况，直接将OA中主次账号的swshrguid互换
		//        2.3.3：HR传入的次账号在OA中不存在 或者 存在但不是OA的主账号，则将OA中的主账号swshrguid更新为HR传入的swshrguid，同时备份原swshrguid到outkey字段

		// 从hrmElementMap中找出主账号
		Element mainHrmElement = null;
		for (Element hrmElement : hrmElementMap.values()) {
			if("1".equals(trimNull(hrmElement.getChildText("residentplace")))){
				mainHrmElement = hrmElement;
				break;
			}
		}
		String hrMainSwshrguid = trimNull(mainHrmElement.getChildText("swshrguid"));

		// 从allAccountArr中找出主账号
		JSONObject mainAccount = null;
		for (int i = 0; i < allAccountArr.size(); i++) {
			JSONObject account = allAccountArr.getJSONObject(i);
			if("0".equals(trimNull(account.getStr("accounttype")))){
				mainAccount = account;
				break;
			}
		}
		String oaMainSwshrguid = trimNull(mainAccount.getStr("swshrguid"));
		String oaMainId        = trimNull(mainAccount.getStr("id"));

		// HR传入的主账号是否再OA中存在
		boolean isHrMainSwshrguidExist = false;
		for(int i = 0; i < allAccountArr.size(); i++){
			JSONObject account = allAccountArr.getJSONObject(i);
			if(hrMainSwshrguid.equals(trimNull(account.getStr("swshrguid")))){
				isHrMainSwshrguidExist = true;
				break;
			}
		}

		if(!isHrMainSwshrguidExist){
			// 2.1：HR传入的主账号swshrguid，在OA中不存在，则将OA中主账号的swshrguid更新为HR传入的swshrguid，同时备份原swshrguid到outkey字段
			String updateSql21 = "update hrmresource set outkey = swshrguid, swshrguid = '" + hrMainSwshrguid + "' where id = " + oaMainId;
			writeLog("preprocessData updateSql21 ==> " + updateSql21);
			if(ExecuteUtil.executeSql(updateSql21)){
				hrmAllCacheMap.put(hrMainSwshrguid,oaMainId);
			}
		}else{
			if(!hrMainSwshrguid.equals(oaMainSwshrguid)){
				// 2.3.1：HR中只传入一个账号，则将OA中的主账号swshrguid更新为HR传入的swshrguid，同时备份原swshrguid到outkey字段，再将OA中的次账号的swshrguid设置为作废状态(加上#invalid)
				if(hrmElementMap.size() == 1){
					String updateSql231_1 = "update hrmresource set outkey = swshrguid, swshrguid = '" + hrMainSwshrguid + "' where id = " + oaMainId;
					writeLog("preprocessData updateSql231_1 ==> " + updateSql231_1);
					ExecuteUtil.executeSql(updateSql231_1);

					String  hrid = hrMainSwshrguid.split("\\|")[0];
					String updateSql231_2 = "update hrmresource set swshrguid = concat(swshrguid,'#invalid') where swshrguid like '" + hrid + "|%' and id <> " + oaMainId;
					writeLog("preprocessData updateSql231_2 ==> " + updateSql231_2);
					ExecuteUtil.executeSql(updateSql231_2);
				}

				// 判断HR传入的次账号知否是OA的主账号
				boolean isHrSubSwshrguidOaMain = false;
				for(Element hrmElement : hrmElementMap.values()){
					if("0".equals(trimNull(hrmElement.getChildText("residentplace")))){
						String hrSubSwshrguid = trimNull(hrmElement.getChildText("swshrguid"));
						if(hrSubSwshrguid.equals(oaMainSwshrguid)){
							isHrSubSwshrguidOaMain = true;
							break;
						}
					}
				}

				if(isHrSubSwshrguidOaMain){
					// 获取HR传入的主账号对应的OA次账号的Id
					String oaSubId = "";
					for(int i = 0; i < allAccountArr.size(); i++){
						JSONObject account = allAccountArr.getJSONObject(i);
						if(hrMainSwshrguid.equals(trimNull(account.getStr("swshrguid")))){
							oaSubId = trimNull(account.getStr("id"));
							break;
						}
					}

					// 2.3.2：主次对调的情况，直接将OA中主次账号的swshrguid互换
					String updateSql232_1 = "update hrmresource set outkey = swshrguid, swshrguid = '" + hrMainSwshrguid + "' where id = " + oaMainId;
					writeLog("preprocessData updateSql232_1 ==> " + updateSql232_1);
					ExecuteUtil.executeSql(updateSql232_1);

					String updateSql232_2 = "update hrmresource set outkey = swshrguid, swshrguid = '" + oaMainSwshrguid + "' where id = " + oaSubId;
					writeLog("preprocessData updateSql232_2 ==> " + updateSql232_2);
					ExecuteUtil.executeSql(updateSql232_2);
				}else{
					// 2.3.3：HR传入的次账号在OA中不存在 或者 存在但不是OA的主账号，则将OA中的主账号swshrguid更新为HR传入的swshrguid，同时备份原swshrguid到outkey字段
					String updateSql233_1 = "update hrmresource set outkey = swshrguid, swshrguid = '" + hrMainSwshrguid + "' where id = " + oaMainId;
					writeLog("preprocessData updateSql233_1 ==> " + updateSql233_1);
					ExecuteUtil.executeSql(updateSql233_1);

					// 还要将HR传入的主账号swshrguid对应的OA次账号作废
					String updateSql233_2 = "update hrmresource set swshrguid = concat(swshrguid,'#invalid') where swshrguid = '" + hrMainSwshrguid + "' and id <> " + oaMainId;
					writeLog("preprocessData updateSql233_2 ==> " + updateSql233_2);
					ExecuteUtil.executeSql(updateSql233_2);
				}
			}
		}
	}

	/**
	 * 1、遍历所有IDM同步的数据，获取所有的工号(如果是主账号离职，直接跳过)
	 * 2、用工号先去人员表查找这个账号关联的所有账号
	 * 3、在(2)中所有的账号，且不在IDM传入数据的，全部离职
	 *
	 * @param hrmList
	 * @param hrmRs
	 * 
	 * @return
	 */
	public ArrayList getAllLeave(List hrmList, RecordSet hrmRs){
		ArrayList list = new ArrayList<String>();

		if(hrmList != null){
			// 1、遍历所有IDM同步的数据，获取所有的hrid(如果是主账号离职，直接跳过)
			Map<String, String> hridMap = new HashMap<>();
			for(int i = 0; i < hrmList.size(); ++i){
				Element	hrmElement = (Element) hrmList.get(i);
				String	swshrguid  = trimNull(hrmElement.getChildText("swshrguid"));
				if(swshrguid.contains("|")){
					String hrid = swshrguid.split("\\|")[0];
					if(!hridMap.containsKey(hrid)){
						hridMap.put(hrid, hrid);
					}
				}
			}

			writeLog("hridMap ==> " + hridMap);

			// 2、用hrid先去人员表查找这个账号关联的所有账号
			for(String key : hridMap.keySet()){
				hrmRs.beforFirst();
				while(hrmRs.next()){
					String swshrguid_oa = Util.null2String(hrmRs.getString("swshrguid"));
					String hrid_oa      = swshrguid_oa.split("\\|")[0];

					if(key.equals(hrid_oa)){
						writeLog("swshrguid_oa ==> " + swshrguid_oa);
						boolean leave = true;

						// 3、在(2)中所有的账号，且不在IDM传入数据的，全部离职
						for(int i = 0; i < hrmList.size(); i++){
							Element	hrmElement 	  = (Element)hrmList.get(i);
							String	swshrguid_idm = trimNull(hrmElement.getChildText("swshrguid"));
							writeLog("swshrguid_idm ==> " + swshrguid_idm);
							if(swshrguid_oa.equals(swshrguid_idm)){
								leave = false;
							}
						}

						if(leave){
							list.add(swshrguid_oa);
						}
					}
				}
			}
		}

		return list;
	}

}
