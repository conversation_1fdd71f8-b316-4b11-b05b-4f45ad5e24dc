package gddq;

import org.apache.commons.lang.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.Iterator;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.resource.ResourceComInfo;
import weaver.interfaces.schedule.BaseCronJob;

public class HrDepPosEmpCron extends BaseCronJob {

    @Override
    public void execute() {
        BaseBean          bean = new BaseBean();
        InputStream       is   = null;
        InputStreamReader isr  = null;
        BufferedReader    br   = null;
        OutputStream      os   = null;

        try {
            //第一步：创建服务地址
            String interfaceurl = bean.getPropValue("gddqinterfaceurl", "interfaceurl");
            URL    url          = new URL(interfaceurl);

            //第二步：打开一个通向服务地址的连接
            HttpURLConnection connection = (HttpURLConnection)url.openConnection();
            //第三步：设置参数
            //3.1发送方式设置：POST必须大写
            connection.setRequestMethod("POST");
            //3.2设置数据格式：content-type
            connection.setRequestProperty("content-type", "text/xml;charset=utf-8");
            //3.3设置输入输出，因为默认新创建的connection没有读写权限，
            connection.setDoInput(true);
            connection.setDoOutput(true);

            //第四步：组织SOAP数据，发送请求,id为空获取所有的部门信息
            String soapXML = getDepXML("");
            //将信息以流的方式发送出去
            os = connection.getOutputStream();
            os.write(soapXML.getBytes());

            //第五步：接收服务端响应，打印
            int depResponseCode = connection.getResponseCode();

            if(200 == depResponseCode){
                //表示服务端响应成功
                //获取当前连接请求返回的数据流
                is  = connection.getInputStream();
                isr = new InputStreamReader(is,"UTF-8");
                br  = new BufferedReader(isr);

                StringBuilder sb = new StringBuilder();
                String temp;
                while(null != (temp = br.readLine())){
                    sb.append(temp);
                }
                Document documentHelper = DocumentHelper.parseText(sb.toString());
                Element  rootElement    = documentHelper.getRootElement();
                Element  body           = rootElement.element("Body").element("GetDepartmentResponse").element("GetDepartmentResult");
                Iterator iterator       = body.elementIterator();

                ArrayList<String> subidlist = new ArrayList<>();
                ArrayList<String> depidlist = new ArrayList<>();
                RecordSet rs = new RecordSet();
                while (iterator.hasNext()){
                    Element el   = (Element)iterator.next();
                    String  id   = el.element("ID").getText();
                    String  type = el.element("Type").getText();
                    String  pid  = el.element("PID").getText();
                    String  name = el.element("Name").getText();

                    //查询数据库中是否已经存在这条数据
                    boolean isExist = isExist(id,type);
                    String  sql     = "";
                    if(isExist){
                        //存在则更新
                        //为C存subcompany表 为O存department表
                        if("C".equalsIgnoreCase(type)){
                            subidlist.add(id);
                            sql = "update hrmsubcompany set pid='"+pid+"',subcompanyname='"+name+"',subcompanydesc='"+name+"' where subcompanycode='"+id+"'";
                        }else if("O".equalsIgnoreCase(type)){
                            depidlist.add(id);
                            sql = "update hrmdepartment set pid='"+pid+"',departmentname='"+name+"',departmentmark='"+name+"' where departmentcode='"+id+"'";
                        }
                    }else{
                        //不存在则插入
                        //为C存subcompany表 为O存department表
                        if("C".equalsIgnoreCase(type)){
                            subidlist.add(id);
                            sql = "insert into hrmsubcompany (subcompanycode,pid,subcompanyname,subcompanydesc) values('"+id+"','"+pid+"','"+name+"','"+name+"')";
                        }else if("O".equalsIgnoreCase(type)){
                            depidlist.add(id);
                            sql = "insert into hrmdepartment (departmentname,departmentmark,departmentcode,pid) values('"+name+"','"+name+"','"+id+"','"+pid+"')";
                        }
                    }
                    rs.execute(sql);
                }

                //分部，部门封存
                //1.先查出数据库中已经有了哪些部门，分部的id
                ArrayList<String> allsubidList = new ArrayList<>();
                ArrayList<String> alldepidList = new ArrayList<>();
                //分部
                String subsql = "select subcompanycode from hrmsubcompany";
                rs.execute(subsql);
                while (rs.next()){
                    String subcompanycode = Util.null2String(rs.getString("subcompanycode"));
                    if(StringUtils.isNotBlank(subcompanycode)){
                        allsubidList.add(subcompanycode);
                    }
                }
                //部门
                String depsql = "select departmentcode from hrmdepartment";
                rs.execute(depsql);
                while (rs.next()){
                    String departmentcode = Util.null2String(rs.getString("departmentcode"));
                    if(StringUtils.isNotBlank(departmentcode)){
                        alldepidList.add(departmentcode);
                    }
                }

                //2.记录有哪些部门是已经传过来的
                //3.两个集合做差集
                //4.对差集结果进行封存
                allsubidList.removeAll(subidlist);
                for(int i=0; i<allsubidList.size(); i++){
                    String updatesubsql = "update hrmsubcompany set canceled='1' where subcompanycode='"+allsubidList.get(i)+"'";
                    rs.execute(updatesubsql);
                }
                alldepidList.removeAll(depidlist);
                for(int i=0;i<alldepidList.size();i++){
                    String updatedepsql="update HrmDepartment set canceled='1' where departmentcode='"+alldepidList.get(i)+"'";
                    rs.execute(updatedepsql);
                }
            }

            //第二步：打开一个通向服务地址的连接
            HttpURLConnection connection2 = (HttpURLConnection) url.openConnection();

            //第三步：设置参数
            //3.1发送方式设置：POST必须大写
            connection2.setRequestMethod("POST");
            //3.2设置数据格式：content-type
            connection2.setRequestProperty("content-type", "text/xml;charset=utf-8");
            connection2.setRequestProperty("Content-Length","0");
            //3.3设置输入输出，因为默认新创建的connection没有读写权限，
            connection2.setDoInput(true);
            connection2.setDoOutput(true);

            //第四步：组织SOAP数据，发送请求,id为空获取所有的部门信息
            String possoapXML = getPosXML();
            //将信息以流的方式发送出去
            os = connection2.getOutputStream();
            os.write(possoapXML.getBytes());
            //第五步：接收服务端响应，打印
            int posresponseCode = connection2.getResponseCode();
            //将信息以流的方式发送出去

            //第五步：接收服务端响应，打印
            ArrayList<String> posidlist = new ArrayList<>();
            if(200 == posresponseCode){
                is  = connection2.getInputStream();
                isr = new InputStreamReader(is,"UTF-8");
                br  = new BufferedReader(isr);
                StringBuilder possb = new StringBuilder();
                String temp;
                while(null != (temp = br.readLine())){
                    possb.append(temp);
                }

                Document  documentHelper = DocumentHelper.parseText(possb.toString());
                Element   rootElement    = documentHelper.getRootElement();
                Element   body           = rootElement.element("Body").element("GetPositionResponse").element("GetPositionResult");
                Iterator  iterator       = body.elementIterator();
                RecordSet rs             = new RecordSet();
                while (iterator.hasNext()){
                    Element el   = (Element)iterator.next();
                    String  id   = el.element("ID").getText();
                    String  name = el.element("Name").getText();
                    //查询数据库中是否已经存在这条数据
                    boolean   isExist = isPosExist(id);
                    String    sql;
                    if(isExist){
                        //存在则更新
                        posidlist.add(id);
                        sql = "update hrmjobtitles set jobactivityid=14,jobtitlemark='"+name+"',jobtitlename='"+name+"' where jobtitlecode='"+id+"'";
                    }else{
                        //不存在则插入 jobactivityid=14
                        posidlist.add(id);
                        sql = "insert into hrmjobtitles (jobtitlemark,jobtitlename,jobtitlecode,jobactivityid) values('"+name+"','"+name+"','"+id+"',14)";
                    }
                    rs.execute(sql);
                }

                //岗位封存
                ArrayList<String> allPosidList = new ArrayList<>();
                String            jobsql   = "select jobtitlecode from hrmjobtitles";
                rs.execute(jobsql);
                while (rs.next()){
                    String jobtitlecode = Util.null2String(rs.getString("jobtitlecode"));
                    if(StringUtils.isNotBlank(jobtitlecode)){
                        allPosidList.add(jobtitlecode);
                    }
                }
                allPosidList.removeAll(posidlist);
                for(int i=0;i<allPosidList.size();i++){
                    String updatepos="update hrmjobtitles set canceled='1' where jobtitlecode='"+allPosidList.get(i)+"'";
                    rs.execute(updatepos);
                }
                //for(String id:allPosidList){
                //    String updatepos = "update hrmjobtitles set canceled='1' where jobtitlecode='"+id+"'";
                //    rs.execute(updatepos);
                //}

            }

            //第二步：打开一个通向服务地址的连接
            HttpURLConnection connection3 = (HttpURLConnection) url.openConnection();

            //第三步：设置参数
            //3.1发送方式设置：POST必须大写
            connection3.setRequestMethod("POST");
            //3.2设置数据格式：content-type
            connection3.setRequestProperty("content-type", "text/xml;charset=utf-8");
            connection3.setRequestProperty("Content-Length","0");
            //3.3设置输入输出，因为默认新创建的connection没有读写权限，
            connection3.setDoInput(true);
            connection3.setDoOutput(true);

            //第四步：组织SOAP数据，发送请求,id为空获取所有的部门信息
            String empsoapXML = getEmpXML("");
            //将信息以流的方式发送出去
            os = connection3.getOutputStream();
            os.write(empsoapXML.getBytes());
            //第五步：接收服务端响应，打印
            int               empresponseCode = connection3.getResponseCode();
            ArrayList<String> newcodeList     = new ArrayList<>();
            ArrayList<String> oldcodeList     = new ArrayList<>();
            if(200 == empresponseCode) {
                //表示服务端响应成功
                //获取当前连接请求返回的数据流
                is  = connection3.getInputStream();
                isr = new InputStreamReader(is, "UTF-8");
                br  = new BufferedReader(isr);
                StringBuilder empsb = new StringBuilder();
                String        temp;
                while (null != (temp = br.readLine())) {
                    empsb.append(temp);
                }

                Document  documentHelper = DocumentHelper.parseText(empsb.toString());
                Element   rootElement    = documentHelper.getRootElement();
                Element   body           = rootElement.element("Body").element("GetEmployeeResponse").element("GetEmployeeResult");
                Iterator  iterator       = body.elementIterator();
                String    hrmsql         = "select max(id) id from HrmResource";
                RecordSet rs             = new RecordSet();
                rs.execute(hrmsql);
                int maxId = 0;
                if(rs.next()){
                    maxId = rs.getInt("id");
                }

                int i = 0;
                while (iterator.hasNext()) {
                    String workcode         = "";
                    String PositionID       = "";
                    String DepartmentID     = "";
                    String CostCenterID     = "";
                    String ManagerID        = "";
                    String CompanyID        = "";
                    String LastName         = "";
                    String Birthday         = "";
                    String Nationality      = "";
                    String Sex              = "";
                    String Status           = "";
                    String UseKind          = "";
                    String MaritalStatus    = "";
                    String RegresidentPlace = "";
                    String CompanyStartDate = "";
                    String CompanyWorkYear  = "";
                    String StartDate        = "";
                    String EndDate          = "";
                    String companyName      = "";
                    String Mobile           = "";
                    String Telephone        = "";
                    String HomeAddress      = "";
                    String JobLevel         = "";
                    String CertificateNum   = "";
                    String EducationLevel   = "";
                    String ProbationEndDate = "";
                    String companyid        = "";
                    String Bank             = "";
                    String Payee            = "";
                    String AccountNo        = "";

                    i++;

                    Element el = (Element) iterator.next();
                    Element id = el.element("ID");
                    if(null != id){
                        workcode = el.element("ID").getText();
                        newcodeList.add(workcode);
                    }
                    Element last= el.element("LastName");
                    if(null != last){
                        LastName = el.element("LastName").getText();
                    }
                    Element Posi= el.element("PositionID");
                    if(null != Posi){
                        PositionID = el.element("PositionID").getText();
                    }
                    Element Depart = el.element("DepartmentID");
                    if(null != Depart){
                        DepartmentID = el.element("DepartmentID").getText();
                    }
                    Element Manager = el.element("ManagerID");
                    if(null != Manager){
                        ManagerID = el.element("ManagerID").getText();
                    }
                    Element CostCenter = el.element("CostCenterID");
                    if(null != CostCenter){
                        CostCenterID = el.element("CostCenterID").getText();
                    }
                    Element Company = el.element("CompanyID");
                    if(null != Company){
                        CompanyID   = el.element("CompanyID").getText();
                        companyid   = changeCompanyid(CompanyID);
                        companyName = getCompanyNameByCode(CompanyID);
                    }
                    Element Birth = el.element("Birthday");
                    if(null != Birth){
                        Birthday = el.element("Birthday").getText();
                    }
                    Element Nationa = el.element("Nationality");
                    if(null != Nationa){
                        Nationality = el.element("Nationality").getText();
                        Nationality = getCodeByCountryName(Nationality);
                    }
                    Element se = el.element("Sex");
                    if(null != se){
                        Sex = el.element("Sex").getText();
                        Sex = Sex.equalsIgnoreCase("M")?"0":"1";
                    }
                    Element Sta = el.element("Status");
                    if(null != Sta){
                        Status = el.element("Status").getText();
                    }
                    Element use = el.element("UseKind");
                    if(null != use){
                        UseKind = el.element("UseKind").getText();
                        UseKind = getIdByUseKind(UseKind);
                    }
                    Element Marital = el.element("MaritalStatus");
                    if(null != Marital){
                        MaritalStatus = el.element("MaritalStatus").getText();
                    }
                    Element Job = el.element("JobLevel");
                    if(null != Job){
                        JobLevel = el.element("JobLevel").getText();
                    }
                    Element Certifi = el.element("CertificateNum");
                    if(null != Certifi){
                        CertificateNum = el.element("CertificateNum").getText();
                    }
                    Element Mob = el.element("Mobile");
                    if(null != Mob){
                        Mobile = el.element("Mobile").getText();
                    }
                    Element Teleph = el.element("Telephone");
                    if(null != Teleph){
                        Telephone = el.element("Telephone").getText();
                    }
                    Element HomeAddr = el.element("HomeAddress");
                    if(null != HomeAddr){
                        HomeAddress = el.element("HomeAddress").getText();
                    }
                    Element Education = el.element("EducationLevel");
                    if(null != Education){
                        EducationLevel = el.element("EducationLevel").getText();
                        EducationLevel = getEducLevIdByName(EducationLevel);
                    }
                    Element ProbationEndD = el.element("ProbationEndDate");
                    if(null != ProbationEndD){
                        ProbationEndDate = el.element("ProbationEndDate").getText();
                    }
                    Element regPlace = el.element("RegresidentPlace");
                    if(null != regPlace){
                        RegresidentPlace = el.element("RegresidentPlace").getText();
                    }
                    Element CompanyStart = el.element("CompanyStartDate");
                    if(null != CompanyStart){
                        CompanyStartDate = el.element("CompanyStartDate").getText();
                    }
                    Element CompanyWork = el.element("CompanyWorkYear");
                    if(null != CompanyWork){
                        CompanyWorkYear = el.element("CompanyWorkYear").getText();
                    }
                    Element StartD = el.element("StartDate");
                    if(null != StartD){
                        StartDate = el.element("StartDate").getText();
                    }
                    Element EndDat = el.element("EndDate");
                    if(null != EndDat){
                        EndDate = el.element("EndDate").getText();
                    }
                    Element BankElement = el.element("Bank");
                    if(null != BankElement){
                        Bank = BankElement.getText();
                    }
                    Element PayElement = el.element("Payee");
                    if(null != PayElement){
                        Payee = PayElement.getText();
                    }
                    Element AccountNoElement = el.element("AccountNo");
                    if(null != AccountNoElement){
                        AccountNo = AccountNoElement.getText();
                    }

                    //查询数据库中是否已经存在这条数据
                    boolean isexist     = isEmpExist(workcode);
                    //若为false则是主账号
                    boolean accountflag = isAccount(workcode);
                    String  hrmresoursql;
                    //只更新主账号
                    if(!accountflag){
                        if(isexist ){
                            //存在则更新
                            hrmresoursql = "update hrmresource set lastname='" + LastName +"',sapjobtitle='"+PositionID+ "',sapdepartmentid='"+DepartmentID+
                                    "',sapmanagerid='"+ManagerID+"',birthday='"+Birthday+"',nationality='"+Nationality+"',sex='"+Sex+"',status='"+Status+
                                    "',usekind='"+UseKind+"',maritalstatus='"+MaritalStatus+"',regresidentplace='"+RegresidentPlace+
                                    "',companystartdate='"+CompanyStartDate+"',companyworkyear='"+CompanyWorkYear+
                                    "',startdate='"+StartDate+"',enddate='"+EndDate+"',locationid='2',joblevel='"+JobLevel+"',certificatenum='"+CertificateNum+
                                    "',mobile='"+Mobile+"',telephone='"+Telephone+"',homeaddress='"+HomeAddress+"',educationlevel='"+EducationLevel+
                                    "',probationenddate='"+ProbationEndDate+"',costid='"+CostCenterID+"',companyid='"+companyid+"',companyname='"+companyName+
                                    "',bank='"+Bank+"',payee='"+Payee+"',accountno='"+AccountNo+"',loginid='"+workcode+"' where workcode='" + workcode + "'";
                        }else{
                            //不存在则插入
                            maxId+=1;
                            hrmresoursql="insert into hrmresource (id,workcode,lastname,sapjobtitle,sapdepartmentid,sapmanagerid,birthday,nationality" +
                                    ",sex,status,usekind,maritalstatus,regresidentplace,companystartdate" +
                                    ",companyworkyear,startdate,enddate,locationid,joblevel,certificatenum,mobile,telephone,homeaddress,educationlevel" +
                                    ",probationenddate,costid,companyname,companyid,bank,payee,accountno,loginid) values('"+maxId+"','"+workcode+"','"+LastName+"','"+PositionID+"','"+DepartmentID+"','"+ManagerID
                                    +"','"+Birthday+"','"+Nationality+"','"+Sex+"','"+Status+"','"+UseKind+"','"+MaritalStatus+"','"+RegresidentPlace
                                    +"','"+CompanyStartDate+"','"+CompanyWorkYear+"','"+StartDate+"','"+EndDate+"','2','"+JobLevel+"','"+CertificateNum+
                                    "','"+Mobile+"','"+Telephone+"','"+HomeAddress+"','"+EducationLevel+"','"+ProbationEndDate+"','"+CostCenterID+"','"+companyName+"','"+companyid+"','"+workcode+"')";
                        }
                        bean.writeLog("hrmresoursql==========="+hrmresoursql);
                        rs.execute(hrmresoursql);
                    }
                }

                String cusselectsql = "select id,costid,companyname,companyid,bank,payee,accountno from hrmresource";
                RecordSet hrrs = new RecordSet();
                hrrs.execute(cusselectsql);
                RecordSet cusrs = new RecordSet();
                while (hrrs.next()){
                    String id          = Util.null2String(hrrs.getString("id"));
                    String costid      = Util.null2String(hrrs.getString("costid"));
                    String companyname = Util.null2String(hrrs.getString("companyname"));
                    String companyid   = Util.null2String(hrrs.getString("companyid"));
                    String bank        = Util.null2String(hrrs.getString("bank"));
                    String payee       = Util.null2String(hrrs.getString("payee"));
                    String accountno   = Util.null2String(hrrs.getString("accountno"));

                    boolean existCostCenterID = isExistCostCenterID(id);
                    String cussql;
                    if(existCostCenterID){
                        //存在则更新
                        cussql = "update cus_fielddata set field1='"+companyname+"',field0='"+costid+"',field3='"+companyid+"',field4='"+bank+"',field5='"+payee+"',field6='"+accountno+"',scopeid=-1,scope='HrmCustomFieldByInfoType' where id='"+id+"'";
                    }else{
                        //不存在则插入
                        cussql = "insert into cus_fielddata(id,field0,field1,field3,field4,field5,field6,scopeid,scope) values('"+id+"','"+costid+"','"+companyname+"','"+companyid+"','"+bank+"','"+payee+"','"+accountno+"',-1,'HrmCustomFieldByInfoType')";
                    }
                    cusrs.execute(cussql);
                }

                bean.writeLog("导入人员结束，共导入===="+i+"人！");
            }

            //数据插入完成之后，开始按照我们系统部门结构树，组装数据结构
            //分部层级hrmsubcompany
            RecordSet rs  = new RecordSet();
            RecordSet rs2 = new RecordSet();
            RecordSet rs3 = new RecordSet();
            String    sql = "select pid from hrmsubcompany";
            rs.execute(sql);
            while(rs.next()){
                String pid = rs.getString("pid");
                if(StringUtils.isNotBlank(pid)){
                    String selsql = "select id from hrmsubcompany where subcompanycode='"+pid+"'";
                    rs2.execute(selsql);
                    while (rs2.next()){
                        String id       = rs2.getString("id");
                        String updatesql= "update hrmsubcompany set companyid='"+id+"',supsubcomid='"+id+"'";
                        rs3.execute(updatesql);
                    }
                }
            }
            sql = "update hrmsubcompany set companyid=1,supsubcomid=0 where pid=50000000";
            rs3.execute(sql);
            //sql = "update hrmsubcompany set companyid=20,supsubcomid=20 where pid=50004014";
            //rs3.execute(sql);

            //部门层级
            String ssql = "select pid from hrmdepartment";
            rs.execute(ssql);
            //先把hrmdepartment表中有分部id的数据都同步过来
            while (rs.next()){
                String pid = Util.null2String(rs.getString("pid"));
                if(StringUtils.isNotBlank(pid)){
                    String compsql = "select id from hrmsubcompany where subcompanycode='"+pid+"'";
                    rs2.execute(compsql);
                    String id = "";
                    if (rs2.next()){
                        id = Util.null2String(rs2.getString("id"));
                    }
                    if(StringUtils.isNotBlank(id)){
                        String updatesql = "update hrmdepartment set subcompanyid1='"+id+"' where pid='"+pid+"'";
                        rs3.execute(updatesql);
                    }
                }
            }

            //hrmdepartment层级结构
            String depsql = "select pid from hrmdepartment";
            rs.execute(depsql);
            while(rs.next()){
                String pid = Util.null2String(rs.getString("pid"));
                if(StringUtils.isNotBlank(pid)){
                    String selsql = "select id,subcompanyid1 from hrmdepartment where departmentcode='"+pid+"'";
                    rs2.execute(selsql);
                    if(rs2.next()){
                        String supdepid      = Util.null2String(rs2.getString("id"));
                        String subcompanyid1 = Util.null2String(rs2.getString("subcompanyid1"));
                        String updatedepsql;
                        if(StringUtils.isNotBlank(subcompanyid1)){
                            updatedepsql = "update hrmdepartment set subcompanyid1='"+subcompanyid1+"',supdepid='"+supdepid+"' where pid='"+pid+"'";
                        }else{
                            updatedepsql = "update hrmdepartment set supdepid='"+supdepid+"' where pid='"+pid+"'";
                        }
                        rs3.execute(updatedepsql);
                    }
                }
            }

            //hrmresource表中数据插入完成后，与对应的部门，分部，岗位，做关联关系
            //accounttype=1的为次账号，不参与sap的同步
            String hrmresourcesql = "select workcode from hrmresource where accounttype is null or accounttype=0";
            rs.execute(hrmresourcesql);
            while(rs.next()){
                String workcode = Util.null2String(rs.getString("workcode"));
                oldcodeList.add(workcode);
                if(StringUtils.isNotBlank(workcode)){
                    String selectsql = "SELECT dep.id  departmentid,dep.subcompanyid1 subcompanyid1,job.id jobtitle FROM HrmResource re " +
                            "LEFT JOIN HrmDepartment dep " +
                            "ON re.sapdepartmentid=dep.departmentcode " +
                            "LEFT JOIN HrmJobTitles job " +
                            "ON re.sapjobtitle=job.jobtitlecode " +
                            "WHERE re.workcode='"+workcode+"'";
                    rs2.execute(selectsql);
                    if(rs2.next()){
                        String departmentid  = Util.null2String(rs2.getString("departmentid"));
                        String subcompanyid1 = Util.null2String(rs2.getString("subcompanyid1"));
                        String jobtitle      = Util.null2String(rs2.getString("jobtitle"));
                        String updatesql     = "update hrmresource set departmentid='"+departmentid+"',subcompanyid1='"+subcompanyid1+"',jobtitle='"+jobtitle+"' " + " where workcode='"+workcode+"'";
                        rs3.execute(updatesql);
                    }
                }
            }

            oldcodeList.removeAll(newcodeList);
            bean.writeLog("oldcodeList====="+oldcodeList);
            for (int i = 0; i <oldcodeList.size() ; i++) {
                if(!"".equals(oldcodeList.get(i))){
                    String upcanc = "update hrmresource set status='5' where workcode ='"+oldcodeList.get(i)+"'";
                    bean.writeLog("upcancsql====="+upcanc);
                    rs.execute(upcanc);
                }
            }

            //更新直接上级
            String    managersql = "select sapmanagerid from hrmresource";
            RecordSet rsi        = new RecordSet();
            RecordSet rsu        = new RecordSet();
            rsi.execute(managersql);
            while(rsi.next()){
                String sapmanagerid = Util.null2String(rsi.getString("sapmanagerid"));
                if(StringUtils.isNotBlank(sapmanagerid)){
                    String manageridBysapid    =  getManageridBySapid(sapmanagerid);
                    String updatemanangeridsql = "update hrmresource set managerid='"+manageridBysapid+"' where sapmanagerid='"+sapmanagerid+"'";
                    rsu.execute(updatemanangeridsql);
                }
            }

            try {
                new ResourceComInfo().removeResourceCache();
            } catch (Exception e) {
                bean.writeLog("调用清缓存方法失败："+e);
            }
        } catch (Exception e) {
            bean.writeLog(" GetPosition error :"+e);
        }finally {
            try {
                if(is != null){
                    is.close();
                }
                if(os != null){
                    os.close();
                }
                if(isr != null) {
                    isr.close();
                }
                if(br != null){
                    br.close();
                }

            } catch(Exception e) {
                bean.writeLog("getabsenceinfo request error --"+e);
            }
        }

    }

    private static String getDepXML(String id){
        String soapXML = "<?xml version=\"1.0\" encoding=\"utf-8\"?>"
                +"<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">\n" +
                "   <soapenv:Header/>\n" +
                "   <soapenv:Body>\n" +
                "      <tem:GetDepartment>\n"
                +"<tem:ID>"
                +id
                +"</tem:ID></tem:GetDepartment>\n" +
                "   </soapenv:Body>\n" +
                "</soapenv:Envelope>";
        return soapXML;
    }

    private static String getPosXML(){
        String soapXML = "<?xml version=\"1.0\" encoding=\"utf-8\"?>"
                +"<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">\n" +
                "   <soapenv:Header/>\n" +
                "   <soapenv:Body>\n" +
                "      <tem:GetPosition>\n" +
                "         <!--Optional:-->\n" +
                "         <tem:ID></tem:ID>\n" +
                "      </tem:GetPosition>\n" +
                "   </soapenv:Body>\n" +
                "</soapenv:Envelope>";
        return soapXML;
    }

    private static boolean isExist(String departmentcode,String type){
        RecordSet rs   = new RecordSet();
        boolean   flag = false;
        if(StringUtils.isNotBlank(departmentcode) && StringUtils.isNotBlank(type)){
            String sql="";
            //为C存subcompany表 为O存department表
            if("C".equalsIgnoreCase(type)){
                sql = "select subcompanyname from hrmsubcompany where subcompanycode='"+departmentcode+"'";
            }else if("o".equalsIgnoreCase(type)){
                sql = "select departmentname from hrmdepartment where departmentcode='"+departmentcode+"'";
            }
            rs.execute(sql);
            if(rs.next()){
                flag = true;
            }
        }
        return flag;
    }

    private static boolean isPosExist(String jobtitlecode){
        RecordSet rs   = new RecordSet();
        boolean   flag = false;
        if(StringUtils.isNotBlank(jobtitlecode)){
            //为C存company表 为O存department表
            String sql = "select jobtitlename from hrmjobtitles where jobtitlecode='" + jobtitlecode + "'";
            rs.execute(sql);
            if(rs.next()){
                flag=true;
            }
        }
        return flag;
    }

    private static String getEmpXML(String id) {
        String soapXML = "<?xml version=\"1.0\" encoding=\"utf-8\"?>"
                + "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">\n" +
                "   <soapenv:Header/>\n" +
                "   <soapenv:Body>\n" +
                "      <tem:GetEmployee>\n" +
                "         <!--Optional:-->\n" +
                "         <tem:ID></tem:ID>\n" +
                "      </tem:GetEmployee>\n" +
                "   </soapenv:Body>\n" +
                "</soapenv:Envelope>";
        return soapXML;
    }

    private static String getCompanyNameByCode(String companycode){
        String companyname = "";
        /*
         * 1000 上海盖奇异电气元件有限公司
         * 1010 上海安奕极电子科技有限公司
         * 1020 盖奇异开关（上海）有限公司
         * 1030 安奕极电气工业系统（上海）有限公司
         * 2000 上海安奕极智能控制系统有限公司
         * 2010 上海诺尔恰商贸有限公司
         * 3000 上海安奕极企业发展有限公司
         * 4000 上海广电电气(集团)股份有限公司
         * 5000 上海通用广电工程有限公司
         * 6000 上海澳通韦尔电力电子有限公司
         * 7000 上海广电电气集团投资管理有限公司
         * 8000 广州广电通用电气有限公司
         * 9000 山东广电电气有限公司
         * */
        if(StringUtils.isNotBlank(companycode)){
            switch (companycode){
                case "1000":
                    companyname = "上海盖奇异电气元件有限公司";
                    break;
                case "1010":
                    companyname = "上海安奕极电子科技有限公司";
                    break;
                case "1020":
                    companyname = "盖奇异开关（上海）有限公司";
                    break;
                case "1030":
                    companyname = "安奕极电气工业系统（上海）有限公司";
                    break;
                case "2000":
                    companyname = "上海安奕极智能控制系统有限公司";
                    break;
                case "2010":
                    companyname = "上海诺尔恰商贸有限公司";
                    break;
                case "3000":
                    companyname = "上海安奕极企业发展有限公司";
                    break;
                case "4000":
                    companyname = "上海广电电气(集团)股份有限公司";
                    break;
                case "5000":
                    companyname = "上海通用广电工程有限公司";
                    break;
                case "6000":
                    companyname = "上海澳通韦尔电力电子有限公司";
                    break;
                case "7000":
                    companyname = "上海广电电气集团投资管理有限公司";
                    break;
                case "8000":
                    companyname = "广州广电通用电气有限公司";
                    break;
                case "9000":
                    companyname = "山东广电电气有限公司";
                    break;
            }

        }

        return companyname;
    }

    private static String changeCompanyid(String companyid){
        String typeid = "";
        if(StringUtils.isNotBlank(companyid)){
            switch (companyid){
                case "1000":
                    typeid = "0";
                    break;
                case "1010":
                    typeid = "1";
                    break;
                case "1020":
                    typeid = "2";
                    break;
                case "1030":
                    typeid = "3";
                    break;
                case "2000":
                    typeid = "4";
                    break;
                case "2010":
                    typeid = "5";
                    break;
                case "3000":
                    typeid = "6";
                    break;
                case "4000":
                    typeid = "7";
                    break;
                case "5000":
                    typeid = "8";
                    break;
                case "6000":
                    typeid = "9";
                    break;
                case "7000":
                    typeid = "10";
                    break;
                case "8000":
                    typeid = "11";
                    break;
                case "9000":
                    typeid = "12";
                    break;
            }

        }
        return typeid;
    }

    private static boolean isExistCostCenterID(String id){
        RecordSet rs  = new RecordSet();
        String    sql = "select * from cus_fielddata where id='"+id+"'";
        rs.execute(sql);
        boolean flag = false;
        if(rs.next()){
            flag = true;
        }
        return flag;
    }

    private static String getIdByUseKind(String userkind){
        RecordSet rs  = new RecordSet();
        String    sql = "select id from hrmusekind where name='"+userkind+"'";
        rs.execute(sql);
        String id = "";
        if(rs.next()){
            id = Util.null2String(rs.getString("id"));
        }
        return id;
    }

    private static String getCodeByCountryName(String countryName){
        RecordSet rs  = new RecordSet();
        String    sql = "SELECT id FROM hrmcountry where countryname='"+countryName+"'";
        rs.execute(sql);
        String id = "";
        if(rs.next()){
            id = Util.null2String(rs.getString("id"));
        }
        return id;
    }

    private static String getEducLevIdByName(String eduname){
        RecordSet rs  = new RecordSet();
        String    sql = "SELECT id FROM HrmEducationLevel where name='"+eduname+"'";
        rs.execute(sql);
        String id = "";
        if(rs.next()){
            id = Util.null2String(rs.getString("id"));
        }
        return id;
    }

    private static String getManageridBySapid(String sapid){
        RecordSet rs = new RecordSet();
        String sql="SELECT id FROM HrmResource WHERE workcode='"+sapid+"'";
        rs.execute(sql);
        String id = "";
        if(rs.next()){
            id = Util.null2String(rs.getString("id"));
        }
        return id;
    }

    private static boolean isEmpExist(String workcode) {
        //根据workcode
        RecordSet rs = new RecordSet();
        boolean flag = false;
        if(StringUtils.isNotBlank(workcode)) {
            String sql = "select lastname from hrmresource where workcode='" + workcode + "'";
            rs.execute(sql);
            if (rs.next()) {
                flag = true;
            }
        }
        return flag;
    }

    //查询是否是主账号
    private static boolean isAccount(String workcode) {
        //根据workcode 和accounttype !=1 (accounttype=1代表子账号)
        RecordSet rs   = new RecordSet();
        boolean   flag = false;
        if(StringUtils.isNotBlank(workcode)) {
            String sql = "select lastname from hrmresource where workcode='" + workcode + "' and  accounttype ='1'";
            rs.execute(sql);
            if (rs.next()) {
                flag = true;
            }
        }
        return flag;
    }

}