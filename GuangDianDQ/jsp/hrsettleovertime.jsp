<%@ page import="weaver.general.*,weaver.conn.*,java.text.*,java.util.*" %>
<%@ page import="net.sf.json.JSONObject" %>
<%@ page import ="org.dom4j.Element" %>
<%@ page import="java.net.URL" %>
<%@ page import="java.net.HttpURLConnection" %>
<%@ page import="java.io.OutputStream" %>
<%@ page import="java.io.InputStream" %>
<%@ page import="java.io.InputStreamReader" %>
<%@ page import="java.io.BufferedReader" %>
<%@ page import="org.dom4j.Document" %>
<%@ page import="org.dom4j.DocumentHelper" %>
<%@ page import="org.apache.commons.lang3.StringUtils" %>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />
<%
    response.setContentType("text/xml;charset=UTF-8");
    String  yggh=request.getParameter("yggh");
    String  lx=request.getParameter("lx");
    String  startdate=request.getParameter("startdate");
    String  duration=request.getParameter("duration");
    String error="请求失败";
    InputStream is=null;
    OutputStream os=null;
    InputStreamReader isr=null;
    BufferedReader br=null;
    float dura = Float.parseFloat(duration);
    BaseBean bean = new BaseBean();
    try {
        //第一步：创建服务地址
        String interfaceurl = bean.getPropValue("gddqinterfaceurl", "interfaceurl");
        URL url = new URL(interfaceurl);
        //第二步：打开一个通向服务地址的连接
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        //第三步：设置参数
        //3.1发送方式设置：POST必须大写
        connection.setRequestMethod("POST");
        //3.2设置数据格式：content-type
        connection.setRequestProperty("content-type", "text/xml;charset=utf-8");
        //3.3设置输入输出，因为默认新创建的connection没有读写权限，
        connection.setDoInput(true);
        connection.setDoOutput(true);

        //用户工号要去workcode
        String soapXML = getSettleXML(yggh,lx,startdate,dura);
        //将信息以流的方式发送出去
        os = connection.getOutputStream();
        os.write(soapXML.getBytes());
        //第五步：接收服务端响应，打印
        int responseCode = connection.getResponseCode();

        if (200 == responseCode) {//表示服务端响应成功
            //获取当前连接请求返回的数据流
            is = connection.getInputStream();
            isr = new InputStreamReader(is, "UTF-8");
            br = new BufferedReader(isr);
            StringBuilder sb = new StringBuilder();
            String temp = null;
            while (null != (temp = br.readLine())) {
                sb.append(temp);
            }
            Document documentHelper = DocumentHelper.parseText(sb.toString());
            Element rootElement = documentHelper.getRootElement();
            Element body = rootElement.element("Body").element("SettleOvertimeResponse").element("SettleOvertimeResult");
            Element Error = body.element("Error");
            if(null!=Error){
                error = Error.getText();
            }
        }
    } catch (Exception e) {
        bean.writeLog(" SettleOvertimeResponse request error :"+e);
    }finally {
        try {
            if( is != null) {
                is.close();
            }
            if(isr != null) {
                isr.close();
            }
            if(br != null){
                br.close();
            }
            if(os!=null){
                os.close();
            }
        } catch(Exception e) {
            bean.writeLog("SettleOvertimeResponse request error --"+e);
        }

    }
    Map result = new HashMap();
    result.put("error", error);
    JSONObject jo = JSONObject.fromObject(result);
    out.println(jo);
%>
<%!
    private static  String getSettleXML(String id,String lx,String startdate,float duration) {
        String soapXML = "<?xml version=\"1.0\" encoding=\"utf-8\"?>"
                + "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">\n" +
                "   <soapenv:Header/>\n" +
                "   <soapenv:Body>\n" +
                "      <tem:SettleOvertime>\n" +
                "         <!--Optional:-->\n" +
                "         <tem:pno>"+id+"</tem:pno>\n" +
                "         <!--Optional:-->\n" +
                "         <tem:type>"+lx+"</tem:type>\n" +
                "         <!--Optional:-->\n" +
                "         <tem:startDate>"+startdate+"</tem:startDate>\n" +
                "         <tem:duration>"+duration+"</tem:duration>\n" +
                "      </tem:SettleOvertime>\n" +
                "   </soapenv:Body>\n" +
                "</soapenv:Envelope>";
        return soapXML;
    }
%>