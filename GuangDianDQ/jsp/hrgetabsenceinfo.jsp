<%@ page import="weaver.general.*,weaver.conn.*,java.text.*,java.util.*" %>
<%@ page import="net.sf.json.JSONObject" %>
<%@ page import ="org.dom4j.Element" %>
<%@ page import="java.net.URL" %>
<%@ page import="java.net.HttpURLConnection" %>
<%@ page import="java.io.OutputStream" %>
<%@ page import="java.io.InputStream" %>
<%@ page import="java.io.InputStreamReader" %>
<%@ page import="java.io.BufferedReader" %>
<%@ page import="org.dom4j.Document" %>
<%@ page import="org.dom4j.DocumentHelper" %>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />
<%
    response.setContentType("text/xml;charset=UTF-8");
    String  yggh=request.getParameter("yggh");
    String  lx=request.getParameter("lx");
    String quotaText="";
    String usedDaysText="";
    InputStream is=null;
    InputStreamReader isr=null;
    BufferedReader br=null;
    OutputStream os=null;
    BaseBean bean = new BaseBean();
    try {
        //第一步：创建服务地址
        String interfaceurl = bean.getPropValue("gddqinterfaceurl", "interfaceurl");
        URL url = new URL(interfaceurl);
        //第二步：打开一个通向服务地址的连接
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        //第三步：设置参数
        //3.1发送方式设置：POST必须大写
        connection.setRequestMethod("POST");
        //3.2设置数据格式：content-type
        connection.setRequestProperty("content-type", "text/xml;charset=utf-8");
        //3.3设置输入输出，因为默认新创建的connection没有读写权限，
        connection.setDoInput(true);
        connection.setDoOutput(true);

        //第四步：组织SOAP数据，发送请求,id为空获取所有的部门信息
        lx=changQingJiaLeiXing(lx);
        //用户工号要去workcode
        //String workcode = getWorkCodeById(yggh);
        String soapXML = getXML(yggh,lx);
        BaseBean base = new BaseBean();
        base.writeLog("getabsenceinfo---request---"+soapXML);
        //将信息以流的方式发送出去
         os = connection.getOutputStream();
        os.write(soapXML.getBytes());
        //第五步：接收服务端响应，打印
        int responseCode = connection.getResponseCode();

        if (200 == responseCode) {//表示服务端响应成功
            //获取当前连接请求返回的数据流
            StringBuilder sb = new StringBuilder();
                is= connection.getInputStream();
                isr = new InputStreamReader(is, "UTF-8");
                br = new BufferedReader(isr);
                String temp = null;
                while (null != (temp = br.readLine())) {
                    sb.append(temp);
                }
            Document documentHelper = DocumentHelper.parseText(sb.toString());
            Element rootElement = documentHelper.getRootElement();
            Element body = rootElement.element("Body").element("GetAbsenceInfoResponse").element("GetAbsenceInfoResult");
            Element Quota = body.element("Quota");
            //定额天数
            if(null!=Quota){
                quotaText = Quota.getText();
                base.writeLog("getabsenceinfo---request-quotaText--"+quotaText);
            }
            Element UsedDays = body.element("UsedDays");
            //已请假天数
            if(null!=UsedDays){
                usedDaysText = UsedDays.getText();
                base.writeLog("getabsenceinfo---request-usedDaysText--"+usedDaysText);
            }
        }
    } catch (Exception e) {
        bean.writeLog("getabsenceinfo request error --:"+e);
    }finally {
        try {
            if(isr != null) {
                isr.close();
            }
            if(br != null){
                br.close();
            }
            if(os!=null){
                os.close();
            }
        } catch(Exception e) {
            bean.writeLog("getabsenceinfo request error --"+e);
        }
    }
    Map result = new HashMap();
    result.put("quotaText", quotaText);
    result.put("usedDaysText", usedDaysText);
    JSONObject jo = JSONObject.fromObject(result);
    out.println(jo);
%>
<%!
    private  String getXML(String id,String type) {
    String soapXML = "<?xml version=\"1.0\" encoding=\"utf-8\"?>"
    + "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">\n" +
    "   <soapenv:Header/>\n" +
    "   <soapenv:Body>\n" +
    "      <tem:GetAbsenceInfo>\n" +
    "         <!--Optional:-->\n" +
    "         <tem:pno>"+id+"</tem:pno>\n" +
    "         <!--Optional:-->\n" +
    "         <tem:absenceType>"+type+"</tem:absenceType>\n" +
    "      </tem:GetAbsenceInfo>\n" +
    "   </soapenv:Body>\n" +
    "</soapenv:Envelope>";
    return soapXML;
    }
private String changQingJiaLeiXing(String field){
        /**
         * 0事假　
         * 1年假　
         * 2病假　
         * 3调休　
         * 4婚假　
         * 5丧假　
         * 6产检　
         * 7产假　
         * 8陪产假　
         * 9出勤（未刷卡）　
         * 10外出培训　
         * 11预支调休
         * */

        /**
         * 0001:年假
         * 0002:事假
         * 0003:病假
         * 0004:丧假
         * 0005:婚假
         * 0006:产假
         * 0007:陪产假
         * 0008:调休（无定额,AEG）
         * 0012:调休（有定额）
         * 0013:产检
         * 0014:预支调休
         * 0040:出差/公事外出
         * 0045:外出培训
         * 0050:出勤（未刷卡）
         * 0060:团队拓展
         * */
        String lx="";
        if(""!=field){
            switch (field){
                case "0":
                 lx="0002";
                 break;
                case "1":
                    lx="0001";
                    break;
                case "2":
                    lx="0003";
                    break;
                case "3":
                    lx="0012";
                    break;
                case "4":
                    lx="0005";
                    break;
                case "5":
                    lx="0004";
                    break;
                case "6":
                    lx="0006";
                    break;
                case "7":
                    lx="0013";
                    break;
                case "8":
                    lx="0007";
                    break;
                case "9":
                    lx="0050";
                    break;
                case "10":
                    lx="0045";
                    break;
                case "11":
                    lx="0014";
                    break;
            }

        }
        return lx;
}
    private static String getWorkCodeById(String id ){
        RecordSet rs = new RecordSet();
        String workcode="";
        String sql="select workcode from hrmresource where id ="+id;
        rs.execute(sql);
        if (rs.next()){
            workcode = Util.null2String(rs.getString("workcode"));
        }
        return workcode;
    }
%>