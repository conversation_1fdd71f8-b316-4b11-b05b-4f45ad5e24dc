<%@ page import="weaver.general.*,weaver.conn.*,java.text.*,java.util.*" %>
<%@ page import="net.sf.json.JSONObject" %>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />
<%
    response.setContentType("text/xml;charset=UTF-8");
    String nodeid=request.getParameter("nodeid");
    String clr=request.getParameter("clr");
    String remark=request.getParameter("remark");
    String msgid=request.getParameter("msgid");
    String workcode=request.getParameter("workcode");

    //获取当前节点中文名称
    String sql="SELECT nodename FROM workflow_nodebase WHERE id='"+nodeid+"'";
    rs.execute(sql);
    String nodename="";
    if(rs.next()){
        nodename=Util.null2String(rs.getString("nodename"));
    }
    Date date = new Date();
    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    String datestr =format.format(date);
    // 审批意见展现样式：审批节点1-审批人1-审批时间1（YYYY-MM-DD HH:MM:SS）-审批意见1 审批节点2-审批人2-审批时间2（YYYY-MM-DD HH:MM:SS）-审批意见2
    remark=nodename+"-"+clr+"-"+datestr+"-"+remark;
    //先把当前msgid的签字意见查出来
    String comments="";
    String remarksql="select aprv_comments from t_bpm_msg_master where msg_id='"+msgid+"'";
    rs.execute(remarksql);
    if(rs.next()){
        comments=Util.null2String(rs.getString("aprv_comments"));
    }
    remark=comments+remark;

    String updatesql = "update  t_bpm_msg_master set aprv_node='"+nodename+"',aprv_user_emp_no='"+workcode+
            "',aprv_user_name='"+clr+"',aprv_at='"+datestr+"',aprv_comments='"+remark+"'+char(10)"+",sync_status='9' where msg_id='"+msgid+"'";

    boolean res = rs.execute(updatesql);

    Map result = new HashMap();
    result.put("res", res);
    JSONObject jo = JSONObject.fromObject(result);
    out.println(jo);
%>