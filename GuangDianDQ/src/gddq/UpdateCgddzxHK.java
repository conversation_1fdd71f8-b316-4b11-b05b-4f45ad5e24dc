package gddq;

import com.weaver.general.BaseBean;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.workflow.WorkflowComInfo;

public class UpdateCgddzxHK extends BaseBean
  implements Action
{
  public String execute(RequestInfo request)
  {
    String retStr = "1";
    RequestManager rm = request.getRequestManager();
    try {
      WorkflowComInfo workflowComInfo = new WorkflowComInfo();
      int requestid = Util.getIntValue(request.getRequestid());
      int cjr = Util.getIntValue(request.getCreatorid());
      String requestname = Util.null2String(request.getRequestManager().getRequestname());
      int workflowid = Util.getIntValue(request.getWorkflowid());
      int formid = Util.getIntValue(workflowComInfo.getFormId("" + workflowid));
      String maintablename = "formtable_main_" + Math.abs(formid);
      int wfstatus = request.getRequestManager().getCreater();
      writeLog("requestid = " + requestid);
      writeLog("workflowid = " + workflowid);
      writeLog("formid = " + formid);
      RecordSet rs = new RecordSet();
      //String sql = "update " + maintablename + " set status='2' where requestid='" + requestid + "'";
      String sql = "exec xm_updatecgddzxstate @requestid="+requestid;
      rs.execute(sql);
    } catch (Exception e) {
      writeLog("update  faield:" + e);
      rm.setMessagecontent("更新采购订单付款状态时出错！");
      retStr = "0";
    }
    return retStr;
  }
}