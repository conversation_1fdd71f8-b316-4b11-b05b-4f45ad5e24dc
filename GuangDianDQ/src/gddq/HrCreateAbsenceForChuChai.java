package gddq;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.MainTableInfo;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.workflow.WorkflowComInfo;

public class HrCreateAbsenceForChuChai extends BaseBean
  implements Action
{
  public String execute(RequestInfo request)
  {
    String retStr = "1";
    RequestManager rm = request.getRequestManager();
    WorkflowComInfo workflowComInfo = new WorkflowComInfo();
    int requestid = Util.getIntValue(request.getRequestid());
    int cjr = Util.getIntValue(request.getCreatorid());
    String requestname = Util.null2String(request.getRequestManager().getRequestname());
    int workflowid = Util.getIntValue(request.getWorkflowid());
    int formid = Util.getIntValue(workflowComInfo.getFormId(new StringBuilder().append("").append(workflowid).toString()));
    String maintablename = new StringBuilder().append("formtable_main_").append(Math.abs(formid)).toString();
    int wfstatus = request.getRequestManager().getCreater();
    writeLog(new StringBuilder().append("requestid = ").append(requestid).toString());
    writeLog(new StringBuilder().append("workflowid = ").append(workflowid).toString());
    writeLog(new StringBuilder().append("formid = ").append(formid).toString());

    InputStream is = null;
    OutputStream os = null;
    InputStreamReader isr = null;
    BufferedReader br = null;
    Property[] properties = request.getMainTableInfo().getProperty();
    String error = "";
    String lx = "";
    String yggh = "";
    String startdate = "";
    String enddate = "";
    String datetype = "";
    for (int i = 0; i < properties.length; i++) {
      String name = properties[i].getName();
      if ("gh".equals(name)) {
        yggh = Util.null2String(properties[i].getValue());
      }
      if ("ksrq".equals(name)) {
        startdate = Util.null2String(properties[i].getValue());
        startdate = startdate.replaceAll("-", "");
      }
      if ("jsrq".equals(name)) {
        enddate = Util.null2String(properties[i].getValue());
        enddate = enddate.replaceAll("-", "");
      }
      if ("sjlx".equals(name)) {
        datetype = Util.null2String(properties[i].getValue());
      }

    }

    BaseBean bean = new BaseBean();
    try
    {
      String interfaceurl = bean.getPropValue("gddqinterfaceurl", "interfaceurl");
      URL url = new URL(interfaceurl);

      HttpURLConnection connection = (HttpURLConnection)url.openConnection();

      connection.setRequestMethod("POST");

      connection.setRequestProperty("content-type", "text/xml;charset=utf-8");

      connection.setDoInput(true);
      connection.setDoOutput(true);

      String soapXML = getXML(yggh, "", startdate, enddate, datetype);
      bean.writeLog(new StringBuilder().append("CreateAbsenceResponse soapXML---").append(soapXML).toString());

      os = connection.getOutputStream();
      os.write(soapXML.getBytes());

      int responseCode = connection.getResponseCode();

      if (200 == responseCode)
      {
        is = connection.getInputStream();
        isr = new InputStreamReader(is, "UTF-8");
        br = new BufferedReader(isr);
        StringBuilder sb = new StringBuilder();
        String temp = null;
        while (null != (temp = br.readLine())) {
          sb.append(temp);
        }
        Document documentHelper = DocumentHelper.parseText(sb.toString());
        Element rootElement = documentHelper.getRootElement();
        Element body = rootElement.element("Body").element("CreateAbsenceResponse").element("CreateAbsenceResult");
        Element Error = body.element("Error");
        if (null != Error)
          error = Error.getText();
      }
    }
    catch (Exception e)
    {
      bean.writeLog(new StringBuilder().append(" CreateAbsenceResponse request error :").append(e).toString());
    } finally {
      try {
        if (isr != null) {
          isr.close();
        }
        if (br != null) {
          br.close();
        }
        if (os != null)
          os.close();
      }
      catch (Exception e) {
        bean.writeLog(new StringBuilder().append("CreateAbsence request error --").append(e).toString());
      }
    }

    if ("" != error) {
      retStr = "0";
      rm.setMessagecontent(error);
    }
    return retStr;
  }

  private static String getXML(String id, String type, String stardate, String enddate, String datetype) {
    String soapXML = new StringBuilder().append("<?xml version=\"1.0\" encoding=\"utf-8\"?><soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">\n   <soapenv:Header/>\n   <soapenv:Body>\n      <tem:CreateAbsence>\n         <!--Optional:-->\n         <tem:pno>").append(id).append("</tem:pno>\n").append("         <!--Optional:-->\n").append("         <tem:absenceType>0040</tem:absenceType>\n").append("         <!--Optional:-->\n").append("         <tem:startDate>").append(stardate).append("</tem:startDate>\n").append("         <!--Optional:-->\n").append("         <tem:endDate>").append(enddate).append("</tem:endDate>\n").append("         <tem:dateType>").append(datetype).append("</tem:dateType>\n").append("      </tem:CreateAbsence>\n").append("   </soapenv:Body>\n").append("</soapenv:Envelope>").toString();

    return soapXML;
  }

  private static String changQingJiaLeiXing(String field)
  {
    String lx = "";
    if ("" != field) {
      switch (field) {
      case "0":
        lx = "0002";
        break;
      case "1":
        lx = "0001";
        break;
      case "2":
        lx = "0003";
        break;
      case "3":
        lx = "0012";
        break;
      case "4":
        lx = "0005";
        break;
      case "5":
        lx = "0004";
        break;
      case "6":
        lx = "0013";
        break;
      case "7":
        lx = "0006";
        break;
      case "8":
        lx = "0007";
        break;
      case "9":
        lx = "0050";
        break;
      case "10":
        lx = "0045";
        break;
      case "11":
        lx = "0014";
        break;
      case "12":
        lx = "0008";
      }

    }

    return lx;
  }
  private static String getWorkCodeById(String id) {
    RecordSet rs = new RecordSet();
    String workcode = "";
    String sql = new StringBuilder().append("select workcode from hrmresource where id =").append(id).toString();
    rs.execute(sql);
    if (rs.next()) {
      workcode = Util.null2String(rs.getString("workcode"));
    }
    return workcode;
  }
}