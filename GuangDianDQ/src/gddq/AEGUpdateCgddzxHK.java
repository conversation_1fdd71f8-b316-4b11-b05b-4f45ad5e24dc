package gddq;

import com.weaver.general.BaseBean;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.workflow.WorkflowComInfo;

public class AEGUpdateCgddzxHK extends BaseBean
  implements Action
{
  public String execute(RequestInfo paramRequestInfo)
  {
    String str1 = "1";
    RequestManager localRequestManager = paramRequestInfo.getRequestManager();
    try {
      WorkflowComInfo localWorkflowComInfo = new WorkflowComInfo();
      int i = Util.getIntValue(paramRequestInfo.getRequestid());
      int j = Util.getIntValue(paramRequestInfo.getCreatorid());
      String str2 = Util.null2String(paramRequestInfo.getRequestManager().getRequestname());
      int k = Util.getIntValue(paramRequestInfo.getWorkflowid());
      int m = Util.getIntValue(localWorkflowComInfo.getFormId("" + k));
      String str3 = "formtable_main_" + Math.abs(m);
      int n = paramRequestInfo.getRequestManager().getCreater();
      writeLog("requestid = " + i);
      writeLog("workflowid = " + k);
      writeLog("formid = " + m);
      RecordSet localRecordSet = new RecordSet();

      String str4 = "exec xm_updatecgddzxstate_aeg @requestid=" + i;
      localRecordSet.execute(str4);
    } catch (Exception localException) {
      writeLog("update  faield:" + localException);
      localRequestManager.setMessagecontent("更新采购订单付款状态时出错！");
      str1 = "0";
    }
    return str1;
  }
}