package gddq;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Cell;
import weaver.soa.workflow.request.DetailTable;
import weaver.soa.workflow.request.DetailTableInfo;
import weaver.soa.workflow.request.MainTableInfo;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.soa.workflow.request.Row;
import weaver.workflow.request.RequestManager;
import weaver.workflow.workflow.WorkflowComInfo;

public class AEGYWZDF extends BaseBean
  implements Action
{
  public String execute(RequestInfo request)
  {
    String retStr = "1";
    RequestManager rm = request.getRequestManager();
    WorkflowComInfo workflowComInfo = new WorkflowComInfo();
    int requestid = Util.getIntValue(request.getRequestid());
    int cjr = Util.getIntValue(request.getCreatorid());
    String requestname = Util.null2String(request.getRequestManager().getRequestname());
    int workflowid = Util.getIntValue(request.getWorkflowid());
    int formid = Util.getIntValue(workflowComInfo.getFormId(new StringBuilder().append("").append(workflowid).toString()));
    String maintablename = new StringBuilder().append("formtable_main_").append(Math.abs(formid)).toString();
    int wfstatus = request.getRequestManager().getCreater();
    writeLog(new StringBuilder().append("requestid = ").append(requestid).toString());
    writeLog(new StringBuilder().append("workflowid = ").append(workflowid).toString());
    writeLog(new StringBuilder().append("formid = ").append(formid).toString());

    InputStream is = null;
    OutputStream os = null;
    InputStreamReader isr = null;
    BufferedReader br = null;
    Property[] properties = request.getMainTableInfo().getProperty();

	String skrgh = "";
    String skr = "";
    String lsh = "";
    String ztbm = "";
    String sapgsdm = "";
    String remark = "";
    String sf = "";
    String skrsfbgsry = "";
    String sfxt = "";
    boolean flag = true;
	
	
	String   sqlsap = "update "+maintablename+" set sapsfdy=0,sapdocno='' where requestid="+requestid;
    RecordSet rssap = new RecordSet();
    rssap.execute(sqlsap);

	
    for (int i = 0; i < properties.length; i++) {
      String name = properties[i].getName();

      if ("skrgh".equals(name)) {
        skrgh = Util.null2String(properties[i].getValue());
      }
      if ("skr".equals(name)) {
        skr = Util.null2String(properties[i].getValue());
      }
      if ("lsh".equals(name)) {
        lsh = Util.null2String(properties[i].getValue());
        //zzmm = getName(zzmm);
      }
      if ("ztbm".equals(name)) {
        ztbm = Util.null2String(properties[i].getValue());
      }
      if ("sapgsdm".equals(name)) {
        sapgsdm = Util.null2String(properties[i].getValue());
      }
      if ("sfxt".equals(name)) {
        sfxt = Util.null2String(properties[i].getValue());
      }
      if ("skrsfbgsry".equals(name)) {
        skrsfbgsry = Util.null2String(properties[i].getValue());
      }
      if ("sf".equals(name)) {
        sf = Util.null2String(properties[i].getValue());
      }

    }

     //lsh="A20210001";
	 //---remark="remark";
	 //----skrgh----skr----ztbm----sf,skrsfbgsry---------
    if ("".equals(getztbm(ztbm))) {
        flag = false;
	}
    if ("".equals(skrgh)) {
        flag = false;
	}
    if ("".equals(skr)) {
        flag = false;
	}
    if ("1".equals(sf)) {
        flag = false;
	}
    if ("1".equals(skrsfbgsry)) {
        flag = false;
	}
     RecordSet rs0 = new RecordSet();
      String sql0= "select max(id) as id from XM_AEGYWZDF_LOG";
      rs0.execute(sql0);
       int  maxid = 0;
       if (rs0.next()) {      
             maxid = rs0.getInt("id")+1;
       }

    //String   sql1 = "insert into XM_AEGYWZDF_LOG(id,skrgh,skr,lsh,remark,ztbm,companycode,rq) values("+maxid+",'"+skrgh+"','"+skr+"','"+lsh+"','"+remark+"','"+ztbm+"','"+sapgsdm+"',GETDATE())";
    //String   sql1 = "insert into XM_AEGYWZDF_LOG(id,skrgh,skr,lsh,remark,ztbm,companycode,sfxt,rq) values("+maxid+",'"+skrgh+"','"+skr+"','"+lsh+"','"+remark+"','"+ztbm+"','"+sapgsdm+"',"+sfxt+",GETDATE())";
    String   sql1 = "insert into XM_AEGYWZDF_LOG(id,skrgh,skr,lsh,remark,skrsfbgsry,sf,ztbm,companycode,sfxt,rq) values("+maxid+",'"+skrgh+"','"+skr+"','"+lsh+"','"+remark+"','"+skrsfbgsry+"','"+sf+"','"+getztbm(ztbm)+"','"+sapgsdm+"',"+sfxt+",GETDATE())";
    RecordSet rs1 = new RecordSet();
    rs1.execute(sql1);

    String   sql2 = "";
    RecordSet rs2 = new RecordSet();


    DetailTable[] detailtable = request.getDetailTableInfo().getDetailTable();
    ArrayList detaillist1 = new ArrayList();
    ArrayList detaillist2 = new ArrayList();

    if (detailtable.length > 0) {
      String sapdjkm = "";
      String je = "";
      String se = "";
	  
      for (int i = 0; i < detailtable.length; i++) {
        DetailTable dt = detailtable[i];
        if (0 == i)
        {
          Row[] s = dt.getRow();
          for (int j = 0; j < s.length; j++) {
            Row r = s[j];
            Cell[] c = r.getCell();
            HashMap rowmap = new HashMap();
            for (int k = 0; k < c.length; k++) {
              Cell c1 = c[k];
              String name = c1.getName();
              if ("sapdjkm".equals(name)) {
                sapdjkm = c1.getValue();
				sapdjkm = getaccount(sapdjkm);
				//sapdjkm = "ACC02";
                rowmap.put("sapdjkm", sapdjkm);
              }
              if ("je".equals(name)) {
                je = c1.getValue();
                rowmap.put("je", je);
              }
              if ("se".equals(name)) {
                se = c1.getValue();
                rowmap.put("se", se);
              }
            }

            if ("".equals(sapdjkm)) {
               flag = false;
	        }
  		    sql2 = "insert into XM_AEGYWZDF_LOG_DT1(id,sapdjkm,je,se) values("+maxid+",'"+sapdjkm+"',"+je+","+se+")";
            rs2.execute(sql2);
            detaillist1.add(rowmap);
          }
        } 
      }
    }

 //if (ztbm.equals(sapgsdm) && flag) {
 if (flag) {
    String detailstr = "";
    String mainstr = "";
    String account = "";
    String amount = "";
    String tax = "";
	
	sqlsap = "update "+maintablename+" set sapsfdy=2 where requestid="+requestid;
    rssap.execute(sqlsap);

    sapgsdm = getcompanycode(sapgsdm);

    for (int i = 0; i < detaillist1.size(); i++) {
  	  detailstr += "<tem:FIExpenseItem><tem:Account>";
      account = (String)((HashMap)detaillist1.get(i)).get("sapdjkm");
      amount = (String)((HashMap)detaillist1.get(i)).get("je");
      tax = (String)((HashMap)detaillist1.get(i)).get("se");
	  detailstr += account+"</tem:Account>\n"+"<tem:Amount>"+amount+"</tem:Amount>\n"+"<tem:Tax>"+tax+"</tem:Tax>\n</tem:FIExpenseItem>\n";
    }
    //mainstr="<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">\n   <soapenv:Header/>\n   <soapenv:Body>\n      <tem:SubmitExpense>\n         <tem:input>\n            <tem:PNO>";
	mainstr="<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">\n   <soapenv:Header/>\n   <soapenv:Body>\n      <tem:SubmitExpense>\n         <tem:input>\n            <tem:DocNumber>";
    mainstr += lsh + "</tem:DocNumber>\n"+"<tem:PNO>";
    mainstr += skrgh + "</tem:PNO>\n" + "<tem:PName>";
    mainstr += skr + "</tem:PName>\n" + "<tem:CompanyCode>";
    //mainstr += URLEncoder.encode(skr, "utf-8") + "</tem:PName>" + "<tem:CompanyCode>";
    mainstr += sapgsdm + "</tem:CompanyCode>\n" + "<tem:Remark>";
    //mainstr += URLEncoder.encode(remark, "utf-8") + "</tem:Remark>";
    //test
    mainstr += remark + "</tem:Remark>\n";
    mainstr += "<tem:Items>\n" + detailstr + "</tem:Items>\n";
    mainstr += "</tem:input>\n" + "</tem:SubmitExpense>\n" + "</soapenv:Body>\n" + "</soapenv:Envelope>\n"; 


      //.append(URLEncoder.encode(skr,"utf-8")).append("</tem:Folk>\n").append("            <tem:Policy>")
      //.append(URLEncoder.encode((String)map
      //.get("zzmm"), -------
      //"utf-8")).append("</tem:Policy>\n").append("            <tem:CertificateNum>")
      //.append((String)map


 //   String mainstr1 = new StringBuilder().append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">\n   <soapenv:Header/>\n   <soapenv:Body>\n      <tem:SubmitExpense>\n         <tem:input>\n            <tem:DocNumber>")
   //   .append(lsh).append("</tem:DocNumber>\n").append("<tem:PNO>")
	 // .append(skrgh).append("</tem:PNO>\n").append("<tem:PName>")
	 // .append(skr)
	 // .append("</tem:PName>\n").append("<tem:CompanyCode>")
	 // .append(sapgsdm).append("</tem:CompanyCode>\n").append("<tem:Remark>")
	 // .append(remark).append("</tem:Remark>\n").append("<tem:Items>\n")
	 // .append(detailstr).append("</tem:Items>\n").append("</tem:input>\n").append("</tem:SubmitExpense>\n").append("</soapenv:Body>\n").append("</soapenv:Envelope>\n").toString();
//    .append("</tem:ICENumber>\n").append("            <tem:WorkHistoryList>\n").append(WorkHistory).append("            </tem:WorkHistoryList>\n").append("            <tem:EducationList>\n").append(Education).append("            </tem:EducationList>\n").append("         </tem:input>\n").append("      </tem:MaintainEmpInfo>\n").append("   </soapenv:Body>\n").append("</soapenv:Envelope>").toString();

    BaseBean bean = new BaseBean();
    try
    {
      String interfaceurl = bean.getPropValue("gddqinterfaceurl", "interfaceurl");
      //URL url = new URL(interfaceurl);
      URL url = new URL("http://192.168.13.254:8080/AEGOAWebService.asmx");
      //URL url = new URL("http://192.168.13.254/OA/AEGOAWebService.asmx");

      HttpURLConnection connection = (HttpURLConnection)url.openConnection();

      connection.setRequestMethod("POST");

      connection.setRequestProperty("content-type", "text/xml;charset=utf-8");
      connection.setRequestProperty("Accept-Charset", "utf-8");

      connection.setDoInput(true);
      connection.setDoOutput(true);


      //String soapXMLtest = GetSoapXml123(detaillist1);
      //String soapXML = GetSoapXml(mainMap, detaillist1, detaillist2);
      String soapXML = GetSoapXml(lsh,skrgh,skr,getztbm(ztbm),remark,detailstr);
      //String soapXML = "";
      //soapXML = mainstr1;

	  
      bean.writeLog(new StringBuilder().append("Mainstr===============").append(soapXML).toString());

      os = connection.getOutputStream();

      os.write(soapXML.getBytes());

      int responseCode = connection.getResponseCode();
      RecordSet rs123 = new RecordSet();
      String sql123 = "update XM_AEGYWZDF_LOG set error1='"+soapXML+"' where id="+maxid;
      rs123.execute(sql123);
	  
      if (200 == responseCode)
      {
        is = connection.getInputStream();
        isr = new InputStreamReader(is, "UTF-8");
        br = new BufferedReader(isr);
        StringBuilder sb = new StringBuilder();
        String temp = null;
        while (null != (temp = br.readLine())) {
          sb.append(temp);
        }
        Document documentHelper = DocumentHelper.parseText(sb.toString());
        Element rootElement = documentHelper.getRootElement();
        Element body = rootElement.element("Body").element("SubmitExpenseResponse").element("SubmitExpenseResult");
        //Iterator iterator = body.elementIterator();
        String Error = "";
        String docno1 = "";
        Element Error1 = body.element("Error");
        Element DocNo = body.element("DocNo");
          if (null != Error1){
             Error = Error1.getText();
          }
          if (null != DocNo){
             docno1 = DocNo.getText();
          }
          sql123 = "update XM_AEGYWZDF_LOG set docno='"+docno1+"',error='"+Error+"' where id="+maxid;
          rs123.execute(sql123);
  	      sqlsap = "update "+maintablename+" set sapsfdy=1,sapdocno='"+docno1+"' where requestid="+requestid;
          rssap.execute(sqlsap);

        if ("" != Error) {
          retStr = "0";
          rm.setMessagecontent(Error);
        }
      }
    } catch (Exception e) {
      bean.writeLog(new StringBuilder().append(" MaintainEmpInfo request error :").append(e).toString());
    } finally {
      try {
        if (isr != null) {
          isr.close();
        }
        if (br != null) {
          br.close();
        }
        if (os != null)
          os.close();
      }
      catch (Exception e) {
        bean.writeLog(new StringBuilder().append("MaintainEmpInfo request error --").append(e).toString());
      }
    }
  }
    //retStr = "0";
    return retStr;
  }

  private static String getaccount(String field)
  {
    String sql = new StringBuilder().append("select kmdm from uf_aegkm where id=").append(field).toString();
    RecordSet rs = new RecordSet();
    rs.execute(sql);
    String name = "";
    if (rs.next()) {
      name = Util.null2String(rs.getString("kmdm"));
    }
    return name;
  }

  private static String getcompanycode(String field)
  {
    String lx = "";
    if ("" != field) {
      switch (field) {
      case "0":
        lx = "1000";
        break;
      case "2":
        lx = "7000";
        break;
      case "13":
        lx = "6000";
        break;
      case "4":
        lx = "2000";
        break;
      case "6":
        lx = "3000";
      }
    }

    return lx;
  }

  private static String getztbm(String field)
  {
    String lx = "";
    if ("" != field) {
      switch (field) {
      case "0":
        lx = "1000";
        break;
      case "2":
        lx = "7000";
        break;
      case "13":
        lx = "6000";
        break;
      case "4":
        lx = "2000";
        break;
      case "6":
        lx = "3000";
      }
    }

    return lx;
  }

  private static String getName(String value) {
    String sql = new StringBuilder().append("SELECT selectname FROM workflow_SelectItem WHERE fieldid=9636 and selectvalue='").append(value).append("'").toString();
    RecordSet rs = new RecordSet();
    rs.execute(sql);
    String name = "";
    if (rs.next()) {
      name = Util.null2String(rs.getString("selectname"));
    }
    return name;
  }

  private static String GetSoapXml(String a1,String a2,String a3,String a4,String a5,String a6) throws UnsupportedEncodingException {
    String soapxml = new StringBuilder().append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">\n   <soapenv:Header/>\n   <soapenv:Body>\n      <tem:SubmitExpense>\n         <tem:input>\n            <tem:DocNumber>")
      .append(a1).append("</tem:DocNumber>\n").append("<tem:PNO>")
	  .append(a2).append("</tem:PNO>\n").append("<tem:PName>")
	  .append(URLEncoder.encode(a3,"utf-8"))
	  .append("</tem:PName>\n").append("<tem:CompanyCode>")
	  .append(a4).append("</tem:CompanyCode>\n").append("<tem:Remark>")
	  .append(URLEncoder.encode(a5,"utf-8")).append("</tem:Remark>\n").append("<tem:Items>\n")
	  .append(a6).append("</tem:Items>\n").append("</tem:input>\n").append("</tem:SubmitExpense>\n").append("</soapenv:Body>\n").append("</soapenv:Envelope>\n").toString();
    return soapxml;
  }


}