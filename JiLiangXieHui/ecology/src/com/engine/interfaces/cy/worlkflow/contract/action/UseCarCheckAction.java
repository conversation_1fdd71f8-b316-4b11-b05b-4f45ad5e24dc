package com.engine.interfaces.cy.worlkflow.contract.action;

import com.engine.parent.workflow.util.ActionUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;

import java.util.HashMap;

public class UseCarCheckAction extends BaseBean implements Action {

    public String useCarDatebegin;
    public String useCarDateend;
    public String useCarTimebegin;
    public String useCarTimeend;
    public String carCol;

    public String out;

    @Override
    public String execute(RequestInfo requestInfo) {
        RequestManager rm = requestInfo.getRequestManager();
        String table = requestInfo.getRequestManager().getBillTableName();
        HashMap<String, String> main = ActionUtil.getMainMap(requestInfo);
        String timeStart = main.get(useCarDatebegin) + " " + main.get(useCarTimebegin);
        String timeEnd = main.get(useCarDateend) + " " + main.get(useCarTimeend);
        RecordSet recordSet = new RecordSet();
        String sql = "select \n" +
                "taba.lcbh lcbh,\n" +
                "wf.currentnodetype,\n" +
                "wf.requestid requestid,\n" +
                "(taba." + useCarDatebegin + "+' '+taba." + useCarTimebegin + ") bg,\n" +
                "(taba." + useCarDateend + "+' '+taba." + useCarTimeend + ") ed \n" +
                "from  \n" +
                table + " taba\n" +
                "left join \n" +
                "workflow_requestbase wf on wf.requestid = taba.requestId where\n" +
                " wf.currentnodetype <> 0 and (taba." + useCarDatebegin + "+' '+taba." + useCarTimebegin + ") is not null and (taba." + useCarDateend + "+' '+taba." + useCarTimeend + ") is not null and taba.cl = '" + main.get(carCol) + "' and\n" +
                " (taba." + useCarDateend + "+' '+taba." + useCarTimeend + ")>= '" + timeStart + "' and (taba." + useCarDatebegin + "+' '+taba." + useCarTimebegin + ")<='" + timeEnd + "' ";
        writeLog("sqL is " + sql);
        boolean flag = recordSet.execute(sql);
        if (flag) {
            if (recordSet.next()) {
                String lcbh = recordSet.getString("lcbh");
                String bg = recordSet.getString("bg");
                String ed = recordSet.getString("ed");
                rm.setMessagecontent(out + " 车辆在<" + bg + "-" + ed + "> 时间段内，已被流程编号为‘" + lcbh + "’流程占用。");
                return FAILURE_AND_CONTINUE;
            } else {
                return SUCCESS;
            }
        } else {
            rm.setMessagecontent("执行sql出错：" + sql + "--" + recordSet.getExceptionMsg());
            return FAILURE_AND_CONTINUE;
        }
    }


    public String getUseCarDatebegin() {
        return useCarDatebegin;
    }

    public void setUseCarDatebegin(String useCarDatebegin) {
        this.useCarDatebegin = useCarDatebegin;
    }

    public String getUseCarDateend() {
        return useCarDateend;
    }

    public void setUseCarDateend(String useCarDateend) {
        this.useCarDateend = useCarDateend;
    }

    public String getUseCarTimebegin() {
        return useCarTimebegin;
    }

    public void setUseCarTimebegin(String useCarTimebegin) {
        this.useCarTimebegin = useCarTimebegin;
    }

    public String getUseCarTimeend() {
        return useCarTimeend;
    }

    public void setUseCarTimeend(String useCarTimeend) {
        this.useCarTimeend = useCarTimeend;
    }

    public String getCarCol() {
        return carCol;
    }

    public void setCarCol(String carCol) {
        this.carCol = carCol;
    }

    public String getOut() {
        return out;
    }

    public void setOut(String out) {
        this.out = out;
    }
}
