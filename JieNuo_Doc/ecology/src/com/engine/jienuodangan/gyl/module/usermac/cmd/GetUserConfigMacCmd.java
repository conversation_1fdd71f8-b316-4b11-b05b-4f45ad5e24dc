package com.engine.jienuodangan.gyl.module.usermac.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.sd2.db.util.DBUtil;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @FileName GetUserConfigMacCmd.java
 * @Description 获取用户配置的mac地址
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/3/28
 */
public class GetUserConfigMacCmd extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * 错误信息
     */
    private String error;
    /**
     * 当前登录的用户id
     */
    private Integer userid;
    /**
     * 配置建模主表id
     */
    private Integer configid;

    @Override
    public BizLogContext getLogContext() {
        return null;
    }


    public GetUserConfigMacCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        error = ""; //初始化错误信息
        userid = -1;
        configid = -1;
    }


    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        log.info(this.getClass().getName() + "---START");
        log.info("params:" + params);
        try {
            //校验参数
            checkParams();
            if (error.isEmpty()) {
                // step 1 :获取当前登录人对应的OA人员id
                getUserIdByLoginid();
                if (userid > 0) {
                    //step 2: 校验当前人员是否是否需要校验mac地址
                    if (needCheckMac()) {
                        result.put("needCheckMac", true);
                        if (configid > 0) {
                            //step 2: 获取配置的mac地址
                            List<String> macList = getUserMacConfig();
                            result.put("macList", macList);
                        } else {
                            log.warn("当前人员还没有建模MAC配置，默认是需要校验的");
                            result.put("macList", null);
                        }
                    } else {
                        result.put("needCheckMac", false);
                    }
                } else {
                    error = "未获取到登录账号对应的OA人员id";
                    log.error(error);
                }

            }
        } catch (Exception e) {
            error = "异常：" + SDUtil.getExceptionDetail(e);
            log.error("异常", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }

        result.put("error", error);
        result.put("status", error.isEmpty());
        log.info("result:" + result);
        log.info(this.getClass().getName() + "---END");
        return result;
    }

    /**
     * 判断是否需要校验MAC地址
     *
     * @return
     */
    private boolean needCheckMac() {
        //默认需要校验，没有配置也需要校验
        boolean needCheck = true;
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        String loginid = Util.null2String(params.get("loginid"));
        //String fieldMacCheck = Util.null2String(params.get("fieldMacCheck"));
        String ignoreLoginids = Util.null2String(params.get("ignoreLoginids"));
        //判断是否是默认忽略的用户id
        if (!ignoreLoginids.isEmpty()) {
            log.info("ignoreLoginids:" + ignoreLoginids);
            String[] ignoreLoginidsArray = ignoreLoginids.split(",");
            for (String ignoreLoginid : ignoreLoginidsArray) {
                if (loginid.equals(ignoreLoginid)) {
                    needCheck = false;
                    break;
                }
            }
        }
        //如果不是忽略的人员，则继续查建模的配置，是否可以不校验
        if (needCheck) {
            String sql = "select * from uf_user_mac where ry =" + userid;
            if (rs.executeQuery(sql)) {
                if (rs.next()) {
                    String checkmac = Util.null2String(rs.getString("checkmac"));
                    configid = rs.getInt("id");
                    //如果是空或者不校验，则needCheck为不校验false
                    if (checkmac.isEmpty() || "0".equals(checkmac)) {
                        log.info("configid:" + configid + ",userid:" + userid + ",当前mac配置为:" + checkmac + ",不需要校验");
                        needCheck = false;
                    } else {
                        log.info("configid:" + configid + ",userid:" + userid + ",当前mac配置为:" + checkmac + ",需要校验");
                    }
                }
            } else {
                log.error("查询用户mac建模配置出错:" + rs.getExceptionMsg() + ";sql:" + sql);
            }
        }
//        String sql = "select " + fieldMacCheck + ",id from cus_fielddata " +
//                " where scope = 'HrmCustomFieldByInfoType' and scopeid = -1" +
//                " and id = (select id from hrmresource where loginid = '" + loginid + "' and (accounttype = 0 or accounttype is null) ) "; //只取主账号的配置
//        log.info("needCheckMac sql:" + sql);
//        if (rs.executeQuery(sql)) {
//            if (rs.next()) {
//                String fieldMacCheckValue = Util.null2String(rs.getString(fieldMacCheck));
//                userid = rs.getInt("id");
//                log.info("userid:" + userid);
//                log.info("fieldMacCheckValue:" + fieldMacCheckValue);
//                //只有当值为0或者空，才不校验
//                if ("0".equals(fieldMacCheckValue) || fieldMacCheckValue.isEmpty()) {
//                    needCheck = false;
//                }
//            }
//        } else {
//            error = "needCheckMac sql error:" + rs.getExceptionMsg();
//            log.error(error);
//        }
        return needCheck;
    }


    /**
     * 获取用户mac地址配置
     *
     * @return
     */
    private List<String> getUserMacConfig() {
        List<String> list = new ArrayList<>();
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        String sql = "select * from uf_user_mac_dt1 " +
                "  where 1=1 " +
                " and mainid =  " + configid +
                " and qy=1 ";
        log.info("getUserMacConfig sql:" + sql);
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                list.add(Util.null2String(rs.getString("macdz")));
            }
        } else {
            error = "getUserMacConfig sql error:" + rs.getExceptionMsg();
            log.error(error);
        }
        return list;
    }

    /**
     * 根据登录id获取人员id
     */
    private void getUserIdByLoginid() {
        String loginid = Util.null2String(params.get("loginid"));
        String sql = "select id from hrmresource where loginid = '" + loginid + "' and (accounttype = 0 or accounttype is null) " +
                "  union all" +
                "  select id from hrmresourcemanager where loginid = '" + loginid + "' ";
        log.info("getUserIdByLoginid sql:" + sql);
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            if (rs.next()) {
                userid = rs.getInt("id");
            }
        }
        log.info("getUserIdByLoginid userid:" + userid);
    }

    /**
     * 校验参数
     */
    private void checkParams() {
        List<String> missCols = new ArrayList<>();
        //校验必填参数
        //登录人id，是否校验mac地址的自定义字段名
        String[] requiredCols = new String[]{"loginid"};
        for (String requiredCol : requiredCols) {
            if (Util.null2String(params.get(requiredCol)).isEmpty()) {
                missCols.add(requiredCol);
            }
        }
        if (!missCols.isEmpty()) {
            error = "缺失参数：" + missCols;
        }
    }
}
