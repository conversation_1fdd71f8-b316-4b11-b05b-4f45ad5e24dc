package com.engine.jienuodangan.gyl.module.usermac.service.impl;

import com.engine.core.impl.Service;
import com.engine.jienuodangan.gyl.module.usermac.cmd.GetUserConfigMacCmd;
import com.engine.jienuodangan.gyl.module.usermac.service.UserMacService;
import weaver.hrm.User;

import java.util.Map;


public class UserMacServiceImpl extends Service implements UserMacService {
    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> getConfigMac(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetUserConfigMacCmd(params, user));
    }
}
