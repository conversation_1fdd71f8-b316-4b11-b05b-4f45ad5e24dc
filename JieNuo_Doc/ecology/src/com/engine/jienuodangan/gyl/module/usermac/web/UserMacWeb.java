package com.engine.jienuodangan.gyl.module.usermac.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.jienuodangan.gyl.module.usermac.service.UserMacService;
import com.engine.jienuodangan.gyl.module.usermac.service.impl.UserMacServiceImpl;
import com.engine.parent.common.util.ApiUtil;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.rsa.security.RSA;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @FileName DemoWeb.java
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date
 */
public class UserMacWeb {
    private UserMacService getService(User user) {
        return ServiceUtil.getService(UserMacServiceImpl.class, user);
    }

    /**
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/getConfigMac")
    @Produces(MediaType.TEXT_PLAIN)
    public String getConfigMac(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        BaseBean bb = new BaseBean();
        Map<String, Object> params = ApiUtil.request2Map(request);
        bb.writeLog("getConfigMac params:", params);
        String loginid = Util.null2String(params.get("loginid"));
        String userpassword = Util.null2String(params.get("userpassword"));
        //判断是否开启了RSA加密
        //是否开启了RSA加密
        String openRSA = Util.null2String(bb.getPropValue("openRSA", "isrsaopen"));
        if ("1".equals(openRSA)) {
            List<String> decriptList = new ArrayList<>();
            RSA rsa = new RSA();
            decriptList.add(loginid);
            decriptList.add(userpassword);
            List resultList = rsa.decryptList(request, decriptList);
            bb.writeLog("resultList:" + resultList);
            bb.writeLog("rsa getmsg:" + rsa.getMessage());
            if (rsa.getMessage().equals("0")) {
                loginid = Util.null2String(resultList.get(0));
                userpassword = Util.null2String(resultList.get(0));
            }
        }
        params.put("loginid", loginid);
        params.put("userpassword", userpassword);
        User user = new User(1);
        //根据登录id获取建模配置的mac信息
        return JSONObject.toJSONString(
                getService(user).getConfigMac(params, user)
        );
    }


}
