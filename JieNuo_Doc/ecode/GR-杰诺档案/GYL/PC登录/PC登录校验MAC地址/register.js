const api_getMacAddress = "http://127.0.0.1:59813/macAddresses"; //本地GR服务的获取MAC地址的接口
const fieldMacCheck = "field0"; //人员自定义字段名-登录校验用户MAC
const ignoreLoginids = "sysadmin"; //忽略校验的人员登录账号

ecodeSDK.rewriteApiParamsQueueSet({
    fn: (url, method, params, type, _fetchParams) => {
        let newUrl = url;
        //判断是PC端和已加载GRSDK
        if (window.ecCom && window.GRSDK) {
            //判断接口
            if (url.indexOf("/api/hrm/login/checkLogin") >= 0) {
                let loginid = params.loginid
                let userpassword = params.userpassword
                let param = {
                    loginid: loginid,
                    userpassword: userpassword,
                    fieldMacCheck: fieldMacCheck,
                    ignoreLoginids: ignoreLoginids
                }
                let result = window.GRSDK.http.post("/api/sd/user/getConfigMac", param)
                console.log("getConfigMac result", result)
                if (result.status === true) {
                    //判断是否需要校验MAC
                    //需要校验MAC地址
                    if (result.needCheckMac === true) {
                        let configMacList = result.macList || "";
                        console.log("configMacList", configMacList)
                        if (configMacList) {
                            //获取当前用户设备实际的MAC地址列表
                            let macResultList = window.GRSDK.http.get(api_getMacAddress, {})
                            console.log("macResultList", macResultList)
                            if (macResultList && macResultList.length > 0) {
                                if (!checkMacValid(configMacList, macResultList)) {
                                    alertError("检测到您当前设备的MAC地址不在规定范围内，禁止登录！")
                                    newUrl = "123"; //屏蔽登录接口
                                } else {
                                    console.log("校验mac成功，允许登录")
                                }
                            } else {
                                alertError("未能获取到您当前设备的MAC信息，请检查是否已经开启了MAC认证服务！")
                                newUrl = "123"; //屏蔽登录接口
                            }
                        } else {
                            alertError("您当前没有授权的MAC地址登录权限，请联系系统管理员！")
                            newUrl = "123"; //屏蔽登录接口
                        }

                    } else {
                        console.log("当前人员不需要校验MAC登录")
                    }
                } else {
                    let error = result.error || JSON.stringify(result)
                    window.antd.message.error("检查mac接口异常:" + error, 5)
                    newUrl = "123"; //屏蔽登录接口
                    //1秒后刷新页面
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000)
                }
            }
        }
        // 不是需要拦截的接口返回默认的参数
        return {
            url: newUrl, // 接口路径
            method: method, // 请求类型
            params: params, // 	请求参数
            type: type,
            _fetchParams: _fetchParams,
        };
    },
    desc: "复写PC端接口传参",
}, 1000);

/**
 * 提示错误
 * @param msg
 */
function alertError(msg) {
    window.antd.Modal.error({
        title: '提示',
        content: msg,
        onOk() {
            console.log("点击知道了");
            //点击知道后，刷新页面，防止登录按钮转圈
            window.location.reload();
        },
    });
}


/**
 * 检查配置的MAC地址是否在设备列表中（忽略大小写）
 * 只要有一个匹配到，则通过校验
 * @param {string[]} configMacList - 配置的MAC列表，如 ["F8:4D:89:63:60:DD"]
 * @param {string[]} macResultList - 设备的MAC列表，如 ["f8:4d:89:63:60:dd","fa:4d:89:36:db:64","fa:4d:89:36:db:65",]
 * @returns {boolean} - 匹配到任意一个返回 true，否则 false
 */
function checkMacValid(configMacList, macResultList) {
    // 1. 将设备MAC列表转为小写（兼容旧浏览器的数组遍历）
    let lowerCaseMacList = [];
    for (let i = 0; i < macResultList.length; i++) {
        lowerCaseMacList.push(macResultList[i].toLowerCase());
    }

    // 2. 遍历配置的MAC地址，检查是否在设备列表中
    for (let j = 0; j < configMacList.length; j++) {
        let configMac = configMacList[j].toLowerCase();
        if (lowerCaseMacList.indexOf(configMac) !== -1) {
            return true; // 匹配成功
        }
    }
    return false; // 全部未匹配
}