class simpleRoot extends React.Component {
    constructor(props) {
        super(props);
        this.state = {}
    }

    componentDidMount() {
        //获取跳转页上的URL参数userid
        if (window.GRSDK) {
            const {util, db} = window.GRSDK;
            let userid = util.getBrowserUrlParam("userid");
            if (userid) {
                //查询用户对应的建模billid
                let tourl = "";
                let billid = -1;
                let sql = "select id from uf_user_mac where ry = " + userid;
                let sqlResult = db.query(sql);
                if (sqlResult && sqlResult.data && sqlResult.data.length > 0) {
                    billid = sqlResult.data[0].id;
                }
                if (billid > 0) {
                    console.log("已有MAC配置数据，跳转显示布局")
                    //编辑模板
                    tourl = "/spa/cube/index.html#/main/cube/card?type=0&modeId=560&billid=" + billid;
                } else {
                    console.log("无MAC配置数据，跳转新建布局")
                    //新建模板
                    tourl = "/spa/cube/index.html#/main/cube/card?type=1&modeId=560&field27456=" + userid;
                }
                window.location.href = tourl;
            } else {
                //页面url没有userid参数
                console.log("页面url没有userid参数，不跳转")
            }
        }
    }

    render() {
        return (
            <div>
                123
            </div>
        )
    }
}

//发布模块
ecodeSDK.setCom('${appId}', 'page', simpleRoot);