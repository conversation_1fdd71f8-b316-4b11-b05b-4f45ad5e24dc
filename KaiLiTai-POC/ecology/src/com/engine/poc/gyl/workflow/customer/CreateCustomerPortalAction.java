package com.engine.poc.gyl.workflow.customer;

import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.workflow.dto.ActionInfo;
import com.engine.parent.workflow.util.ActionUtil;
import com.engine.sd2.crm.CrmUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.sd2.email.util.EmailUtil;
import lombok.Getter;
import lombok.Setter;
import weaver.conn.RecordSet;
import weaver.crm.CrmShareBase;
import weaver.crm.Maint.CustomerInfoComInfo;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import javax.servlet.http.Cookie;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @FileName CreateCustomerPortalAction.java
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/8/19
 */
@Getter
@Setter
public class CreateCustomerPortalAction extends BaseBean implements Action {
    //Action参数---START---
    //Action参数---END---
    //使用二开log类
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private static final String PASSWORD = "123456";

    /**
     * @param requestInfo
     * @return
     */
    @Override
    public String execute(RequestInfo requestInfo) {
        log.info("---START---");
        String error = "";
        try {
            ActionInfo actionInfo = ActionUtil.getInfo(requestInfo);
            //执行业务逻辑
            error = execuetMy(actionInfo, requestInfo);

        } catch (Exception e) {
            error = "执行异常：" + e.getMessage();
            log.error("执行异常：", e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        log.info("---END---");
        return ActionUtil.handleResult(error, requestInfo);
    }

    private String execuetMy(ActionInfo actionInfo, RequestInfo requestInfo) {
        String errorMsg = "";
        try {
            Map<String, String> mainData = actionInfo.getMainData();
            // 是否需要创建客户门户
            String needOpen = Util.null2String(mainData.get("sfktjxsmh"));
            //邮箱地址
            String email = Util.null2String(mainData.get("dzyj"));
            //申请人
            String sqr = Util.null2String(mainData.get("sqr"));
            //供应商名称
            String khid = Util.null2String(mainData.get("khid"));

            Cookie[] cookies = requestInfo.getRequestManager().getRequest().getCookies();
            List<String> cookiesList = new ArrayList<>();
            if (cookies != null) {
                for (Cookie cookie : cookies) {
                    String name = cookie.getName();
                    String value = cookie.getValue();
                    String paramsObj = name + "=" + value;
                    cookiesList.add(paramsObj);
                }
            }
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            String mainTableName = actionInfo.getFormtableName();
            String uuid = UUID.randomUUID().toString();
            String fieldSql = "name, address1,status, type, manager, uuid, createdate, deleted, language,introduction,email";
            String valueSql = "f.khid, f.jtgsdz,2, f.khlx,f.khjl, '" + uuid + "', '" + TimeUtil.getCurrentDateString() + "', 0,7,f.js,f.dzyj ";
            String sql = "insert into CRM_CustomerInfo(" + fieldSql + ") select " + valueSql + " from " + mainTableName + " f where f.requestid=" + actionInfo.getRequestId();
            log.info("sql 010 = " + sql);
            if (rs.executeUpdate(sql)) {
                if (rs.executeQuery("select max(id) as mid from CRM_CustomerInfo where uuid='" + uuid + "'")) {
                    if (rs.next()) {
                        String customerId = rs.getString("mid");
                        log.info("customerId: " + customerId);
                        String cookiesStr = String.join(";", cookiesList);
                        log.info("cookiesStr: " + cookiesStr);
                        log.info("needOpen: " + needOpen);
                        if ("0".equals(needOpen)) {
                            errorMsg = CrmUtil.applyPortal("http://clouddemo.e-cology.com.cn:65282", customerId, PASSWORD, cookiesStr);
                        }
                        //设置缓存，设置默认共享
                        CustomerInfoComInfo customerInfoComInfo = new CustomerInfoComInfo();
                        customerInfoComInfo.addCustomerInfoCache(customerId);

                        CrmShareBase crmShareBase = new CrmShareBase();
                        crmShareBase.setDefaultShare(customerId);
                        if ("0".equals(needOpen)) {
                            sendEmail(email, sqr, customerId, needOpen, khid);
                        }
                    }
                }
            } else {
                log.error("insert error" + rs.getExceptionMsg());
            }


        } catch (Exception e) {
            errorMsg = this.getClass().getName() + "异常：" + SDUtil.getExceptionDetail(e);
        }

        return errorMsg;
    }

    private void sendEmail(String email, String sqr, String customerId, String needOpen, String name) {
        if (!email.isEmpty()) {
            String sendTo = email;
            String numberName = String.format("%05d", Util.getIntValue(customerId));
            String sendCc = getSqrEmail(sqr);
            String title = "客户新建提醒";
            String content = "尊敬的" + name + "，恭喜您已成功被选为合格经销商!<br/>";
            content += "您可以通过以下信息登录我司经销商管理系统，您可以执行在线下订单、收发货等操作。<br/>";
            content += "-----------------------<br/>";
            content += "登录地址：http://clouddemo.e-cology.com.cn:65282/wui/index.html#/?logintype=2 <br/>";
            content += "账号名：U" + numberName + "<br/>";
            content += "初始密码：1234567" + "<br/>";
            log.info("sendTo:" + sendTo);
            log.info("sendCc:" + sendCc);
            log.info("title:" + title);
            log.info("content:" + content);
            if (!EmailUtil.sendMail(sendTo, sendCc, "", title, content)) {
                log.error("发送邮件失败！");
            }
        }
    }

    private String getSqrEmail(String sqr) {
        if (!"1".equals(sqr)) {
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            String sql = "select email from hrmresource where id =" + sqr;
            if (rs.executeQuery(sql)) {
                if (rs.next()) {
                    return Util.null2String(rs.getString("email"));
                }
            }
        }
        return "";

    }
}
