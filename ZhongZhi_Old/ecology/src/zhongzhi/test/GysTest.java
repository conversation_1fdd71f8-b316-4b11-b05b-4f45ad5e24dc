package zhongzhi.test;

import org.codehaus.xfire.client.Client;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.net.URL;

public class GysTest {
    private static String url = "http://127.0.0.1:88/services/GysService?wsdl";//webservice地址

    public static void main(String[] args) {
        try {
            GysTest t=new GysTest();
            Document doc= DocumentHelper.createDocument();
            Client client=new Client(new URL(url));
            Object[] results = null;

           /* doc= t.saveProjectXml(doc);
            System.out.println("result:\n"+ doc.asXML());
            results = client.invoke("saveProject", new Object[] { doc.asXML() });//创建项目

            doc = t.updateProjectXml(doc);
            results = client.invoke("updateProject", new Object[] { doc.asXML() });//更新项目

            doc = t.deleteProjectXml(doc);
            results = client.invoke("deleteProject", new Object[] { doc.asXML() });//删除项目*/

            doc = t.queryGysxml(doc);
            results = client.invoke("getModeDataByID", new Object[] { doc.asXML() });//查询项目

            if (results!=null) {//操作结果
                System.out.println("result:\n"+ results[0].toString());
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 查询项目，只支持编号查询
     * @param doc
     * @return
     */
    private Document queryGysxml(Document doc){
        /*Element rootElement= doc.addElement("project");
        //用户权限信息
        Element e= rootElement.addElement("user");
        e.addElement("loginid").addText("wym");//人员编号
        e.addElement("password").addText("1");//密码

        //基本信息
        e=rootElement.addElement("base");
        e.addElement("procode").addText("proj202011110001");//项目编码*/

        /**
         *
         *    <gys:in0>53</gys:in0>
         *          <gys:in1>	hr-007</gys:in1>
         *          <gys:in2>4</gys:in2>
         *          <gys:in3>y</gys:in3>
         *          <gys:in4>y</gys:in4>
         */
        
        Element rootElement= doc.addElement("ROOT");
        Element headerElement= rootElement.addElement("header");

        headerElement.addElement("userid").addText("1");
        headerElement.addElement("modeid").addText("2");
        headerElement.addElement("gt2gysbh").addText("ccc0006");
        return doc;
    }

    /**
     * 删除项目，只支持通过编号进行查询
     * @param doc
     * @return
     */
    private Document deleteProjectXml(Document doc){
        Element rootElement= doc.addElement("project");
        //用户权限信息
        Element e= rootElement.addElement("user");
        e.addElement("loginid").addText("zyt4");//人员编号
        e.addElement("password").addText("1");//密码

        //基本信息
        e=rootElement.addElement("base");
        e.addElement("procode").addText("prj0002");//项目编码

        return doc;
    }

    /**
     * 更新项目
     * @param doc
     * @return
     */
    private Document updateProjectXml(Document doc){
        Element rootElement= doc.addElement("project");
        //用户权限信息
        Element e= rootElement.addElement("user");
        e.addElement("loginid").addText("zyt4");//人员编号
        e.addElement("password").addText("1");//密码

        //基本信息
        e=rootElement.addElement("base");
        e.addElement("procode").addText("prjccww0002");//项目编码
        e.addElement("name").addText("test项目update");//项目名称
        e.addElement("prjtype").addText("项目类型AAA");//项目类型
        e.addElement("worktype").addText("工作类型AAA");//项目工作类型
        e.addElement("members").addText("zyt4,zyt2,zyt3");//项目成员
        e.addElement("isblock").addText("1");

        //管理信息
        e=rootElement.addElement("manage");
        e.addElement("manager").addText("cj22");

        return doc;
    }

    /**
     * 创建项目
     * @param doc
     * @return
     */
    private Document saveProjectXml(Document doc){
        Element rootElement= doc.addElement("project");
        //用户权限信息
        Element e= rootElement.addElement("user");
        e.addElement("loginid").addText("zyt4");//人员编号
        e.addElement("password").addText("1");//密码

        //基本信息
        e=rootElement.addElement("base");
        e.addElement("procode").addText("prjccww0002");
        e.addElement("name").addText("test项目");
        e.addElement("prjtype").addText("项目类型AAA");
        e.addElement("worktype").addText("工作类型AAA");
        e.addElement("members").addText("zyt4,zyt2,zyt3");
        e.addElement("isblock").addText("1");

        //管理信息
        e=rootElement.addElement("manage");
        e.addElement("manager").addText("cj22");

        //其它信息
        e=rootElement.addElement("other");
        e.addElement("a1").addAttribute("label", "其它信息1") .addText("测试");
        e.addElement("a2").addAttribute("label", "hello") .addText("你好");
        e.addElement("a3").addAttribute("label", "祝福") .addText("愿你天天好心情!");

        return doc;
    }
}
