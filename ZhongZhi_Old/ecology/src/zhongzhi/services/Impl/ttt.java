package zhongzhi.services.Impl;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;


public class ttt {
    public static void main(String[] args) {
        Document document = DocumentHelper.createDocument();
        Element var6 = document.addElement("result").addAttribute("time", System.currentTimeMillis()+"").addAttribute("status", "1").addAttribute("msg", "查询项目成功!");
        //企业名称
        String tt=",";
        for(int i=0;i<3;i++){
            /*Element qymcNode = var6.addElement("project");
            Element baseelement = qymcNode.addElement("base");
            baseelement.addElement("procode").addText("1111111");
            baseelement.addElement("name").addText("cesgu");
            baseelement.addElement("prjtype").addText("22");
            baseelement.addElement("worktype").addText("1");*/
            tt+=i+",";

        }
        System.out.println(tt);

        String aa=",123,2222,";
       aa= aa.substring(1,aa.length()-1);
        System.out.println(aa);
    }
}
