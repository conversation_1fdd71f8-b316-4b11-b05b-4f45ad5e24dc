
package zhongzhi.services.Impl;

import com.engine.cube.biz.CodeBuilder;
import java.io.ByteArrayInputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.jdom.CDATA;
import org.jdom.Document;
import org.jdom.Element;
import org.jdom.input.SAXBuilder;
import org.jdom.output.Format;
import org.jdom.output.XMLOutputter;
import weaver.conn.RecordSet;
import weaver.formmode.dao.FormInfoDao;
import weaver.formmode.log.FormmodeLog;
import weaver.formmode.setup.ModeRightInfo;
import weaver.formmode.setup.ModeSetUtil;
import weaver.formmode.view.ModeShareManager;
import weaver.formmode.view.ModeViewLog;
import weaver.formmode.virtualform.VirtualFormHandler;
import weaver.formmode.webservices.FieldInfo;
import weaver.formmode.webservices.FileUtil;
import weaver.formmode.webservices.exception.ModeDataExceptionEnum;
import weaver.formmode.webservices.exception.ModeDataServiceException;
import weaver.general.Base64;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.security.util.SecurityMethodUtil;
import weaver.systeminfo.SystemEnv;
import weaver.workflow.form.FormManager;
import zhongzhi.services.YsService;

public class YsServiceImpl extends FormmodeLog implements YsService {
    private static final String base64Flag = "Base64_";
    private FieldInfo fieldInfo = new FieldInfo();
    private FileUtil fileutil = new FileUtil();
    private String oaip = this.getPropValue("OAIpaddress", "Ipaddress");

    public YsServiceImpl() {
    }

    public String deleteModeDataById(int var1, String bh, int var3, String var4) {
        String var5 = "";
        boolean var6 = false;

        try {
            RecordSet var7 = new RecordSet();
            FieldInfo var8 = new FieldInfo();
            var8.getMainTableName(var1);
            var8.getDetailTableNameList(var1);
            String var9 = var8.getTablename();
            int var10 = this.getFormIdByModeId(var1);
            boolean var11 = VirtualFormHandler.isVirtualForm(var10);
            String column = "xmbm";
            String var13 = "";
            if (var11) {
                FormInfoDao var14 = new FormInfoDao();
                Map var15 = var14.getFormInfoById(var10);
                var9 = VirtualFormHandler.getRealFromName(var9);
                column = Util.null2String(var15.get("vprimarykey"));
                var13 = this.getVDataSource(var15);
            }
            String sql="select id from "+var9+" where "+column+"=?";
            var7.executeQuery(sql, bh+"");
            //供应商信息只存在一条数据
            int billid=-1;
            if(var7.next()){
                billid=var7.getInt("id");
            }
            List var20 = var8.getDetailtable_list();
            String var21 = "";
            String var16 = "delete from " + var9 + " where " + column + "=?";
            if (var11) {
                RecordSet var17 = new RecordSet();
                var17.executeQueryWithDatasource("select * from " + var9 + " where " + column + "=?", var13, new Object[]{bh});
                Map var18 = this.recordSet2Map(var17);
                this.writeLog("deleteModeDataById.oldData:" + var18);
                var6 = var7.executeUpdateWithDatasource(var16, var13, new Object[]{bh});
            } else {
                var6 = var7.executeUpdate(var16, new Object[]{bh});
            }

            for(int var22 = 0; var22 < var20.size(); ++var22) {
                var21 = var20.get(var22).toString();
                var16 = "delete from " + var21 + " where mainid=?";
                var7.executeUpdate(var16, new Object[]{bh});
            }

            this.saveModeDataOthers(var1, billid, var3, "deleteAll", "5");
            if (var6) {
                var5 = this.getReturnElements(billid+"", 0, SystemEnv.getHtmlLabelName(83885, Util.threadVarLanguage()));
            } else {
                var5 = this.getReturnElements(billid+"", -1, SystemEnv.getHtmlLabelName(83912, Util.threadVarLanguage()));
            }
        } catch (Exception var19) {
            var19.printStackTrace();
            this.writeLog("deleteModeDataById:" + var19);
            var5 = this.getErrorMsg(var19.getMessage());
        }

        return var5;
    }

    public String saveModeData(String var1) {
        String var2 = "";
        boolean var3 = false;
        String var4 = "";
        String bh ="";
        boolean var6 = false;

        try {
            if (Util.null2String(var1).length() > 0 && var1.startsWith("Base64_")) {
                var1 = var1.replace("Base64_", "");
                var1 = new String(Base64.decode(var1.getBytes()));
                var6 = true;
            }

            RecordSet var7 = new RecordSet();
            SAXBuilder var8 = new SAXBuilder();
            Document var9 = null;

            try {
                SecurityMethodUtil.setSaxBuilderFeature(var8);
                var9 = var8.build(new ByteArrayInputStream(var1.getBytes("UTF-8")));
            } catch (Exception var52) {
                this.writeLog(var52);
                throw new ModeDataServiceException(ModeDataExceptionEnum.PARSE_XML_FAILURE.toStringWithExp(var52.getMessage()));
            }

            Element var10 = var9.getRootElement();
            Element var11 = var10.getChild("header");
            if (var11 == null) {
                throw new ModeDataServiceException(ModeDataExceptionEnum.HEADER_NODE_NOT_FOUND.toString());
            }

            Integer var12 = Util.getIntValue(var11.getChildText("userid"));
            Integer var13 = Util.getIntValue(var11.getChildText("modeid"));
            bh = var11.getChildText("xmbm");
            if (var13 == -1) {
                throw new ModeDataServiceException(ModeDataExceptionEnum.MODEID_NOT_FOUND.toString());
            }

            if (var12 == -1) {
                throw new ModeDataServiceException(ModeDataExceptionEnum.USERID_NOT_FOUND.toString());
            }

            Element var14 = var10.getChild("search");
            if (var14 == null) {
                throw new ModeDataServiceException(ModeDataExceptionEnum.SEARCH_NODE_NOT_FOUND.toString());
            }

            Integer var15 = -1;
            String var16 = "";
            String var17 = "select w.tablename,w.id from modeinfo m,workflow_bill w where w.id=m.formid and m.id=?";
            var7.executeQuery(var17, new Object[]{var13});
            if (var7.next()) {
                var15 = Util.getIntValue(var7.getString("id"));
                var16 = Util.null2String(var7.getString("tablename"));
            }

            if (var15 == -1) {
                throw new ModeDataServiceException(ModeDataExceptionEnum.FORMID_NOT_FOUND.toString());
            }

            boolean var18 = VirtualFormHandler.isVirtualForm(var15);
            String var19 = "";
            String column = "xmbm";
            if (var18) {
                FormInfoDao var21 = new FormInfoDao();
                Map var22 = var21.getFormInfoById(var15);
                var19 = this.getVDataSource(var22);
                column = Util.null2String(var22.get("vprimarykey"));
                var16 = VirtualFormHandler.getRealFromName(var16);
            }

            if (var16.length() > 33 && var16.charAt(32) == '_') {
                throw new ModeDataServiceException(ModeDataExceptionEnum.NOT_SUPPORT_VIRFORM.toString());
            }

            String var54 = "";
            String var55 = "";
            String var23 = "";
            String var24 = "";
            String var25 = "";
            String var26 = "";
            String var27 = "";
            Element var28 = var10.getChild("data");
            if (var28 == null) {
                throw new ModeDataServiceException(ModeDataExceptionEnum.DATA_NODE_NOT_FOUND.toString());
            }

            var28 = var28.getChild("maintable");
            if (var28 == null) {
                throw new ModeDataServiceException(ModeDataExceptionEnum.MAINTABLE_NODE_NOT_FOUND.toString());
            }

            List var29 = var28.getChildren("field");
            ArrayList var30 = new ArrayList();
            ArrayList var31 = new ArrayList();

            for(int var32 = 0; var32 < var29.size(); ++var32) {
                var25 = Util.null2String(((Element)var29.get(var32)).getChildText("filedname"));
                var26 = Util.null2String(((Element)var29.get(var32)).getChildText("filedlabel"));
                var27 = Util.null2String(((Element)var29.get(var32)).getChildText("filedvalue"));
                var27 = Util.toHtmlForWorkflow(var27);
                var23 = Util.null2String(((Element)var29.get(var32)).getChildText("fileddbtype")).toLowerCase();
                var54 = var54 + var25;
                var54 = var54 + ",";
                if (!var23.startsWith("int") && !var23.startsWith("float") && !var23.startsWith("number") && !var23.startsWith("decimal")) {
                    if (var23.equals("clob") ||var23.equals("text") || var23.contains("char")) {
                        var55 = var55 + "'" + var27 + "'";
                    } else {
                        var30.add(var27);
                        var55 = var55 + "?";
                    }
                } else {
                    var55 = var55 + (!var27.equals("") ? var27 : "null") + "";
                }

                var55 = var55 + ",";
                if (!"".equals(var24)) {
                    var24 = var24 + ",";
                }

                if (!var23.startsWith("int") && !var23.startsWith("float") && !var23.startsWith("number") && !var23.startsWith("decimal")) {
                    if (var23.equals("clob") ||var23.equals("text") || var23.contains("char")) {
                        var24 = var24 + var25 + "='" + var27 + "'";
                    } else {
                        var31.add(var27);
                        var24 = var24 + "?";
                    }
                } else {
                    var24 = var24 + var25 + "=" + (!var27.equals("") ? var27 : "null") + "";
                }
            }

            Object[] var56 = new Object[var30.size()];

            int var33;
            for(var33 = 0; var33 < var30.size(); ++var33) {
                var56[var33] = var30.get(var33);
            }
            int billid=-1;
            if (!"".equals(bh)) {
                if (!"".equals(var24)) {
                    Object[] var58 = new Object[var31.size() + 1];
                    var58[0] = bh;

                    for(int var59 = 1; var59 < var31.size(); ++var59) {
                        var58[var59] = var31.get(var59);
                    }

                    if (var18) {
                        RecordSet var61 = new RecordSet();
                        var17 = "select * from " + var16 + " where " + column + "=?";
                        var61.executeQueryWithDatasource(var17, var19, new Object[]{bh});
                        Map var35 = this.recordSet2Map(var61);
                        this.writeLog("saveModeData.oldData:" + var35);
                        var17 = "update " + var16 + " set " + var24 + " where " + column + "=?";
                        var3 = var7.executeUpdateWithDatasource(var17, var19, var58);
                    } else {
                        var17 = "update " + var16 + " set " + var24 + " where xmbm=?";
                        var3 = var7.executeUpdate(var17, var58);
                        var17="select id from "+var16+" where xmbm=?";
                        var7.executeQuery(var17, var58);
                        if(var7.next()){
                            billid=var7.getInt("id");
                        }

                    }

                    this.writeLog(var17);
                    if (!var3) {
                        throw new ModeDataServiceException(ModeDataExceptionEnum.UPDATE_SQL_FAILURE.toStringWithExp(var17));
                    }
                }
            } else {
                if (var18) {
                    var54 = this.removePrefixAndSuffix(var54, ",");
                    var55 = this.removePrefixAndSuffix(var55, ",");
                    var17 = "insert into " + var16 + " (" + var54 + ") values (" + var55 + ")";
                    this.writeLog(var17);
                    var3 = var7.executeUpdateWithDatasource(var17, var19, var56);
                } else {
                    var17 = "insert into " + var16 + " (" + var54 + "formmodeid,modedatacreater,modedatacreatertype,modedatacreatedate,modedatacreatetime) values (" + var55 + " " + var13 + "," + var12 + ",0,'" + this.getCurrentDate() + "','" + this.getCurrentTime() + "')";
                    var3 = var7.executeUpdate(var17, var56);
                    this.writeLog(var17);
                }

                if (!var3) {
                    throw new ModeDataServiceException(ModeDataExceptionEnum.INSERT_SQL_FAILURE.toStringWithExp(var17));
                }

                var17 = "select max(id) as id from " + var16;
                var7.executeQuery(var17, new Object[0]);
                billid = var7.next() ? Util.getIntValue(var7.getString("id")) : 0;
                CodeBuilder var57 = new CodeBuilder(var13);
                var57.getModeCodeStr(var15, billid);
                ModeSetUtil var34 = new ModeSetUtil();
                var34.setModedatastatusValue(var7, var13, billid, var16, 0);
            }

            this.saveModeDataOthers(var13, billid, var12, "save", "6");
            this.resolveFilesElement(var13, var12, billid, var16, var28.getChildren("files"));
            boolean var60 = true;
            String var62 = "";
            String var63 = "";
            String var36 = "";
            String var37 = "";
            String var38 = "";
            String var39 = "";
            FormManager var40 = new FormManager();
            if (var10.getChild("data").getChild("detail") != null && var10.getChild("data").getChild("detail").getChildren("detailtable") != null) {
                List var41 = var10.getChild("data").getChild("detail").getChildren("detailtable");
                ArrayList var42 = new ArrayList();
                ArrayList var43 = new ArrayList();

                for(int var44 = 0; var44 < var41.size(); ++var44) {
                    var33 = Util.getIntValue(((Element)var41.get(var44)).getAttributeValue("id")) + 1;
                    var39 = var40.getDetailTablename(var15, var33);
                    List var45 = ((Element)var41.get(var44)).getChildren("row");

                    for(int var46 = 0; var46 < var45.size(); ++var46) {
                        Element var47 = (Element)var45.get(var46);
                        var62 = var47.getAttributeValue("id");
                        var63 = var47.getAttributeValue("action");
                        var54 = "";
                        var55 = "";
                        var23 = "";
                        var24 = "";
                        List var48 = var47.getChildren("field");

                        for(int var49 = 0; var49 < var48.size(); ++var49) {
                            var36 = Util.null2String(((Element)var48.get(var49)).getChildText("filedname"));
                            var37 = Util.null2String(((Element)var48.get(var49)).getChildText("filedlabel"));
                            var38 = Util.null2String(((Element)var48.get(var49)).getChildText("filedvalue"));
                            var38 = Util.toHtmlForWorkflow(var38);
                            var23 = Util.null2String(((Element)var48.get(var49)).getChildText("fileddbtype")).toLowerCase();
                            if (!"".equals(var54)) {
                                var54 = var54 + ",";
                            }

                            var54 = var54 + var36;
                            if (!"".equals(var55)) {
                                var55 = var55 + ",";
                            }

                            if (!var23.startsWith("int") && !var23.startsWith("float") && !var23.startsWith("number") && !var23.startsWith("decimal")) {
                                if (!var23.equals("clob") && !var23.equals("text")) {
                                    var55 = var55 + "'" + var38 + "'";
                                } else {
                                    var42.add(var27);
                                    var55 = var55 + "?";
                                }
                            } else {
                                var55 = var55 + (!var38.equals("") ? var38 : "null");
                            }

                            if (!"".equals(var24)) {
                                var24 = var24 + ",";
                            }

                            if (!var23.startsWith("int") && !var23.startsWith("float") && !var23.startsWith("number") && !var23.startsWith("decimal")) {
                                if (!var23.equals("clob") && !var23.equals("text")) {
                                    var24 = var24 + var36 + "='" + var38 + "'";
                                } else {
                                    var43.add(var27);
                                    var24 = var24 + var36 + "=？";
                                }
                            } else {
                                var24 = var24 + var36 + "=" + (!var38.equals("") ? var38 : "null") + "";
                            }
                        }

                        boolean var64 = false;
                        Object[] var50;
                        int var51;
                        if ("add".equalsIgnoreCase(var63)) {
                            var50 = new Object[var42.size()];

                            for(var51 = 0; var51 < var42.size(); ++var51) {
                                var50[var51] = var42.get(var51);
                            }

                            var17 = "insert into " + var39 + " (mainid," + var54 + ") values (" + bh + "," + var55 + ")";
                            if (var54.equals("")) {
                                var17 = "insert into " + var39 + " (mainid) values (" + bh + ")";
                            }

                            var64 = var7.executeUpdate(var17, var50);
                        } else if (!"update".equalsIgnoreCase(var63)) {
                            if ("delete".equalsIgnoreCase(var63)) {
                                var17 = "delete from " + var39 + " where id= " + var62;
                                var64 = var7.executeUpdate(var17, new Object[0]);
                            }
                        } else {
                            var50 = new Object[var43.size()];

                            for(var51 = 0; var51 < var43.size(); ++var51) {
                                var50[var51] = var43.get(var51);
                            }

                            var17 = "update " + var39 + " set " + var24 + " where id=" + var62;
                            var64 = var7.executeUpdate(var17, var50);
                        }

                        this.writeLog(var17);
                        if (!var64) {
                            throw new ModeDataServiceException(ModeDataExceptionEnum.DETAILTABLE_OPERATE_FAILURE.toStringWithExp(var17));
                        }

                        if ("add".equalsIgnoreCase(var63)) {
                            var17 = "select max(id) as id from " + var39;
                            var7.executeSql(var17);
                            var7.next();
                            var62 = Util.null2String(var7.getString("id"));
                        }

                        this.resolveFilesElement(var13, var12, Integer.parseInt(var62), var39, var47.getChildren("files"));
                    }
                }
            }
        } catch (Exception var53) {
            var53.printStackTrace();
            this.writeLog("saveModeData:" + var53);
            var4 = var53.getMessage();
        }

        if ("".equals(var4)) {
            var2 = this.getReturnElements(bh, 0, SystemEnv.getHtmlLabelName(83885, Util.threadVarLanguage()));
        } else {
            var2 = this.getReturnElements(bh, -1, var4);
        }

        if (var6) {
            var2 = new String(Base64.encode(var2.getBytes()));
        }

        return var2;
    }

    public int getAllModeDataCount(int modeId, int userid, String conditions, String right) {
        RecordSet var5 = new RecordSet();
        FieldInfo var6 = new FieldInfo();
        var6.getMainTableName(modeId);
        String var7 = var6.getTablename();
        int var8 = this.getFormIdByModeId(modeId);
        boolean var9 = VirtualFormHandler.isVirtualForm(var8);
        if (var9) {
            var7 = VirtualFormHandler.getRealFromName(var7);
        }

        var7 = var7 + " ";
        String var10 = " where 1=1 ";
        if ("y".equalsIgnoreCase(right.trim()) && !var9) {
            var7 = var7 + "t1," + this.getShareSql(modeId, userid) + " t2 ";
            var10 = var10 + " and t1.id = t2.sourceid ";
        }

        if (!"".equals(conditions.trim())) {
            conditions = " and " + conditions;
        }

        var10 = var10 + conditions;
        String var11 = "select id from " + var7 + var10;
        this.writeLog(var11);
        if (var9) {
            try {
                FormInfoDao var12 = new FormInfoDao();
                Map var13 = var12.getFormInfoById(var8);
                String var14 = this.getVDataSource(var13);
                var5.executeSqlWithDataSource(var11, var14);
            } catch (Exception var15) {
                this.writeLog(var15);
            }
        } else {
            var5.executeSql(var11);
        }

        return var5.getCounts();
    }

    public String getAllModeDataList(int modeId, int userid,String currdate) {
        byte var9 = 7;
        String var10 = "";
        try {
            Document doc = new Document();
            Element rootElement = new Element("ROOT");
            doc.setRootElement(rootElement);
            Element var13 = this.getHeaderElement(modeId, "", userid);
            rootElement.addContent(var13);
            RecordSet rs = new RecordSet();
            FieldInfo fieldInfo = new FieldInfo();
            fieldInfo.getManTableField(modeId, var9);
            fieldInfo.getDetailTableField(modeId, var9);
            fieldInfo.getMainTableName(modeId);
            fieldInfo.getDetailTableNameList(modeId);
            List getLabel_list = fieldInfo.getLabel_list();
            List getType_list = fieldInfo.getType_list();
            List getName_list = fieldInfo.getName_list();
            List getValue_list = fieldInfo.getValue_list();
            List getHtmltype_list = fieldInfo.getHtmltype_list();
            List getFielddbtype_list = fieldInfo.getFielddbtype_list();
            List getFieldid_list = fieldInfo.getFieldid_list();
            String tablename = fieldInfo.getTablename();
            int var28 = this.getFormIdByModeId(modeId);
            boolean isVirtualForm = VirtualFormHandler.isVirtualForm(var28);
            String vDataSource = "";
            String id = "id";
            String allfield = " id,modedatacreater,modedatacreatedate,modedatacreatetime ";
            if (isVirtualForm) {
                FormInfoDao formInfoDao = new FormInfoDao();
                Map FormInfoMap = formInfoDao.getFormInfoById(var28);
                vDataSource = this.getVDataSource(FormInfoMap);
                id = Util.null2String(FormInfoMap.get("vprimarykey"));
                tablename = VirtualFormHandler.getRealFromName(tablename);
                allfield = "";
            }
            String select = " select ";

            for(int i = 0; i < getName_list.size(); ++i) {
                allfield = allfield + ",";
                allfield = allfield + getName_list.get(i).toString();
            }

            allfield = allfield.trim();
            if (allfield.startsWith(",")) {
                allfield = allfield.substring(1);
            }


            String var57 = " from " + tablename + " ";
            String var35 = " where 1=1 ";
           /* SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Date date = new Date();
            String currdate=format.format(date);*/
            String likecon=" and t1.modedatacreatedate='"+currdate+"'";
            if (!isVirtualForm) {
                var57 = var57 + "t1," + this.getShareSql(modeId, userid) + " t2 ";
                var35 = var35 + " and t1.id = t2.sourceid "+likecon;
            }
            //select * form uu var35 + conditions
            String var36 = " order by " + id;
            String var37 = " order by " + id + " desc";
            String var38 = " order by " + id + " asc";
            String var39 = "";
            String var40 = "";
            String var41 = "";
            String var42 = "";
            String var43 = "";
            String var44 = "";
            String var45 = "";
            String var46 = "";
            String var47 = "";
            String var48 = "";
            String var49 = "";
//            String var50 = this.getPaginationSql(select, allfield, var57, var35, var36, var37, var38, pageNo, pageSize, recordCount, vDataSource);
            //取消分页查询并添加一当前日期为条件模糊查询

            String sql=select + " " +allfield+" "+var57+" "+var35+" "+var36+" ";
            this.writeLog("selectsql======="+sql);
 //           writeLog("var50sql======="+var50);

            if (isVirtualForm) {
                rs.executeSqlWithDataSource(sql, vDataSource);
            } else {
                writeLog("varyssql======="+sql);
                rs.executeSql(sql);
            }

            Element element;
            for(; rs.next(); rootElement.addContent(element)) {
                var46 = Util.null2String(rs.getString("id"));
                var47 = Util.null2String(rs.getString("modedatacreater"));
                var48 = Util.null2String(rs.getString("modedatacreatedate"));
                var49 = Util.null2String(rs.getString("modedatacreatetime"));
                element = new Element("data");
                element.setAttribute("id", var46);
                element.addContent(this.getHandlersElemens(var47, var48, var49));
                Element var16 = new Element("maintable");

                for(int var51 = 0; var51 < getName_list.size(); ++var51) {
                    var41 = getName_list.get(var51).toString();
                    var39 = getLabel_list.get(var51).toString();
                    var40 = getType_list.get(var51).toString();
                    var43 = getHtmltype_list.get(var51).toString();
                    var44 = getFielddbtype_list.get(var51).toString();
                    var45 = getFieldid_list.get(var51).toString();
                    var42 = Util.null2String(rs.getString(var41));

                    try {
                        Element var15 = this.getFieldElements(var45, var39, var41, var42, var40, var43, var44);
                        var16.addContent(var15);
                    } catch (Exception var53) {
                        this.writeLog("fieldElement:" + var53);
                    }
                }

                element.addContent(var16);
                Element var17 = this.getDetailDataElements(modeId, var46, var9, fieldInfo);
                element.addContent(var17);

            }

            var10 = this.createXml(doc);
        } catch (Exception var54) {
            var54.printStackTrace();
            this.writeLog("getAllModeDataList:" + var54);
            var10 = this.getErrorMsg(var54.getMessage());
        }

        return var10;
    }

    public String getModeDataByID(int var1, String xmbm, int var3, String var4, String var5) {
        byte var6 = 7;
        String var7 = "";

        try {
            RecordSet var8 = new RecordSet();
            Document var9 = new Document();
            Element var10 = new Element("ROOT");
            var9.setRootElement(var10);
            Element var11 = this.getHeaderElement(var1, xmbm, var3);
            var10.addContent(var11);
            var10.addContent(this.getSearchElements("", var4));
            FieldInfo var16 = new FieldInfo();
            var16.getManTableField(var1, var6);
            var16.getDetailTableField(var1, var6);
            var16.getMainTableName(var1);
            var16.getDetailTableNameList(var1);
            List var17 = var16.getLabel_list();
            List var18 = var16.getType_list();
            List var19 = var16.getName_list();
            List var20 = var16.getValue_list();
            List var21 = var16.getHtmltype_list();
            List var22 = var16.getFielddbtype_list();
            List var23 = var16.getFieldid_list();
            String var24 = var16.getTablename();
            int var25 = this.getFormIdByModeId(var1);
            boolean var26 = VirtualFormHandler.isVirtualForm(var25);
            String bh = "xmbm";
            String var28 = "";
            if (var26) {
                FormInfoDao var29 = new FormInfoDao();
                Map var30 = var29.getFormInfoById(var25);
                var28 = this.getVDataSource(var30);
                bh = Util.null2String(var30.get("vprimarykey"));
                var24 = VirtualFormHandler.getRealFromName(var24);
            }

            String var45 = "";
            String var46 = "";
            String var31 = "";
            String var32 = "";
            String var33 = "";
            String var34 = "";
            String var35 = "";
            String var36 = "";
            String var37 = "";
            String var38 = "";
            String var39 = "";
            String var40 = "";
            var45 = "select * from " + var24 + " where " + bh + " = ?";
            if (var26) {
                var8.executeQueryWithDatasource(var45, var28, new Object[]{xmbm});
            } else {
                var8.executeQuery(var45, new Object[]{xmbm});
            }

            this.writeLog(var45);

            Element var15;
            for(; var8.next(); var10.addContent(var15)) {
                var37 = Util.null2String(var8.getString("id"));
                var38 = Util.null2String(var8.getString("modedatacreater"));
                var39 = Util.null2String(var8.getString("modedatacreatedate"));
                var40 = Util.null2String(var8.getString("modedatacreatetime"));
                var15 = new Element("data");
                var15.setAttribute("id", var37);
                var15.addContent(this.getHandlersElemens(var38, var39, var40));
                Element var14 = new Element("maintable");

                for(int var41 = 0; var41 < var19.size(); ++var41) {
                    var32 = var19.get(var41).toString();
                    var46 = var17.get(var41).toString();
                    var31 = var18.get(var41).toString();
                    var34 = var21.get(var41).toString();
                    var35 = var22.get(var41).toString();
                    var36 = var23.get(var41).toString();
                    var33 = Util.null2String(var8.getString(var32));

                    try {
                        Element var12 = this.getFieldElements(var36, var46, var32, var33, var31, var34, var35);
                        var14.addContent(var12);
                    } catch (Exception var43) {
                        this.writeLog("1fieldElement:" + var43);
                    }
                }

                var15.addContent(var14);
                if (var5.equalsIgnoreCase("y")) {
                    Element var13 = this.getDetailDataElements(var1, xmbm, var6, var16);
                    var15.addContent(var13);
                }
            }

            var7 = this.createXml(var9);
        } catch (Exception var44) {
            var44.printStackTrace();
            this.writeLog("getModeDataByID:" + var44);
            var7 = this.getErrorMsg(var44.getMessage());
        }

        return var7;
    }

    private void resolveFilesElement(int var1, int var2, int var3, String var4, List<Element> var5) throws Exception {
        if (null != var5) {
            for(int var6 = 0; var6 < var5.size(); ++var6) {
                Element var7 = (Element)var5.get(var6);
                String var8 = var7.getChildText("filedname");
                List var9 = var7.getChildren("file");
                String var10 = "";
                String var11 = "";
                String var12 = "";
                String var13 = "";
                String var14 = "";
                RecordSet var15 = new RecordSet();

                for(int var16 = 0; var16 < var9.size(); ++var16) {
                    var10 = ((Element)var9.get(var16)).getChildText("filename");
                    var11 = ((Element)var9.get(var16)).getChildText("filecontent");
                    var12 = ((Element)var9.get(var16)).getChildText("filecontenttype");
                    if (!"".equals(var10) && !"".equals(var11)) {
                        if (!var14.equals("")) {
                            var14 = var14 + ",";
                        }

                        int var17 = this.fileutil.buildFile(var1, var2, var11, var10);
                        if (var17 == 0) {
                            throw new ModeDataServiceException(ModeDataExceptionEnum.URLGET_DOCID_ZERO.toString());
                        }

                        var14 = var14 + var17;
                    }
                }

                var13 = "update " + var4 + " set " + var8 + "='" + var14 + "' where id=?";
                var15.executeUpdate(var13, new Object[]{var3});
                this.writeLog(var13);
            }
        }

    }

    private void saveModeDataOthers(int var1, int var2, int var3, String var4, String var5) throws Exception {
        if ("save".equals(var4)) {
            ModeRightInfo var6 = new ModeRightInfo();
            var6.rebuildModeDataShareByEdit(var3, var1, var2);
        } else if ("deleteAll".equals(var4)) {
            RecordSet var12 = new RecordSet();
            var12.executeUpdate("delete from modeDataShare_" + var1 + " where sourceid=?", new Object[]{var2});
        }

        String var13 = "127.0.0.1";
        String var7 = SystemEnv.getHtmlLabelName(502911, Util.threadVarLanguage());
        String var10 = "";
        if ("5".equals(var5)) {
            var7 = var7 + SystemEnv.getHtmlLabelName(91, Util.threadVarLanguage());
        } else if ("6".equals(var5)) {
            var7 = var7 + SystemEnv.getHtmlLabelName(86, Util.threadVarLanguage());
        } else {
            var7 = SystemEnv.getHtmlLabelName(103, Util.threadVarLanguage());
        }

        ModeViewLog var11 = new ModeViewLog();
        var11.resetParameter();
        var11.setClientaddress(var13);
        var11.setModeid(var1);
        var11.setOperatedesc(var7);
        var11.setOperatetype(var5);
        var11.setOperateuserid(var2);
        var11.setRelatedid(var2);
        var11.setRelatedname(var10);
        var11.setSysLogInfo();
    }

    public String getShareSql(int modeId, int userid) {
        RecordSet rs = new RecordSet();
        User user = new User();
        String sql = "";
        if (userid == 1) {
            sql = "select * from HrmResourceManager where id=" + userid;
        } else {
            sql = "select * from HrmResource where id=" + userid;
        }

        rs.executeSql(sql);
        rs.next();
        user.setUid(rs.getInt("id"));
        user.setLoginid(rs.getString("loginid"));
        user.setSeclevel(rs.getString("seclevel"));
        user.setUserDepartment(Util.getIntValue(rs.getString("departmentid"), 0));
        user.setUserSubCompany1(Util.getIntValue(rs.getString("subcompanyid1"), 0));
        user.setUserSubCompany2(Util.getIntValue(rs.getString("subcompanyid2"), 0));
        user.setUserSubCompany3(Util.getIntValue(rs.getString("subcompanyid3"), 0));
        user.setUserSubCompany4(Util.getIntValue(rs.getString("subcompanyid4"), 0));
        user.setManagerid(rs.getString("managerid"));
        user.setLogintype("1");
        ModeShareManager modeShareManager = new ModeShareManager();
        modeShareManager.setModeId(modeId);
        String var7 = modeShareManager.getShareDetailTableByUser("formmode", user);
        return var7;
    }

    private Element getHeaderElement(int var1, String var2, int var3) {
        Element var4 = new Element("header");
        Element var5 = new Element("userid");
        var5.addContent(var3 + "");
        var4.addContent(var5);
        var5 = new Element("modeid");
        var5.addContent(var1 + "");
        var4.addContent(var5);
        String var6 = "";
        if (!"".equals(var2)) {
            var6 = var2 + "";
        }

        var5 = new Element("xmbm");
        var5.addContent(var6);
        var4.addContent(var5);
        return var4;
    }

    private Element getSearchElements(String var1, String var2) {
        Element var3 = new Element("search");
        Element var4 = new Element("condition");
        var4.addContent(var1);
        var3.addContent(var4);
        if ("".equals(var2.trim())) {
            var2 = "n";
        }

        var4 = new Element("right");
        var4.addContent(var2);
        var3.addContent(var4);
        return var3;
    }

    private Element getHandlersElemens(String var1, String var2, String var3) {
        Element var4 = new Element("handlers");
        Element var5 = new Element("modedatacreater");
        var5.addContent(var1);
        var4.addContent(var5);
        var5 = new Element("modedatacreatershow");
        var5.addContent(this.fieldInfo.getResourceName(var1));
        var4.addContent(var5);
        var5 = new Element("modedatacreatedate");
        var5.addContent(var2);
        var4.addContent(var5);
        var5 = new Element("modedatacreatetime");
        var5.addContent(var3);
        var4.addContent(var5);
        return var4;
    }

    private Element getDetailDataElements(int var1, String xmbm, int var3, FieldInfo var4) throws Exception {
        RecordSet var5 = new RecordSet();
        List var6 = var4.getDtlabel_list();
        List var7 = var4.getDttype_list();
        List var8 = var4.getDtname_list();
        List var9 = var4.getDtvalue_list();
        List var10 = var4.getDttablename_list();
        List var11 = var4.getDthtmltype_list();
        List var12 = var4.getDtfielddbtype_list();
        List var13 = var4.getDtfieldid_list();
        String var14 = var4.getTablename();
        List var15 = var4.getDetailtable_list();
        String var21 = "";
        String var22 = "";
        String var23 = "";
        String var24 = "";
        String var25 = "";
        String var26 = "";
        String var27 = "";
        String var28 = "";
        String var29 = "";
        String var30 = "";
        Element var18 = new Element("detail");

        for(int var31 = 0; var31 < var15.size(); ++var31) {
            Element var19 = new Element("detailtable");
            var19.setAttribute("id", var31 + "");
            var29 = var15.get(var31).toString();
            var21 = "select d.* from " + var14 + " m," + var29 + " d where m.id=d.mainid and m.xmbm = " + xmbm;
            this.writeLog(var21);
            var5.executeSql(var21);

            while(var5.next()) {
                var30 = Util.null2String(var5.getString("id"));
                Element var20 = new Element("row");
                var20.setAttribute("id", var30);

                for(int var32 = 0; var32 < var8.size(); ++var32) {
                    if (var29.equals(var10.get(var32).toString())) {
                        var24 = var8.get(var32).toString();
                        var22 = var6.get(var32).toString();
                        var23 = var7.get(var32).toString();
                        var26 = var11.get(var32).toString();
                        var27 = var12.get(var32).toString();
                        var28 = var13.get(var32).toString();
                        var25 = Util.null2String(var5.getString(var24));

                        try {
                            Element var17 = this.getFieldElements(var28, var22, var24, var25, var23, var26, var27);
                            var20.addContent(var17);
                        } catch (Exception var34) {
                            this.writeLog("dtfieldElement:" + var34);
                        }
                    }
                }

                var19.addContent(var20);
            }

            var18.addContent(var19);
        }

        return var18;
    }

    private Element getFieldElements(String var1, String var2, String var3, String var4, String var5, String var6, String var7) {
        Element var8;
        if ("6".equals(var6.trim())) {
            Element var10 = new Element("files");
            var8 = new Element("filedname");
            var8.addContent(var3);
            var10.addContent(var8);
            var8 = new Element("filedlabel");
            var8.addContent(var2);
            var10.addContent(var8);
            var8 = new Element("fileddbtype");
            var8.addContent(var7);
            var10.addContent(var8);
            var8 = new Element("filedvalue");
            var8.addContent(var4);
            var10.addContent(var8);
            Element var11;
            if (!"".equals(var4.trim())) {
                RecordSet var14 = new RecordSet();
                String var13 = "";
                var13 = "update docdetail set docPublishType='2' where id in (" + var4 + ")";
                var14.executeSql(var13);
                this.writeLog("getFieldElements:" + var13);
                var13 = "select t2.imagefileid,t1.imagefilename,t1.imagefiletype,t1.filerealpath from ImageFile t1,docimagefile t2 where t1.imagefileid=t2.imagefileid and t2.docid in (" + var4 + ")";
                var14.executeSql(var13);

                while(var14.next()) {
                    var11 = new Element("file");
                    var8 = new Element("filename");
                    var8.addContent(var14.getString("imagefilename"));
                    var11.addContent(var8);
                    var8 = new Element("filecontent");
                    var8.addContent(this.oaip + "/weaver/weaver.file.FileDownload?fileid=" + var14.getString("imagefileid"));
                    var11.addContent(var8);
                    var8 = new Element("filecontenttype");
                    var8.addContent("http");
                    var11.addContent(var8);
                    var10.addContent(var11);
                }
            } else {
                var11 = new Element("file");
                var8 = new Element("filename");
                var11.addContent(var8);
                var8 = new Element("filecontent");
                var11.addContent(var8);
                var8 = new Element("filecontenttype");
                var11.addContent(var8);
                var10.addContent(var11);
            }

            var8 = new Element("fieldshowname");
            var8.addContent(var4);
            var10.addContent(var8);
            return var10;
        } else {
            String var12 = "";
            if ("5".equals(var6.trim()) && "1".equals(var5.trim())) {
                var12 = this.getSelectItem(var1.trim(), var4);
            } else if ("4".equals(var6.trim())) {
                var12 = var4;
            } else {
                var12 = this.fieldInfo.getFieldName(var4, Integer.parseInt(var5), var7);
            }

            if ("".equals(var12.trim())) {
                var12 = var4;
            }

            Element var9 = new Element("field");
            var8 = new Element("filedname");
            var8.addContent(var3);
            var9.addContent(var8);
            var8 = new Element("filedlabel");
            var8.addContent(var2.trim());
            var9.addContent(var8);
            var8 = new Element("fileddbtype");
            var8.addContent(var7);
            var9.addContent(var8);
            var8 = new Element("filedvalue");
            var8.addContent(var4);
            var9.addContent(var8);
            var8 = new Element("fieldshowname");
            var8.addContent(var12);
            var9.addContent(var8);
            return var9;
        }
    }

    private String getSelectItem(String var1, String var2) {
        String var3 = "";
        if (!var2.equals("")) {
            RecordSet var4 = new RecordSet();
            var4.executeSql("select selectname, selectvalue,isdefault,cancel from workflow_SelectItem where fieldid=" + var1 + " and selectvalue=" + var2);

            while(var4.next()) {
                var3 = Util.toHtmlMode(var4.getString("selectname"));
            }
        }

        return var3;
    }

    private String getErrorMsg(String var1) {
        Document var2 = new Document();
        Element var3 = new Element("ROOT");
        Element var4 = new Element("return");
        var2.setRootElement(var3);
        Element var5 = new Element("errormsg");
        var5.addContent(var1);
        var4.addContent(var5);
        var3.addContent(var4);
        return this.createXml(var2);
    }

    private String getReturnElements(String var1, int var2, String var3) {
        Document var4 = new Document();
        Element var5 = new Element("ROOT");
        Element var6 = new Element("return");
        var4.setRootElement(var5);
        Element var7 = new Element("xmbm");
        var7.addContent(var1 + "");
        var6.addContent(var7);
        var7 = new Element("returnnode");
        var7.addContent(var2 + "");
        var6.addContent(var7);
        var7 = new Element("returnmessage");
        var7.addContent(new CDATA(var3));
        var6.addContent(var7);
        var5.addContent(var6);
        return this.createXml(var4);
    }
    //select, allfield, var57, var35, var36, var37, var38, pageNo, pageSize, recordCount, vDataSource
    private String getPaginationSql(String select, String allfield, String var3, String var4, String var5, String var6, String var7, int pageNo, int pageSize, int var10, String var11) {
        String sql = "";
        RecordSet rs = new RecordSet();
        String DBType = "";
        if (!"".equals(var11)) {
            DBType = rs.getDBTypeByPoolName(var11);
        } else {
            DBType = rs.getDBType();
        }

        int count;
        int var16;
        if ("oracle".equals(DBType)) {
            count = pageNo * pageSize + 1;
            var16 = (pageNo - 1) * pageSize;
            sql = " select * from ( select my_table.*,rownum as my_rownum from ( select tableA.*,rownum as r from ( " + select + " " + allfield + " " + var3 + " " + var4 + " " + var5 + " ) tableA  ) my_table where rownum < " + count + " ) where my_rownum > " + var16;
        } else {
            count = pageSize * pageNo;
            var16 = pageSize;
            if (count > var10) {
                count = var10;
                var16 = var10 - pageSize * (pageNo - 1);
            }

            if (pageNo == 1) {
                sql = select + " top " + var16 + " " + allfield + " " + var3 + " " + var4 + " " + var5;
            } else {
                sql = " select top " + var16 + " * from ( select top " + var16 + " * from ( " + select + " top " + count + " " + allfield + " " + var3 + " " + var4 + " " + var5 + " ) tbltemp1 " + var6 + " ) tbltemp2 " + var7;
            }
        }

        return sql;
    }

    private String getCurrentDate() {
        SimpleDateFormat var1 = new SimpleDateFormat("yyyy-MM-dd");
        return var1.format(new Date());
    }

    private String getCurrentTime() {
        SimpleDateFormat var1 = new SimpleDateFormat("HH:mm:ss");
        return var1.format(new Date());
    }

    public String createXml(Document var1) {
        XMLOutputter var2 = null;
        Format var3 = Format.getCompactFormat();
        var3.setEncoding("UTF-8");
        var3.setIndent((String)null);
        var2 = new XMLOutputter(var3);
        return var2.outputString(var1);
    }

    public String getVDataSource(Map<String, Object> var1) throws ModeDataServiceException {
        String var2 = Util.null2String(var1.get("vdatasource"));
        String var3 = Util.null2String(var1.get("virtualformtype"));
        if ("1".equals(var3)) {
            throw new ModeDataServiceException(ModeDataExceptionEnum.NOT_SUPPORT_View_VIRFORM.toString());
        } else {
            if ("$ECOLOGY_SYS_LOCAL_POOLNAME".equals(var2)) {
                var2 = "local";
            }

            return var2;
        }
    }

    public int getFormIdByModeId(int var1) {
        RecordSet var2 = new RecordSet();
        int var3 = 0;
        String var4 = "select formid from modeinfo where id=?";
        var2.executeQuery(var4, new Object[]{var1});
        if (var2.next()) {
            var3 = var2.getInt("formid");
        }

        return var3;
    }

    private String removePrefixAndSuffix(String var1, String var2) {
        var1 = Util.null2String(var1).trim();
        if (var1.endsWith(var2)) {
            var1 = var1.substring(0, var1.length() - 1);
        }

        if (var1.startsWith(var2)) {
            var1 = var1.substring(1);
        }

        return var1;
    }

    public Map<String, String> recordSet2Map(RecordSet var1) {
        HashMap var2 = new HashMap();
        String[] var3 = var1.getColumnName();
        if (var3 == null) {
            return var2;
        } else {
            String[] var4 = var3;
            int var5 = var3.length;

            for(int var6 = 0; var6 < var5; ++var6) {
                String var7 = var4[var6];
                var2.put(var7.toLowerCase(), var1.getString(var7));
            }

            return var2;
        }
    }
}
