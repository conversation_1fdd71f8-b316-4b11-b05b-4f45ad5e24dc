package zhongzhi.services.Impl;

import com.engine.cube.biz.CodeBuilder;
import java.io.ByteArrayInputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.jdom.CDATA;
import org.jdom.Document;
import org.jdom.Element;
import org.jdom.input.SAXBuilder;
import org.jdom.output.Format;
import org.jdom.output.XMLOutputter;
import weaver.conn.RecordSet;
import weaver.formmode.dao.FormInfoDao;
import weaver.formmode.log.FormmodeLog;
import weaver.formmode.setup.ModeRightInfo;
import weaver.formmode.setup.ModeSetUtil;
import weaver.formmode.view.ModeShareManager;
import weaver.formmode.view.ModeViewLog;
import weaver.formmode.virtualform.VirtualFormHandler;
import weaver.formmode.webservices.FieldInfo;
import weaver.formmode.webservices.FileUtil;
import weaver.formmode.webservices.exception.ModeDataExceptionEnum;
import weaver.formmode.webservices.exception.ModeDataServiceException;
import weaver.general.Base64;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.security.util.SecurityMethodUtil;
import weaver.systeminfo.SystemEnv;
import weaver.workflow.form.FormManager;
import zhongzhi.services.BxmxService;


public class BxmxServiceImpl extends FormmodeLog implements BxmxService {
    private FileUtil fileutil = new FileUtil();

    public BxmxServiceImpl() {
    }

    public String saveModeData(String var1) {
        String var2 = "";
        boolean var3 = false;
        String var4 = "";
        String bh ="";
        boolean var6 = false;

        try {
            if (Util.null2String(var1).length() > 0 && var1.startsWith("Base64_")) {
                var1 = var1.replace("Base64_", "");
                var1 = new String(Base64.decode(var1.getBytes()));
                var6 = true;
            }

            RecordSet var7 = new RecordSet();
            SAXBuilder var8 = new SAXBuilder();
            Document var9 = null;

            try {
                SecurityMethodUtil.setSaxBuilderFeature(var8);
                var9 = var8.build(new ByteArrayInputStream(var1.getBytes("UTF-8")));
            } catch (Exception var52) {
                this.writeLog(var52);
                throw new ModeDataServiceException(ModeDataExceptionEnum.PARSE_XML_FAILURE.toStringWithExp(var52.getMessage()));
            }

            Element var10 = var9.getRootElement();
            Element var11 = var10.getChild("header");
            if (var11 == null) {
                throw new ModeDataServiceException(ModeDataExceptionEnum.HEADER_NODE_NOT_FOUND.toString());
            }

            Integer userid = Util.getIntValue(var11.getChildText("userid"));
            Integer var13 = Util.getIntValue(var11.getChildText("modeid"));
            bh = var11.getChildText("xmbh");
            String flowid=var11.getChildText("flowid");
            if (var13 == -1) {
                throw new ModeDataServiceException(ModeDataExceptionEnum.MODEID_NOT_FOUND.toString());
            }

            if (userid == -1) {
                throw new ModeDataServiceException(ModeDataExceptionEnum.USERID_NOT_FOUND.toString());
            }

            Element var14 = var10.getChild("search");
            if (var14 == null) {
                throw new ModeDataServiceException(ModeDataExceptionEnum.SEARCH_NODE_NOT_FOUND.toString());
            }

            Integer tableid = -1;
            String tablename = "";
            String var17 = "select w.tablename,w.id from modeinfo m,workflow_bill w where w.id=m.formid and m.id=?";
            var7.executeQuery(var17, new Object[]{var13});
            if (var7.next()) {
                tableid = Util.getIntValue(var7.getString("id"));
                tablename = Util.null2String(var7.getString("tablename"));
            }

            if (tableid == -1) {
                throw new ModeDataServiceException(ModeDataExceptionEnum.FORMID_NOT_FOUND.toString());
            }

            boolean isVirtualForm = VirtualFormHandler.isVirtualForm(tableid);
            String var19 = "";
            String gt2gysbh = "xmbh";
            if (isVirtualForm) {
                FormInfoDao var21 = new FormInfoDao();
                Map var22 = var21.getFormInfoById(tableid);
                var19 = this.getVDataSource(var22);
                gt2gysbh = Util.null2String(var22.get("vprimarykey"));
                tablename = VirtualFormHandler.getRealFromName(tablename);
            }

            if (tablename.length() > 33 && tablename.charAt(32) == '_') {
                throw new ModeDataServiceException(ModeDataExceptionEnum.NOT_SUPPORT_VIRFORM.toString());
            }

            String var54 = "";
            String var55 = "";
            String var23 = "";
            String var24 = "";
            String var25 = "";
            String var26 = "";
            String var27 = "";
            Element var28 = var10.getChild("data");
            if (var28 == null) {
                throw new ModeDataServiceException(ModeDataExceptionEnum.DATA_NODE_NOT_FOUND.toString());
            }

            var28 = var28.getChild("maintable");
            if (var28 == null) {
                throw new ModeDataServiceException(ModeDataExceptionEnum.MAINTABLE_NODE_NOT_FOUND.toString());
            }

            List fieldList = var28.getChildren("field");
            ArrayList var30 = new ArrayList();
            ArrayList var31 = new ArrayList();
            String procid="";
            String currentbh="";
            for(int var32 = 0; var32 < fieldList.size(); ++var32) {
                var25 = Util.null2String(((Element)fieldList.get(var32)).getChildText("filedname"));
                var26 = Util.null2String(((Element)fieldList.get(var32)).getChildText("filedlabel"));
                var27 = Util.null2String(((Element)fieldList.get(var32)).getChildText("filedvalue"));
                if("flowid".equals(var25)){
                    procid=var27;
                }
                if("xmbh".equals(var25)){
                    currentbh=var27;
                }
                var27 = Util.toHtmlForWorkflow(var27);
                var23 = Util.null2String(((Element)fieldList.get(var32)).getChildText("fileddbtype")).toLowerCase();
                var54 = var54 + var25;
                var54 = var54 + ",";
                if (!var23.startsWith("int") && !var23.startsWith("float") && !var23.startsWith("number") && !var23.startsWith("decimal")) {
                    if (var23.equals("clob") ||var23.equals("text") || var23.contains("char")) {
                        var55 = var55 + "'" + var27 + "'";
                    } else {
                        var30.add(var27);
                        var55 = var55 + "?";
                    }
                } else {
                    var55 = var55 + (!var27.equals("") ? var27 : "null") + "";
                }

                var55 = var55 + ",";
                if (!"".equals(var24)) {
                    var24 = var24 + ",";
                }

                if (!var23.startsWith("int") && !var23.startsWith("float") && !var23.startsWith("number") && !var23.startsWith("decimal")) {
                    if (var23.equals("clob") ||var23.equals("text") || var23.contains("char")) {
                        var24 = var24 + var25 + "='" + var27 + "'";
                    } else {
                        var31.add(var27);
                        var24 = var24 + "?";
                    }
                } else {
                    var24 = var24 + var25 + "=" + (!var27.equals("") ? var27 : "null") + "";
                }
            }

            Object[] var56 = new Object[var30.size()];

            int var33;
            for(var33 = 0; var33 < var30.size(); ++var33) {
                var56[var33] = var30.get(var33);
            }
            int billid=-1;
            //插入的时候到项目表中找到项目id插入到表中
            String projectId = getProjectIdByPrcode(currentbh);
            if (StringUtils.isNotBlank(flowid)) {
                if (!"".equals(var24)) {
                    Object[] var58 = new Object[var31.size() + 1];
                    var58[0] = bh;

                    for(int var59 = 1; var59 < var31.size(); ++var59) {
                        var58[var59] = var31.get(var59);
                    }
                    var17="select id from "+tablename+" where xmbh='"+bh+"' and flowid='"+flowid+"'";
                    //var7.executeQuery(var17, var58,flowid);
                    writeLog("selectsql======"+var17);
                    var7.execute(var17);
                    if(var7.next()){
                        billid=var7.getInt("id");
                    }
                    if (isVirtualForm) {
                        RecordSet var61 = new RecordSet();
                        var17 = "select * from " + tablename + " where " + gt2gysbh + "=?";
                        var61.executeQueryWithDatasource(var17, var19, new Object[]{bh});
                        Map var35 = this.recordSet2Map(var61);
                        this.writeLog("saveModeData.oldData:" + var35);
                        var17 = "update " + tablename + " set " + var24 + " where " + gt2gysbh + "=?";
                        var3 = var7.executeUpdateWithDatasource(var17, var19, var58);
                    } else {
                        var17 = "update " + tablename + " set " + var24+",xmmc='"+ projectId+ "' where  flowid='"+flowid+"'";
                       // var3 = var7.executeUpdate(var17, var58,flowid);
                        var3= var7.execute(var17);
                        writeLog("var17====="+var17+"====flowid=="+flowid);
                    }

                    this.writeLog(var17);
                    if (!var3) {
                        throw new ModeDataServiceException(ModeDataExceptionEnum.UPDATE_SQL_FAILURE.toStringWithExp(var17));
                    }
                }
            } else {
                if (isVirtualForm) {
                    var54 = this.removePrefixAndSuffix(var54, ",");
                    var55 = this.removePrefixAndSuffix(var55, ",");
                    var17 = "insert into " + tablename + " (" + var54 + ") values (" + var55 + ")";
                    this.writeLog(var17);
                    var3 = var7.executeUpdateWithDatasource(var17, var19, var56);
                } else {
                    //根据流程id查询流程是否已经存在
                    RecordSet rs = new RecordSet();
                    String sql="select * from "+tablename+" where flowid='"+procid+"'";
                    rs.execute(sql);
                    if(rs.next()){
                        var2 = this.getReturnElements(bh, -1, "流程编号已经存在！");
                        return var2;
                    }else{
                        var17 = "insert into " + tablename + " (" + var54 + "formmodeid,modedatacreater,modedatacreatertype,modedatacreatedate,modedatacreatetime,xmmc) values (" +
                                var55 + " " + var13 + "," + userid + ",0,'" + this.getCurrentDate() + "','" + this.getCurrentTime() + "','"+projectId+"')";
                        var3 = var7.executeUpdate(var17, var56);
                    }
                    this.writeLog(var17);
                }

                if (!var3) {
                    throw new ModeDataServiceException(ModeDataExceptionEnum.INSERT_SQL_FAILURE.toStringWithExp(var17));
                }

                var17 = "select max(id) as id from " + tablename;
                var7.executeQuery(var17, new Object[0]);
                billid = var7.next() ? Util.getIntValue(var7.getString("id")) : 0;
                CodeBuilder var57 = new CodeBuilder(var13);
                var57.getModeCodeStr(tableid, billid);
                ModeSetUtil var34 = new ModeSetUtil();
                var34.setModedatastatusValue(var7, var13, billid, tablename, 0);
            }

            this.saveModeDataOthers(var13, billid, userid, "save", "6");
            this.resolveFilesElement(var13, userid, billid, tablename, var28.getChildren("files"));
            boolean var60 = true;
            String var62 = "";
            String var63 = "";
            String var36 = "";
            String var37 = "";
            String var38 = "";
            String var39 = "";
            FormManager var40 = new FormManager();
            if (var10.getChild("data").getChild("detail") != null && var10.getChild("data").getChild("detail").getChildren("detailtable") != null) {
                List var41 = var10.getChild("data").getChild("detail").getChildren("detailtable");
                ArrayList var42 = new ArrayList();
                ArrayList var43 = new ArrayList();

                for(int var44 = 0; var44 < var41.size(); ++var44) {
                    var33 = Util.getIntValue(((Element)var41.get(var44)).getAttributeValue("id")) + 1;
                    var39 = var40.getDetailTablename(tableid, var33);
                    List var45 = ((Element)var41.get(var44)).getChildren("row");

                    for(int var46 = 0; var46 < var45.size(); ++var46) {
                        Element var47 = (Element)var45.get(var46);
                        var62 = var47.getAttributeValue("id");
                        var63 = var47.getAttributeValue("action");
                        var54 = "";
                        var55 = "";
                        var23 = "";
                        var24 = "";
                        List var48 = var47.getChildren("field");

                        for(int var49 = 0; var49 < var48.size(); ++var49) {
                            var36 = Util.null2String(((Element)var48.get(var49)).getChildText("filedname"));
                            var37 = Util.null2String(((Element)var48.get(var49)).getChildText("filedlabel"));
                            var38 = Util.null2String(((Element)var48.get(var49)).getChildText("filedvalue"));
                            var38 = Util.toHtmlForWorkflow(var38);
                            var23 = Util.null2String(((Element)var48.get(var49)).getChildText("fileddbtype")).toLowerCase();
                            if (!"".equals(var54)) {
                                var54 = var54 + ",";
                            }

                            var54 = var54 + var36;
                            if (!"".equals(var55)) {
                                var55 = var55 + ",";
                            }

                            if (!var23.startsWith("int") && !var23.startsWith("float") && !var23.startsWith("number") && !var23.startsWith("decimal")) {
                                if (!var23.equals("clob") && !var23.equals("text")) {
                                    var55 = var55 + "'" + var38 + "'";
                                } else {
                                    var42.add(var27);
                                    var55 = var55 + "?";
                                }
                            } else {
                                var55 = var55 + (!var38.equals("") ? var38 : "null");
                            }

                            if (!"".equals(var24)) {
                                var24 = var24 + ",";
                            }

                            if (!var23.startsWith("int") && !var23.startsWith("float") && !var23.startsWith("number") && !var23.startsWith("decimal")) {
                                if (!var23.equals("clob") && !var23.equals("text")) {
                                    var24 = var24 + var36 + "='" + var38 + "'";
                                } else {
                                    var43.add(var27);
                                    var24 = var24 + var36 + "=？";
                                }
                            } else {
                                var24 = var24 + var36 + "=" + (!var38.equals("") ? var38 : "null") + "";
                            }
                        }

                        boolean var64 = false;
                        Object[] var50;
                        int var51;
                        if ("add".equalsIgnoreCase(var63)) {
                            var50 = new Object[var42.size()];

                            for(var51 = 0; var51 < var42.size(); ++var51) {
                                var50[var51] = var42.get(var51);
                            }

                            var17 = "insert into " + var39 + " (mainid," + var54 + ") values (" + bh + "," + var55 + ")";
                            if (var54.equals("")) {
                                var17 = "insert into " + var39 + " (mainid) values (" + bh + ")";
                            }

                            var64 = var7.executeUpdate(var17, var50);
                        } else if (!"update".equalsIgnoreCase(var63)) {
                            if ("delete".equalsIgnoreCase(var63)) {
                                var17 = "delete from " + var39 + " where id= " + var62;
                                var64 = var7.executeUpdate(var17, new Object[0]);
                            }
                        } else {
                            var50 = new Object[var43.size()];

                            for(var51 = 0; var51 < var43.size(); ++var51) {
                                var50[var51] = var43.get(var51);
                            }

                            var17 = "update " + var39 + " set " + var24 + " where id=" + var62;
                            var64 = var7.executeUpdate(var17, var50);
                        }

                        this.writeLog(var17);
                        if (!var64) {
                            throw new ModeDataServiceException(ModeDataExceptionEnum.DETAILTABLE_OPERATE_FAILURE.toStringWithExp(var17));
                        }

                        if ("add".equalsIgnoreCase(var63)) {
                            var17 = "select max(id) as id from " + var39;
                            var7.executeSql(var17);
                            var7.next();
                            var62 = Util.null2String(var7.getString("id"));
                        }

                        this.resolveFilesElement(var13, userid, Integer.parseInt(var62), var39, var47.getChildren("files"));
                    }
                }
            }
        } catch (Exception var53) {
            var53.printStackTrace();
            this.writeLog("saveModeData:" + var53);
            var4 = var53.getMessage();
        }

        if ("".equals(var4)) {
            var2 = this.getReturnElements(bh, 0, SystemEnv.getHtmlLabelName(83885, Util.threadVarLanguage()));
        } else {
            var2 = this.getReturnElements(bh, -1, var4);
        }

        if (var6) {
            var2 = new String(Base64.encode(var2.getBytes()));
        }

        return var2;
    }



    private void resolveFilesElement(int var1, int var2, int var3, String var4, List<Element> var5) throws Exception {
        if (null != var5) {
            for(int var6 = 0; var6 < var5.size(); ++var6) {
                Element var7 = (Element)var5.get(var6);
                String var8 = var7.getChildText("filedname");
                List var9 = var7.getChildren("file");
                String var10 = "";
                String var11 = "";
                String var12 = "";
                String var13 = "";
                String var14 = "";
                RecordSet var15 = new RecordSet();

                for(int var16 = 0; var16 < var9.size(); ++var16) {
                    var10 = ((Element)var9.get(var16)).getChildText("filename");
                    var11 = ((Element)var9.get(var16)).getChildText("filecontent");
                    var12 = ((Element)var9.get(var16)).getChildText("filecontenttype");
                    if (!"".equals(var10) && !"".equals(var11)) {
                        if (!var14.equals("")) {
                            var14 = var14 + ",";
                        }

                        int var17 = this.fileutil.buildFile(var1, var2, var11, var10);
                        if (var17 == 0) {
                            throw new ModeDataServiceException(ModeDataExceptionEnum.URLGET_DOCID_ZERO.toString());
                        }

                        var14 = var14 + var17;
                    }
                }

                var13 = "update " + var4 + " set " + var8 + "='" + var14 + "' where id=?";
                var15.executeUpdate(var13, new Object[]{var3});
                this.writeLog(var13);
            }
        }

    }

    private void saveModeDataOthers(int var1, int var2, int var3, String var4, String var5) throws Exception {
        if ("save".equals(var4)) {
            ModeRightInfo var6 = new ModeRightInfo();
            var6.rebuildModeDataShareByEdit(var3, var1, var2);
        } else if ("deleteAll".equals(var4)) {
            RecordSet var12 = new RecordSet();
            var12.executeUpdate("delete from modeDataShare_" + var1 + " where sourceid=?", new Object[]{var2});
        }

        String var13 = "127.0.0.1";
        String var7 = SystemEnv.getHtmlLabelName(502911, Util.threadVarLanguage());
        String var10 = "";
        if ("5".equals(var5)) {
            var7 = var7 + SystemEnv.getHtmlLabelName(91, Util.threadVarLanguage());
        } else if ("6".equals(var5)) {
            var7 = var7 + SystemEnv.getHtmlLabelName(86, Util.threadVarLanguage());
        } else {
            var7 = SystemEnv.getHtmlLabelName(103, Util.threadVarLanguage());
        }

        ModeViewLog var11 = new ModeViewLog();
        var11.resetParameter();
        var11.setClientaddress(var13);
        var11.setModeid(var1);
        var11.setOperatedesc(var7);
        var11.setOperatetype(var5);
        var11.setOperateuserid(var2);
        var11.setRelatedid(var2);
        var11.setRelatedname(var10);
        var11.setSysLogInfo();
    }

    private String getSelectItem(String var1, String var2) {
        String var3 = "";
        if (!var2.equals("")) {
            RecordSet var4 = new RecordSet();
            var4.executeSql("select selectname, selectvalue,isdefault,cancel from workflow_SelectItem where fieldid=" + var1 + " and selectvalue=" + var2);

            while(var4.next()) {
                var3 = Util.toHtmlMode(var4.getString("selectname"));
            }
        }

        return var3;
    }

    private String getErrorMsg(String var1) {
        Document var2 = new Document();
        Element var3 = new Element("ROOT");
        Element var4 = new Element("return");
        var2.setRootElement(var3);
        Element var5 = new Element("errormsg");
        var5.addContent(var1);
        var4.addContent(var5);
        var3.addContent(var4);
        return this.createXml(var2);
    }

    private String getReturnElements(String var1, int var2, String var3) {
        Document var4 = new Document();
        Element var5 = new Element("ROOT");
        Element var6 = new Element("return");
        var4.setRootElement(var5);
        Element var7 = new Element("xmbh");
        var7.addContent(var1 + "");
        var6.addContent(var7);
        var7 = new Element("returnnode");
        var7.addContent(var2 + "");
        var6.addContent(var7);
        var7 = new Element("returnmessage");
        var7.addContent(new CDATA(var3));
        var6.addContent(var7);
        var5.addContent(var6);
        return this.createXml(var4);
    }


    private String getCurrentDate() {
        SimpleDateFormat var1 = new SimpleDateFormat("yyyy-MM-dd");
        return var1.format(new Date());
    }

    private String getCurrentTime() {
        SimpleDateFormat var1 = new SimpleDateFormat("HH:mm:ss");
        return var1.format(new Date());
    }

    public String createXml(Document var1) {
        XMLOutputter var2 = null;
        Format var3 = Format.getCompactFormat();
        var3.setEncoding("UTF-8");
        var3.setIndent((String)null);
        var2 = new XMLOutputter(var3);
        return var2.outputString(var1);
    }

    public String getVDataSource(Map<String, Object> var1) throws ModeDataServiceException {
        String var2 = Util.null2String(var1.get("vdatasource"));
        String var3 = Util.null2String(var1.get("virtualformtype"));
        if ("1".equals(var3)) {
            throw new ModeDataServiceException(ModeDataExceptionEnum.NOT_SUPPORT_View_VIRFORM.toString());
        } else {
            if ("$ECOLOGY_SYS_LOCAL_POOLNAME".equals(var2)) {
                var2 = "local";
            }

            return var2;
        }
    }

    public int getFormIdByModeId(int var1) {
        RecordSet var2 = new RecordSet();
        int var3 = 0;
        String var4 = "select formid from modeinfo where id=?";
        var2.executeQuery(var4, new Object[]{var1});
        if (var2.next()) {
            var3 = var2.getInt("formid");
        }

        return var3;
    }

    private String removePrefixAndSuffix(String var1, String var2) {
        var1 = Util.null2String(var1).trim();
        if (var1.endsWith(var2)) {
            var1 = var1.substring(0, var1.length() - 1);
        }

        if (var1.startsWith(var2)) {
            var1 = var1.substring(1);
        }

        return var1;
    }

    public Map<String, String> recordSet2Map(RecordSet var1) {
        HashMap var2 = new HashMap();
        String[] var3 = var1.getColumnName();
        if (var3 == null) {
            return var2;
        } else {
            String[] var4 = var3;
            int var5 = var3.length;

            for(int var6 = 0; var6 < var5; ++var6) {
                String var7 = var4[var6];
                var2.put(var7.toLowerCase(), var1.getString(var7));
            }

            return var2;
        }
    }
    private static String getProjectIdByPrcode(String procode){
        RecordSet rs = new RecordSet();
        String sql="select id from Prj_ProjectInfo where procode='"+procode+"'";
        rs.execute(sql);
        String id="";
        if(rs.next()){
             id = Util.null2String(rs.getString("id"));
        }
        return id;
    }
}
