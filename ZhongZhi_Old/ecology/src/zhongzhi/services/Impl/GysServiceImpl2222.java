package zhongzhi.services.Impl;

import com.api.cube.util.CubeCipherUitl;
import com.api.formmode.cache.ModeFormFieldEncryptComInfo;
import com.engine.cube.biz.CodeBuilder;
import com.engine.cube.cmd.restfulinterface.DeleteModeDataByPK;
import com.weaver.formmodel.util.DateHelper;
import org.jdom.CDATA;
import org.jdom.Document;
import org.jdom.Element;
import org.jdom.input.SAXBuilder;
import org.jdom.output.Format;
import org.jdom.output.XMLOutputter;
import weaver.conn.RecordSet;
import weaver.formmode.dao.FormInfoDao;
import weaver.formmode.data.ModeDataIdUpdate;
import weaver.formmode.log.FormmodeLog;
import weaver.formmode.setup.ModeRightInfo;
import weaver.formmode.setup.ModeSetUtil;
import weaver.formmode.view.ModeShareManager;
import weaver.formmode.view.ModeViewLog;
import weaver.formmode.virtualform.VirtualFormHandler;
import weaver.formmode.webservices.FieldInfo;
import weaver.formmode.webservices.FileUtil;
import weaver.formmode.webservices.exception.ModeDataExceptionEnum;
import weaver.formmode.webservices.exception.ModeDataServiceException;
import weaver.general.Base64;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.security.util.SecurityMethodUtil;
import weaver.systeminfo.SystemEnv;
import weaver.workflow.form.FormManager;
import zhongzhi.services.GysService;

import java.io.ByteArrayInputStream;
import java.text.SimpleDateFormat;
import java.util.*;

public class GysServiceImpl2222 extends FormmodeLog implements GysService {
    private static final String base64Flag = "Base64_";
    private FieldInfo fieldInfo = new FieldInfo();
    private FileUtil fileutil = new FileUtil();
    private String oaip = this.getPropValue("OAIpaddress", "Ipaddress");
    private String deljsonString = "";

    public GysServiceImpl2222(){}

    public String deleteModeDataById(int modeid, String bh, int userid, String var4) {
        String var5 = "";
        boolean var6 = false;

        try {
            RecordSet rs = new RecordSet();
            FieldInfo fieldInfo = new FieldInfo();
            fieldInfo.getMainTableName(modeid);
            fieldInfo.getDetailTableNameList(modeid);
            String tablename = fieldInfo.getTablename();
            int formId = this.getFormIdByModeId(modeid);
            boolean isVirtualForm = VirtualFormHandler.isVirtualForm(formId);
            //gt2gysbh
            //String var12 = "id";
            String gt2gysbh = "gt2gysbh";
            String var13 = "";
            if (isVirtualForm) {
                FormInfoDao var14 = new FormInfoDao();
                Map var15 = var14.getFormInfoById(formId);
                tablename = VirtualFormHandler.getRealFromName(tablename);
                gt2gysbh = Util.null2String(var15.get("vprimarykey"));
                var13 = this.getVDataSource(var15);
            }

            List var21 = fieldInfo.getDetailtable_list();


            String sql="select id from "+tablename+" where "+gt2gysbh+"=?";
            rs.executeQuery(sql, bh+"");
            //供应商信息只存在一条数据
            int billid=-1;
            if(rs.next()){
                billid=  rs.getInt("id");
            }
            DeleteModeDataByPK var22 = new DeleteModeDataByPK("", (Map)null, (User)null);
            var22.createDelData(modeid + "");
            this.deljsonString = var22.getDeleteJson(formId + "", tablename, billid+"" );
            String var16 = "";
            String var17 = "delete from " + tablename + " where " + gt2gysbh + "=?";
            if (isVirtualForm) {
                RecordSet var18 = new RecordSet();
                var18.executeQueryWithDatasource("select * from " + tablename + " where " + gt2gysbh + "=?", var13, new Object[]{bh});
                Map var19 = this.recordSet2Map(var18);
                this.writeLog("deleteModeDataById.oldData:" + var19);
                var6 = rs.executeUpdateWithDatasource(var17, var13, new Object[]{bh});
            } else {
                var6 = rs.executeUpdate(var17, new Object[]{bh});
            }
            //删除对应主表的所有明细表数据
            //中智环境现在没有明细表，不做处理
            for(int var23 = 0; var23 < var21.size(); ++var23) {
                var16 = var21.get(var23).toString();
                var17 = "delete from " + var16 + " where mainid=?";
                rs.executeUpdate(var17, new Object[]{bh});
            }


            this.saveModeDataOthers(modeid, billid, userid, "deleteAll", "5");
            if (var6) {
                var5 = this.getReturnElements(billid+"", 0, SystemEnv.getHtmlLabelName(83885, Util.threadVarLanguage()));
            } else {
                var5 = this.getReturnElements(billid+"", -1, SystemEnv.getHtmlLabelName(83912, Util.threadVarLanguage()));
            }
        } catch (Exception var20) {
            var20.printStackTrace();
            this.writeLog("deleteModeDataById:" + var20);
            var5 = this.getErrorMsg(var20.getMessage());
        }

        return var5;
    }

    public String saveModeData(String var1) {
        String var2 = "";
        boolean var3 = false;
        String var4 = "";
        String bh ="";
        boolean var6 = false;

        try {
            if (Util.null2String(var1).length() > 0 && var1.startsWith("Base64_")) {
                var1 = var1.replace("Base64_", "");
                var1 = new String(Base64.decode(var1.getBytes()));
                var6 = true;
            }

            RecordSet var7 = new RecordSet();
            SAXBuilder var8 = new SAXBuilder();
            Document var9 = null;

            try {
                SecurityMethodUtil.setSaxBuilderFeature(var8);
                var9 = var8.build(new ByteArrayInputStream(var1.getBytes("UTF-8")));
            } catch (Exception var52) {
                this.writeLog(var52);
                throw new ModeDataServiceException(ModeDataExceptionEnum.PARSE_XML_FAILURE.toStringWithExp(var52.getMessage()));
            }

            Element var10 = var9.getRootElement();
            Element var11 = var10.getChild("header");
            if (var11 == null) {
                throw new ModeDataServiceException(ModeDataExceptionEnum.HEADER_NODE_NOT_FOUND.toString());
            }

            Integer userid = Util.getIntValue(var11.getChildText("userid"));
            Integer modeid = Util.getIntValue(var11.getChildText("modeid"));
            //此处应该改成gt2gysbh
            bh = var11.getChildText("gt2gysbh");
            if (modeid == -1) {
                throw new ModeDataServiceException(ModeDataExceptionEnum.MODEID_NOT_FOUND.toString());
            }

            if (userid == -1) {
                throw new ModeDataServiceException(ModeDataExceptionEnum.USERID_NOT_FOUND.toString());
            }

            Element var14 = var10.getChild("search");
            if (var14 == null) {
                throw new ModeDataServiceException(ModeDataExceptionEnum.SEARCH_NODE_NOT_FOUND.toString());
            }

            Integer tableid = -1;
            String tablename = "";
            String var17 = "select w.tablename,w.id from modeinfo m,workflow_bill w where w.id=m.formid and m.id=?";
            var7.executeQuery(var17, new Object[]{modeid});
            if (var7.next()) {
                tableid = Util.getIntValue(var7.getString("id"));
                tablename = Util.null2String(var7.getString("tablename"));
            }

            if (tableid == -1) {
                throw new ModeDataServiceException(ModeDataExceptionEnum.FORMID_NOT_FOUND.toString());
            }

            boolean isVirtualForm = VirtualFormHandler.isVirtualForm(tableid);
            String vdatasource = "";
            //此处要改成编号 gt2gysbh
            String gt2gysbh = "gt2gysbh";

            if (isVirtualForm) {
                FormInfoDao var21 = new FormInfoDao();
                Map var22 = var21.getFormInfoById(tableid);
                vdatasource = this.getVDataSource(var22);
                gt2gysbh = Util.null2String(var22.get("vprimarykey"));
                tablename = VirtualFormHandler.getRealFromName(tablename);
            }

            if (tablename.length() > 33 && tablename.charAt(32) == '_') {
                throw new ModeDataServiceException(ModeDataExceptionEnum.NOT_SUPPORT_VIRFORM.toString());
            }

            String var54 = "";
            String var55 = "";
            String fileddbtype = "";
            String var24 = "";
            String filedname = "";
            String filedlabel = "";
            String filedvalue = "";
            Element var28 = var10.getChild("data");
            if (var28 == null) {
                throw new ModeDataServiceException(ModeDataExceptionEnum.DATA_NODE_NOT_FOUND.toString());
            }
            //获取要存入的主表字段信息
            var28 = var28.getChild("maintable");
            if (var28 == null) {
                throw new ModeDataServiceException(ModeDataExceptionEnum.MAINTABLE_NODE_NOT_FOUND.toString());
            }

            List maintablefield = var28.getChildren("field");
            ArrayList filedvaluelist = new ArrayList();
            ArrayList var31 = new ArrayList();

            for(int var32 = 0; var32 < maintablefield.size(); ++var32) {
                filedname = Util.null2String(((Element)maintablefield.get(var32)).getChildText("filedname"));
                filedlabel = Util.null2String(((Element)maintablefield.get(var32)).getChildText("filedlabel"));
                filedvalue = Util.null2String(((Element)maintablefield.get(var32)).getChildText("filedvalue"));
                filedvalue = Util.toHtmlForWorkflow(filedvalue);
                fileddbtype = Util.null2String(((Element)maintablefield.get(var32)).getChildText("fileddbtype")).toLowerCase();
                var54 = var54 + filedname;
                var54 = var54 + ",";
                filedvalue = this.isFirldNeedEncrypt(filedvalue, tablename, tableid, filedname);
                if (!fileddbtype.startsWith("int") && !fileddbtype.startsWith("float") && !fileddbtype.startsWith("number") && !fileddbtype.startsWith("decimal")) {
                    if (!fileddbtype.equals("clob") && !fileddbtype.equals("text")) {
                        var55 = var55 + "'" + filedvalue + "'";
                    } else {
                        filedvaluelist.add(filedvalue);
                        var55 = var55 + "?";
                    }
                } else {
                    var55 = var55 + (!filedvalue.equals("") ? filedvalue : "null") + "";
                }

                var55 = var55 + ",";
                if (!"".equals(var24)) {
                    var24 = var24 + ",";
                }

                if (!fileddbtype.startsWith("int") && !fileddbtype.startsWith("float") && !fileddbtype.startsWith("number") && !fileddbtype.startsWith("decimal")) {
                    if (!fileddbtype.equals("clob") && !fileddbtype.equals("text")) {
                        var24 = var24 + filedname + "='" + filedvalue + "'";
                    } else {
                        var31.add(filedvalue);
                        var24 = var24 + "?";
                    }
                } else {
                    var24 = var24 + filedname + "=" + (!filedvalue.equals("") ? filedvalue : "null") + "";
                }
                //var55拼接之后  mc,111,name,222
                //var24拼接之后  mc=111
            }

            Object[] filedvaluearray = new Object[filedvaluelist.size()];

            int var33;
            for(var33 = 0; var33 < filedvaluelist.size(); ++var33) {
                filedvaluearray[var33] = filedvaluelist.get(var33);
            }

            String var36;
            String var37;
            //更新
            int billid=-1;
            if (!"".equals(bh)) {
                if (!"".equals(var24)) {
                    Object[] var58 = new Object[var31.size() + 1];
                    var58[0] = bh;

                    for(int var59 = 1; var59 < var31.size(); ++var59) {
                        var58[var59] = var31.get(var59);
                    }
                    //如果是虚拟表单
                    if (isVirtualForm) {
                        RecordSet var61 = new RecordSet();
                        var17 = "select * from " + tablename + " where " + gt2gysbh + "=?";
                        var61.executeQueryWithDatasource(var17, vdatasource, new Object[]{bh});
                        Map var35 = this.recordSet2Map(var61);
                        this.writeLog("saveModeData.oldData:" + var35);
                        var17 = "update " + tablename + " set " + var24 + " where " + gt2gysbh + "=?";
                        var3 = var7.executeUpdateWithDatasource(var17, vdatasource, var58);
                    } else {
                        //id需要改成gt2gysbh
                        var17 = "update " + tablename + " set " + var24 + " where gt2gysbh=?";
                        var3 = var7.executeUpdate(var17, var58);
                        var17="select id from "+tablename+" where gt2gysbh=?";
                        var7.executeQuery(var17, var58);
                        if(var7.next()){
                            billid=var7.getInt("id");
                        }
                    }

                    if (var3) {
                        ModeDataIdUpdate var62 = new ModeDataIdUpdate();
                        RecordSet var63 = new RecordSet();
                        if (!VirtualFormHandler.isVirtualForm(tableid)) {
                            var36 = DateHelper.getCurrentDate() + " " + DateHelper.getCurrentTime();
                            var62.updateModifyInfo(tablename);
                            if (!"".equals(bh)) {
                                var37 = "update " + tablename + " set modedatamodifier=?,modedatamodifydatetime=? where gt2gysbh =?";
                                var63.executeUpdate(var37, new Object[]{userid, var36, bh});
                            }
                        }
                    }

                    this.writeLog(var17);
                    if (!var3) {
                        throw new ModeDataServiceException(ModeDataExceptionEnum.UPDATE_SQL_FAILURE.toStringWithExp(var17));
                    }
                }
            } else {
                //新增
                if (isVirtualForm) {
                    var54 = this.removePrefixAndSuffix(var54, ",");
                    var55 = this.removePrefixAndSuffix(var55, ",");
                    var17 = "insert into " + tablename + " (" + var54 + ") values (" + var55 + ")";
                    this.writeLog(var17);
                    var3 = var7.executeUpdateWithDatasource(var17, vdatasource, filedvaluearray);
                } else {
                    var17 = "insert into " + tablename + " (" + var54 + "formmodeid,modedatacreater,modedatacreatertype,modedatacreatedate,modedatacreatetime) values (" + var55 + " " + modeid + "," + userid + ",0,'" + this.getCurrentDate() + "','" + this.getCurrentTime() + "')";
                    var3 = var7.executeUpdate(var17, filedvaluearray);
                    this.writeLog(var17);
                }

                if (!var3) {
                    throw new ModeDataServiceException(ModeDataExceptionEnum.INSERT_SQL_FAILURE.toStringWithExp(var17));
                }

                var17 = "select max(id) as id from " + tablename ;
                var7.executeQuery(var17, new Object[0]);

                billid= var7.next() ? Util.getIntValue(var7.getString("id")) : 0;
                CodeBuilder var57 = new CodeBuilder(modeid);
                var57.getModeCodeStr(tableid, billid);
                ModeSetUtil var34 = new ModeSetUtil();
                var34.setModedatastatusValue(var7, modeid, billid, tablename, 0);
            }

            this.saveModeDataOthers(modeid, billid, userid, "save", "6");
            this.resolveFilesElement(modeid, userid, billid, tablename, var28.getChildren("files"));
            boolean var60 = true;
            String var64 = "";
            String var65 = "";
            var36 = "";
            var37 = "";
            String var38 = "";
            String var39 = "";
            FormManager var40 = new FormManager();
            if (var10.getChild("data").getChild("detail") != null && var10.getChild("data").getChild("detail").getChildren("detailtable") != null) {
                List var41 = var10.getChild("data").getChild("detail").getChildren("detailtable");
                ArrayList var42 = new ArrayList();
                ArrayList var43 = new ArrayList();

                for(int var44 = 0; var44 < var41.size(); ++var44) {
                    var33 = Util.getIntValue(((Element)var41.get(var44)).getAttributeValue("id")) + 1;
                    var39 = var40.getDetailTablename(tableid, var33);
                    List var45 = ((Element)var41.get(var44)).getChildren("row");

                    for(int var46 = 0; var46 < var45.size(); ++var46) {
                        Element var47 = (Element)var45.get(var46);
                        var64 = var47.getAttributeValue("id");
                        var65 = var47.getAttributeValue("action");
                        var54 = "";
                        var55 = "";
                        fileddbtype = "";
                        var24 = "";
                        List var48 = var47.getChildren("field");

                        for(int var49 = 0; var49 < var48.size(); ++var49) {
                            var36 = Util.null2String(((Element)var48.get(var49)).getChildText("filedname"));
                            var37 = Util.null2String(((Element)var48.get(var49)).getChildText("filedlabel"));
                            var38 = Util.null2String(((Element)var48.get(var49)).getChildText("filedvalue"));
                            var38 = Util.toHtmlForWorkflow(var38);
                            fileddbtype = Util.null2String(((Element)var48.get(var49)).getChildText("fileddbtype")).toLowerCase();
                            var38 = this.isFirldNeedEncrypt(var38, var39, var33, var36);
                            if (!"".equals(var54)) {
                                var54 = var54 + ",";
                            }

                            var54 = var54 + var36;
                            if (!"".equals(var55)) {
                                var55 = var55 + ",";
                            }

                            if (!fileddbtype.startsWith("int") && !fileddbtype.startsWith("float") && !fileddbtype.startsWith("number") && !fileddbtype.startsWith("decimal")) {
                                if (!fileddbtype.equals("clob") && !fileddbtype.equals("text")) {
                                    var55 = var55 + "'" + var38 + "'";
                                } else {
                                    var42.add(filedvalue);
                                    var55 = var55 + "?";
                                }
                            } else {
                                var55 = var55 + (!var38.equals("") ? var38 : "null");
                            }

                            if (!"".equals(var24)) {
                                var24 = var24 + ",";
                            }

                            if (!fileddbtype.startsWith("int") && !fileddbtype.startsWith("float") && !fileddbtype.startsWith("number") && !fileddbtype.startsWith("decimal")) {
                                if (!fileddbtype.equals("clob") && !fileddbtype.equals("text")) {
                                    var24 = var24 + var36 + "='" + var38 + "'";
                                } else {
                                    var43.add(filedvalue);
                                    var24 = var24 + var36 + "=？";
                                }
                            } else {
                                var24 = var24 + var36 + "=" + (!var38.equals("") ? var38 : "null") + "";
                            }
                        }

                        boolean var66 = false;
                        Object[] var50;
                        int var51;
                        if ("add".equalsIgnoreCase(var65)) {
                            var50 = new Object[var42.size()];

                            for(var51 = 0; var51 < var42.size(); ++var51) {
                                var50[var51] = var42.get(var51);
                            }

                            var17 = "insert into " + var39 + " (mainid," + var54 + ") values (" + bh + "," + var55 + ")";
                            if (var54.equals("")) {
                                var17 = "insert into " + var39 + " (mainid) values (" + bh + ")";
                            }

                            var66 = var7.executeUpdate(var17, var50);
                        } else if (!"update".equalsIgnoreCase(var65)) {
                            if ("delete".equalsIgnoreCase(var65)) {
                                var17 = "delete from " + var39 + " where id= " + var64;
                                var66 = var7.executeUpdate(var17, new Object[0]);
                            }
                        } else {
                            var50 = new Object[var43.size()];

                            for(var51 = 0; var51 < var43.size(); ++var51) {
                                var50[var51] = var43.get(var51);
                            }

                            var17 = "update " + var39 + " set " + var24 + " where id=" + var64;
                            var66 = var7.executeUpdate(var17, var50);
                        }

                        this.writeLog(var17);
                        if (!var66) {
                            throw new ModeDataServiceException(ModeDataExceptionEnum.DETAILTABLE_OPERATE_FAILURE.toStringWithExp(var17));
                        }

                        if ("add".equalsIgnoreCase(var65)) {
                            var17 = "select max(id) as id from " + var39;
                            var7.executeSql(var17);
                            var7.next();
                            var64 = Util.null2String(var7.getString("id"));
                        }

                        this.resolveFilesElement(modeid, userid, Integer.parseInt(var64), var39, var47.getChildren("files"));
                    }
                }
            }
        } catch (Exception var53) {
            var53.printStackTrace();
            this.writeLog("saveModeData:" + var53);
            var4 = var53.getMessage();
        }

        if ("".equals(var4)) {
            var2 = this.getReturnElements(bh, 0, SystemEnv.getHtmlLabelName(83885, Util.threadVarLanguage()));
        } else {
            var2 = this.getReturnElements(bh, -1, var4);
        }

        if (var6) {
            var2 = new String(Base64.encode(var2.getBytes()));
        }

        return var2;
    }

    public int getAllModeDataCount(int modeid, int userid, String conditions, String var4) {
        RecordSet rs = new RecordSet();
        FieldInfo fieldInfo = new FieldInfo();
        fieldInfo.getMainTableName(modeid);
        String tablename = fieldInfo.getTablename();
        int var8 = this.getFormIdByModeId(modeid);
        boolean isVirtualForm = VirtualFormHandler.isVirtualForm(var8);
        if (isVirtualForm) {
            tablename = VirtualFormHandler.getRealFromName(tablename);
        }

        tablename = tablename + " ";
        String var10 = " where 1=1 ";
        if ("y".equalsIgnoreCase(var4.trim()) && !isVirtualForm) {
            tablename = tablename + "t1," + this.getShareSql(modeid, userid) + " t2 ";
            var10 = var10 + " and t1.id = t2.sourceid ";
        }

        if (!"".equals(conditions.trim())) {
            conditions = " and " + conditions;
        }

        var10 = var10 + conditions;
        String var11 = "select id from " + tablename + var10;
        this.writeLog(var11);
        if (isVirtualForm) {
            try {
                FormInfoDao var12 = new FormInfoDao();
                Map var13 = var12.getFormInfoById(var8);
                String var14 = this.getVDataSource(var13);
                rs.executeSqlWithDataSource(var11, var14);
            } catch (Exception var15) {
                this.writeLog(var15);
            }
        } else {
            rs.executeSql(var11);
        }

        return rs.getCounts();
    }

    public String getAllModeDataList(int var1, int var2, int var3, int var4, int var5, String var6, String var7, String var8) {
        byte var9 = 7;
        String var10 = "";

        try {
            Document var11 = new Document();
            Element var12 = new Element("ROOT");
            var11.setRootElement(var12);
            Element var13 = this.getHeaderElement(var1, "", var5);
            var12.addContent(var13);
            var12.addContent(this.getSearchElements(var6, var7));
            RecordSet var18 = new RecordSet();
            FieldInfo var19 = new FieldInfo();
            var19.getManTableField(var1, var9);
            var19.getDetailTableField(var1, var9);
            var19.getMainTableName(var1);
            var19.getDetailTableNameList(var1);
            List var20 = var19.getLabel_list();
            List var21 = var19.getType_list();
            List var22 = var19.getName_list();
            List var23 = var19.getValue_list();
            List var24 = var19.getHtmltype_list();
            List var25 = var19.getFielddbtype_list();
            List var26 = var19.getFieldid_list();
            String var27 = var19.getTablename();
            int var28 = this.getFormIdByModeId(var1);
            boolean var29 = VirtualFormHandler.isVirtualForm(var28);
            String var30 = "";
            String var31 = "id";
            String var32 = " id,modedatacreater,modedatacreatedate,modedatacreatetime ";
            if (var29) {
                FormInfoDao var33 = new FormInfoDao();
                Map var34 = var33.getFormInfoById(var28);
                var30 = this.getVDataSource(var34);
                var31 = Util.null2String(var34.get("vprimarykey"));
                var27 = VirtualFormHandler.getRealFromName(var27);
                var32 = "";
            }

            if (var2 < 1) {
                var2 = 1;
            }

            String var55 = " select ";

            for(int var56 = 0; var56 < var22.size(); ++var56) {
                var32 = var32 + ",";
                var32 = var32 + var22.get(var56).toString();
            }

            var32 = var32.trim();
            if (var32.startsWith(",")) {
                var32 = var32.substring(1);
            }

            if (var4 <= 0) {
                var4 = this.getAllModeDataCount(var1, var5, var6, var7);
            }

            String var57 = " from " + var27 + " ";
            String var35 = " where 1=1 ";
            if ("y".equalsIgnoreCase(var7.trim()) && !var29) {
                var57 = var57 + "t1," + this.getShareSql(var1, var5) + " t2 ";
                var35 = var35 + " and t1.id = t2.sourceid ";
            }

            if (!"".equals(var6.trim())) {
                var6 = " and " + var6;
            }

            var35 = var35 + var6;
            String var36 = " order by " + var31;
            String var37 = " order by " + var31 + " desc";
            String var38 = " order by " + var31 + " asc";
            String var39 = "";
            String var40 = "";
            String var41 = "";
            String var42 = "";
            String var43 = "";
            String var44 = "";
            String var45 = "";
            String var46 = "";
            String var47 = "";
            String var48 = "";
            String var49 = "";
            String var50 = this.getPaginationSql(var55, var32, var57, var35, var36, var37, var38, var2, var3, var4, var30);
            this.writeLog(var50);
            if (var29) {
                var18.executeSqlWithDataSource(var50, var30);
            } else {
                var18.executeSql(var50);
            }

            Element var14;
            for(; var18.next(); var12.addContent(var14)) {
                var46 = Util.null2String(var18.getString("id"));
                var47 = Util.null2String(var18.getString("modedatacreater"));
                var48 = Util.null2String(var18.getString("modedatacreatedate"));
                var49 = Util.null2String(var18.getString("modedatacreatetime"));
                var14 = new Element("data");
                var14.setAttribute("id", var46);
                var14.addContent(this.getHandlersElemens(var47, var48, var49));
                Element var16 = new Element("maintable");

                for(int var51 = 0; var51 < var22.size(); ++var51) {
                    var41 = var22.get(var51).toString();
                    var39 = var20.get(var51).toString();
                    var40 = var21.get(var51).toString();
                    var43 = var24.get(var51).toString();
                    var44 = var25.get(var51).toString();
                    var45 = var26.get(var51).toString();
                    var42 = Util.null2String(var18.getString(var41));

                    try {
                        Element var15 = this.getFieldElements(var45, var39, var41, var42, var40, var43, var44);
                        var16.addContent(var15);
                    } catch (Exception var53) {
                        this.writeLog("fieldElement:" + var53);
                    }
                }

                var14.addContent(var16);
                if (var8.equalsIgnoreCase("y")) {
                    Element var17 = this.getDetailDataElements(var1, var46, var9, var19);
                    var14.addContent(var17);
                }
            }

            var10 = this.createXml(var11);
        } catch (Exception var54) {
            var54.printStackTrace();
            this.writeLog("getAllModeDataList:" + var54);
            var10 = this.getErrorMsg(var54.getMessage());
        }

        return var10;
    }

    public String getModeDataByID(int modeid, String gysbh, int userid, String right, String isReturnDetail) {
        byte var6 = 7;
        String var7 = "";

        try {
            RecordSet var8 = new RecordSet();
            Document var9 = new Document();
            Element root = new Element("ROOT");
            var9.setRootElement(root);
            Element headerElement = this.getHeaderElement(modeid, gysbh, userid);
            root.addContent(headerElement);
            root.addContent(this.getSearchElements("", right));
            FieldInfo fieldInfo = new FieldInfo();
            fieldInfo.getManTableField(modeid, var6);
            fieldInfo.getDetailTableField(modeid, var6);
            fieldInfo.getMainTableName(modeid);
            fieldInfo.getDetailTableNameList(modeid);
            List label_list = fieldInfo.getLabel_list();
            List type_list = fieldInfo.getType_list();
            List name_list = fieldInfo.getName_list();
            List value_list = fieldInfo.getValue_list();
            List htmltype_list = fieldInfo.getHtmltype_list();
            List fielddbtype_list = fieldInfo.getFielddbtype_list();
            List fieldid_list = fieldInfo.getFieldid_list();
            String tablename = fieldInfo.getTablename();
            int formid = this.getFormIdByModeId(modeid);
            boolean isVirtualForm = VirtualFormHandler.isVirtualForm(formid);
            //此处应改为gt2gysbh
            //String bh = "id";
            String bh = "gt2gysbh";
            String var28 = "";
            if (isVirtualForm) {
                FormInfoDao formInfoDao = new FormInfoDao();
                Map formInfo = formInfoDao.getFormInfoById(formid);
                var28 = this.getVDataSource(formInfo);
                bh = Util.null2String(formInfo.get("vprimarykey"));
                tablename = VirtualFormHandler.getRealFromName(tablename);
            }

            String var45 = "";
            String label = "";
            String var31 = "";
            String name = "";
            String var33 = "";
            String htmltype = "";
            String var35 = "";
            String fieldid = "";
            String var37 = "";
            String modedatacreater = "";
            String modedatacreatedate = "";
            String modedatacreatetime = "";
            var45 = "select * from " + tablename + " where " + bh + " = ?";
            if (isVirtualForm) {
                var8.executeQueryWithDatasource(var45, var28, new Object[]{gysbh});
            } else {
                var8.executeQuery(var45, new Object[]{gysbh});
            }

            this.writeLog(var45);

            Element var15;
            for(; var8.next(); root.addContent(var15)) {
                var37 = Util.null2String(var8.getString("id"));
                modedatacreater = Util.null2String(var8.getString("modedatacreater"));
                modedatacreatedate = Util.null2String(var8.getString("modedatacreatedate"));
                modedatacreatetime = Util.null2String(var8.getString("modedatacreatetime"));
                var15 = new Element("data");
                var15.setAttribute("id", var37);
                var15.addContent(this.getHandlersElemens(modedatacreater, modedatacreatedate, modedatacreatetime));
                Element var14 = new Element("maintable");

                for(int var41 = 0; var41 < name_list.size(); ++var41) {
                    name = name_list.get(var41).toString();
                    label = label_list.get(var41).toString();
                    var31 = type_list.get(var41).toString();
                    htmltype = htmltype_list.get(var41).toString();
                    var35 = fielddbtype_list.get(var41).toString();
                    fieldid = fieldid_list.get(var41).toString();
                    var33 = Util.null2String(var8.getString(name));

                    try {
                        Element var12 = this.getFieldElements(fieldid, label, name, var33, var31, htmltype, var35);
                        var14.addContent(var12);
                    } catch (Exception var43) {
                        this.writeLog("1fieldElement:" + var43);
                    }
                }

                var15.addContent(var14);
                if (isReturnDetail.equalsIgnoreCase("y")) {
                    Element var13 = this.getDetailDataElements(modeid, gysbh, var6, fieldInfo);
                    var15.addContent(var13);
                }
            }

            var7 = this.createXml(var9);
        } catch (Exception var44) {
            var44.printStackTrace();
            this.writeLog("getModeDataByID:" + var44);
            var7 = this.getErrorMsg(var44.getMessage());
        }

        return var7;
    }

    private void resolveFilesElement(int var1, int var2, int var3, String var4, List<Element> var5) throws Exception {
        if (null != var5) {
            for(int var6 = 0; var6 < var5.size(); ++var6) {
                Element var7 = (Element)var5.get(var6);
                String var8 = var7.getChildText("filedname");
                List var9 = var7.getChildren("file");
                String var10 = "";
                String var11 = "";
                String var12 = "";
                String var13 = "";
                String var14 = "";
                RecordSet var15 = new RecordSet();

                for(int var16 = 0; var16 < var9.size(); ++var16) {
                    var10 = ((Element)var9.get(var16)).getChildText("filename");
                    var11 = ((Element)var9.get(var16)).getChildText("filecontent");
                    var12 = ((Element)var9.get(var16)).getChildText("filecontenttype");
                    if (!"".equals(var10) && !"".equals(var11)) {
                        if (!var14.equals("")) {
                            var14 = var14 + ",";
                        }

                        int var17 = this.fileutil.buildFile(var1, var2, var11, var10);
                        if (var17 == 0) {
                            throw new ModeDataServiceException(ModeDataExceptionEnum.URLGET_DOCID_ZERO.toString());
                        }

                        var14 = var14 + var17;
                    }
                }

                var13 = "update " + var4 + " set " + var8 + "='" + var14 + "' where id=?";
                var15.executeUpdate(var13, new Object[]{var3});
                this.writeLog(var13);
            }
        }

    }

    private String isFirldNeedEncrypt(String var1, String var2, int var3, String var4) throws Exception {
        boolean var5 = VirtualFormHandler.isVirtualForm(var3);
        if (var5) {
            var2 = VirtualFormHandler.getRealFromName(var2);
        }

        RecordSet var6 = new RecordSet();
        String var7 = "select t2.id,t2.fieldhtmltype,t2.fielddbtype,t2.type,t1.isencrypt from ModeFormFieldEncrypt t1 , workflow_billfield t2 where t1.fieldid=t2.id and t2.billid= '" + var3 + "' and t2.fieldname = '" + var4 + "'";
        var6.executeQuery(var7, new Object[0]);
        if (var6.next()) {
            String var8 = Util.null2String(var6.getString("id"));
            String var9 = Util.null2String(var6.getString("fieldhtmltype"));
            String var10 = Util.null2String(var6.getString("type"));
            String var11;
            if ("1".equals(var9) && "1".equals(var10)) {
                var11 = (new ModeFormFieldEncryptComInfo()).getIsencrypt(var8);
                if ("1".equals(var11)) {
                    var1 = CubeCipherUitl.encrypt(var1);
                }
            } else if ("3".equals(var9) && ("161".equals(var10) || "162".equals(var10))) {
                var11 = Util.null2String(var6.getString("fielddbtype"));
                var1 = CubeCipherUitl.getBrowserEncryptValue(var11, var1, var10);
            }
        }

        return var1;
    }

    private void saveModeDataOthers(int var1, int var2, int var3, String var4, String var5) throws Exception {
        if ("save".equals(var4)) {
            ModeRightInfo var6 = new ModeRightInfo();
            var6.rebuildModeDataShareByEdit(var3, var1, var2);
        } else if ("deleteAll".equals(var4)) {
            RecordSet var12 = new RecordSet();
            var12.executeUpdate("delete from modeDataShare_" + var1 + " where sourceid=?", new Object[]{var2});
        }

        String var13 = "127.0.0.1";
        String var7 = SystemEnv.getHtmlLabelName(502911, Util.threadVarLanguage());
        String var10 = "";
        if ("5".equals(var5)) {
            var7 = var7 + SystemEnv.getHtmlLabelName(91, Util.threadVarLanguage());
        } else if ("6".equals(var5)) {
            var7 = var7 + SystemEnv.getHtmlLabelName(86, Util.threadVarLanguage());
        } else {
            var7 = SystemEnv.getHtmlLabelName(103, Util.threadVarLanguage());
        }

        ModeViewLog var11 = new ModeViewLog();
        var11.resetParameter();
        var11.setClientaddress(var13);
        var11.setModeid(var1);
        var11.setOperatedesc(var7);
        var11.setOperatetype(var5);
        var11.setOperateuserid(var3);
        var11.setRelatedid(var2);
        var11.setRelatedname(var10);
        if (this.deljsonString.length() > 0) {
            var11.setDeldata(this.deljsonString);
        }

        var11.setSysLogInfo();
    }

    public String getShareSql(int var1, int var2) {
        RecordSet var3 = new RecordSet();
        User var4 = new User();
        String var5 = "";
        if (var2 == 1) {
            var5 = "select * from HrmResourceManager where id=" + var2;
        } else {
            var5 = "select * from HrmResource where id=" + var2;
        }

        var3.executeSql(var5);
        var3.next();
        var4.setUid(var3.getInt("id"));
        var4.setLoginid(var3.getString("loginid"));
        var4.setSeclevel(var3.getString("seclevel"));
        var4.setUserDepartment(Util.getIntValue(var3.getString("departmentid"), 0));
        var4.setUserSubCompany1(Util.getIntValue(var3.getString("subcompanyid1"), 0));
        var4.setUserSubCompany2(Util.getIntValue(var3.getString("subcompanyid2"), 0));
        var4.setUserSubCompany3(Util.getIntValue(var3.getString("subcompanyid3"), 0));
        var4.setUserSubCompany4(Util.getIntValue(var3.getString("subcompanyid4"), 0));
        var4.setManagerid(var3.getString("managerid"));
        var4.setLogintype("1");
        ModeShareManager var6 = new ModeShareManager();
        var6.setModeId(var1);
        String var7 = var6.getShareDetailTableByUser("formmode", var4);
        return var7;
    }

    private Element getHeaderElement(int modeid, String gysbh, int userid) {
        Element header = new Element("header");
        Element user = new Element("userid");
        user.addContent(userid + "");
        header.addContent(user);
        user = new Element("modeid");
        user.addContent(modeid + "");
        header.addContent(user);
        String var6 = "";
        if (!"".equals(gysbh) ) {
            var6 = gysbh + "";
        }
        //这里换成gt2gysbh
        //user = new Element("id");
        user = new Element("gt2gysbh");
        user.addContent(var6);
        header.addContent(user);
        return header;
    }

    private Element getSearchElements(String var1, String var2) {
        Element var3 = new Element("search");
        Element var4 = new Element("condition");
        var4.addContent(var1);
        var3.addContent(var4);
        if ("".equals(var2.trim())) {
            var2 = "n";
        }

        var4 = new Element("right");
        var4.addContent(var2);
        var3.addContent(var4);
        return var3;
    }

    private Element getHandlersElemens(String var1, String var2, String var3) {
        Element var4 = new Element("handlers");
        Element var5 = new Element("modedatacreater");
        var5.addContent(var1);
        var4.addContent(var5);
        var5 = new Element("modedatacreatershow");
        var5.addContent(this.fieldInfo.getResourceName(var1));
        var4.addContent(var5);
        var5 = new Element("modedatacreatedate");
        var5.addContent(var2);
        var4.addContent(var5);
        var5 = new Element("modedatacreatetime");
        var5.addContent(var3);
        var4.addContent(var5);
        return var4;
    }

    private Element getDetailDataElements(int var1, String gysbh, int var3, FieldInfo var4) throws Exception {
        RecordSet var5 = new RecordSet();
        List var6 = var4.getDtlabel_list();
        List var7 = var4.getDttype_list();
        List var8 = var4.getDtname_list();
        List var9 = var4.getDtvalue_list();
        List var10 = var4.getDttablename_list();
        List var11 = var4.getDthtmltype_list();
        List var12 = var4.getDtfielddbtype_list();
        List var13 = var4.getDtfieldid_list();
        String var14 = var4.getTablename();
        List var15 = var4.getDetailtable_list();
        String var21 = "";
        String var22 = "";
        String var23 = "";
        String var24 = "";
        String var25 = "";
        String var26 = "";
        String var27 = "";
        String var28 = "";
        String var29 = "";
        String var30 = "";
        Element var18 = new Element("detail");

        for(int var31 = 0; var31 < var15.size(); ++var31) {
            Element var19 = new Element("detailtable");
            var19.setAttribute("id", var31 + "");
            var29 = var15.get(var31).toString();
            var21 = "select d.* from " + var14 + " m," + var29 + " d where m.id=d.mainid and m.gt2gysbh = " + gysbh;
            this.writeLog(var21);
            var5.executeSql(var21);

            while(var5.next()) {
                var30 = Util.null2String(var5.getString("id"));
                Element var20 = new Element("row");
                var20.setAttribute("id", var30);

                for(int var32 = 0; var32 < var8.size(); ++var32) {
                    if (var29.equals(var10.get(var32).toString())) {
                        var24 = var8.get(var32).toString();
                        var22 = var6.get(var32).toString();
                        var23 = var7.get(var32).toString();
                        var26 = var11.get(var32).toString();
                        var27 = var12.get(var32).toString();
                        var28 = var13.get(var32).toString();
                        var25 = Util.null2String(var5.getString(var24));

                        try {
                            Element var17 = this.getFieldElements(var28, var22, var24, var25, var23, var26, var27);
                            var20.addContent(var17);
                        } catch (Exception var34) {
                            this.writeLog("dtfieldElement:" + var34);
                        }
                    }
                }

                var19.addContent(var20);
            }

            var18.addContent(var19);
        }

        return var18;
    }

    private Element getFieldElements(String fieldid, String var2, String var3, String var4, String var5, String var6, String var7) {
        var4 = CubeCipherUitl.decrypt(var4);
        Element var8;
        if ("6".equals(var6.trim())) {
            Element var10 = new Element("files");
            var8 = new Element("filedname");
            var8.addContent(var3);
            var10.addContent(var8);
            var8 = new Element("filedlabel");
            var8.addContent(var2);
            var10.addContent(var8);
            var8 = new Element("fileddbtype");
            var8.addContent(var7);
            var10.addContent(var8);
            var8 = new Element("filedvalue");
            var8.addContent(var4);
            var10.addContent(var8);
            Element var11;
            if (!"".equals(var4.trim())) {
                RecordSet var14 = new RecordSet();
                String var13 = "";
                var13 = "update docdetail set docPublishType='2' where id in (" + var4 + ")";
                var14.executeSql(var13);
                this.writeLog("getFieldElements:" + var13);
                var13 = "select t2.imagefileid,t1.imagefilename,t1.imagefiletype,t1.filerealpath from ImageFile t1,docimagefile t2 where t1.imagefileid=t2.imagefileid and t2.docid in (" + var4 + ")";
                var14.executeSql(var13);

                while(var14.next()) {
                    var11 = new Element("file");
                    var8 = new Element("filename");
                    var8.addContent(var14.getString("imagefilename"));
                    var11.addContent(var8);
                    var8 = new Element("filecontent");
                    var8.addContent(this.oaip + "/weaver/weaver.file.FileDownload?fileid=" + var14.getString("imagefileid"));
                    var11.addContent(var8);
                    var8 = new Element("filecontenttype");
                    var8.addContent("http");
                    var11.addContent(var8);
                    var10.addContent(var11);
                }
            } else {
                var11 = new Element("file");
                var8 = new Element("filename");
                var11.addContent(var8);
                var8 = new Element("filecontent");
                var11.addContent(var8);
                var8 = new Element("filecontenttype");
                var11.addContent(var8);
                var10.addContent(var11);
            }

            var8 = new Element("fieldshowname");
            var8.addContent(var4);
            var10.addContent(var8);
            return var10;
        } else {
            String var12 = "";
            if ("5".equals(var6.trim()) && "1".equals(var5.trim())) {
                var12 = this.getSelectItem(fieldid.trim(), var4);
            } else if ("4".equals(var6.trim())) {
                var12 = var4;
            } else {
                var12 = this.fieldInfo.getFieldName(var4, Integer.parseInt(var5), var7);
            }

            if ("".equals(var12.trim())) {
                var12 = var4;
            }

            Element var9 = new Element("field");
            var8 = new Element("filedname");
            var8.addContent(var3);
            var9.addContent(var8);
            var8 = new Element("filedlabel");
            var8.addContent(var2.trim());
            var9.addContent(var8);
            var8 = new Element("fileddbtype");
            var8.addContent(var7);
            var9.addContent(var8);
            var8 = new Element("filedvalue");
            var8.addContent(var4);
            var9.addContent(var8);
            var8 = new Element("fieldshowname");
            var8.addContent(var12);
            var9.addContent(var8);
            return var9;
        }
    }

    private String getSelectItem(String var1, String var2) {
        String var3 = "";
        if (!var2.equals("")) {
            RecordSet var4 = new RecordSet();
            var4.executeSql("select selectname, selectvalue,isdefault,cancel from workflow_SelectItem where fieldid=" + var1 + " and selectvalue=" + var2);

            while(var4.next()) {
                var3 = Util.toHtmlMode(var4.getString("selectname"));
            }
        }

        return var3;
    }

    private String getErrorMsg(String var1) {
        Document var2 = new Document();
        Element var3 = new Element("ROOT");
        Element var4 = new Element("return");
        var2.setRootElement(var3);
        Element var5 = new Element("errormsg");
        var5.addContent(var1);
        var4.addContent(var5);
        var3.addContent(var4);
        return this.createXml(var2);
    }

    private String getReturnElements(String var1, int var2, String var3) {
        Document var4 = new Document();
        Element var5 = new Element("ROOT");
        Element var6 = new Element("return");
        var4.setRootElement(var5);
        Element var7 = new Element("gt2gysbh");
        var7.addContent(var1 + "");
        var6.addContent(var7);
        var7 = new Element("returnnode");
        var7.addContent(var2 + "");
        var6.addContent(var7);
        var7 = new Element("returnmessage");
        var7.addContent(new CDATA(var3));
        var6.addContent(var7);
        var5.addContent(var6);
        return this.createXml(var4);
    }

    private String getPaginationSql(String var1, String var2, String var3, String var4, String var5, String var6, String var7, int var8, int var9, int var10, String var11) {
        String var12 = "";
        RecordSet var13 = new RecordSet();
        String var14 = "";
        if (!"".equals(var11)) {
            var14 = var13.getDBTypeByPoolName(var11);
        } else {
            var14 = var13.getDBType();
        }

        int var15;
        int var16;
        if ("oracle".equals(var14)) {
            var15 = var8 * var9 + 1;
            var16 = (var8 - 1) * var9;
            var12 = " select * from ( select my_table.*,rownum as my_rownum from ( select tableA.*,rownum as r from ( " + var1 + " " + var2 + " " + var3 + " " + var4 + " " + var5 + " ) tableA  ) my_table where rownum < " + var15 + " ) where my_rownum > " + var16;
        } else if ("mysql".equals(var14)) {
            var15 = (var8 - 1) * var9;
            var12 = var1 + " " + var2 + " " + var3 + " " + var4 + " " + var5 + " limit " + var15 + "," + var9;
        } else {
            var15 = var9 * var8;
            var16 = var9;
            if (var15 > var10) {
                var15 = var10;
                var16 = var10 - var9 * (var8 - 1);
            }

            if (var8 == 1) {
                var12 = var1 + " top " + var16 + " " + var2 + " " + var3 + " " + var4 + " " + var5;
            } else {
                var12 = " select top " + var16 + " * from ( select top " + var16 + " * from ( " + var1 + " top " + var15 + " " + var2 + " " + var3 + " " + var4 + " " + var5 + " ) tbltemp1 " + var6 + " ) tbltemp2 " + var7;
            }
        }

        return var12;
    }

    private String getCurrentDate() {
        SimpleDateFormat var1 = new SimpleDateFormat("yyyy-MM-dd");
        return var1.format(new Date());
    }

    private String getCurrentTime() {
        SimpleDateFormat var1 = new SimpleDateFormat("HH:mm:ss");
        return var1.format(new Date());
    }

    public String createXml(Document var1) {
        XMLOutputter var2 = null;
        Format var3 = Format.getCompactFormat();
        var3.setEncoding("UTF-8");
        var3.setIndent((String)null);
        var2 = new XMLOutputter(var3);
        return var2.outputString(var1);
    }

    public String getVDataSource(Map<String, Object> var1) throws ModeDataServiceException {
        String var2 = Util.null2String(var1.get("vdatasource"));
        String var3 = Util.null2String(var1.get("virtualformtype"));
        if ("1".equals(var3)) {
            throw new ModeDataServiceException(ModeDataExceptionEnum.NOT_SUPPORT_View_VIRFORM.toString());
        } else {
            if ("$ECOLOGY_SYS_LOCAL_POOLNAME".equals(var2)) {
                var2 = "local";
            }

            return var2;
        }
    }

    public int getFormIdByModeId(int var1) {
        RecordSet var2 = new RecordSet();
        int var3 = 0;
        String var4 = "select formid from modeinfo where id=?";
        var2.executeQuery(var4, new Object[]{var1});
        if (var2.next()) {
            var3 = var2.getInt("formid");
        }

        return var3;
    }

    private String removePrefixAndSuffix(String var1, String var2) {
        var1 = Util.null2String(var1).trim();
        if (var1.endsWith(var2)) {
            var1 = var1.substring(0, var1.length() - 1);
        }

        if (var1.startsWith(var2)) {
            var1 = var1.substring(1);
        }

        return var1;
    }

    public Map<String, String> recordSet2Map(RecordSet var1) {
        HashMap var2 = new HashMap();
        String[] var3 = var1.getColumnName();
        if (var3 == null) {
            return var2;
        } else {
            String[] var4 = var3;
            int var5 = var3.length;

            for(int var6 = 0; var6 < var5; ++var6) {
                String var7 = var4[var6];
                var2.put(var7.toLowerCase(), var1.getString(var7));
            }

            return var2;
        }
    }
}
