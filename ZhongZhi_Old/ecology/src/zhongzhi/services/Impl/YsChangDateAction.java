package zhongzhi.services.Impl;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

import java.text.SimpleDateFormat;
import java.util.Date;

public class YsChangDateAction extends BaseBean implements Action {
    @Override
    public String execute(RequestInfo request) {
        String retStr = Action.SUCCESS;
        try {
            Property[] properties =request.getMainTableInfo().getProperty();// 获取表单主字段信息
            String xmmc="";
            for (int i = 0; i < properties.length; i++) {
                String name = properties[i].getName();// 主字段名称
                if("xmmc".equalsIgnoreCase(name)){
                    xmmc= Util.null2String(properties[i].getValue());
                }
                writeLog("name===========>"+name);
            }
            RecordSet rs = new RecordSet();
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Date date = new Date();
            String format1 = format.format(date);
            String sql="update uf_ysxx set modedatacreatedate='"+format1+"' where xmmc="+xmmc;
            rs.execute(sql);
        }catch (Exception e){
            writeLog("update uf_ysxx error ==="+e);
            retStr=Action.FAILURE_AND_CONTINUE;
        }
        return retStr;
    }
}
