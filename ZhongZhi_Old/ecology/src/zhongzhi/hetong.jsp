<!DOCTYPE HTML>
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
    <title>圣诞快乐***😂</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <style type="text/css">
        @font-face {
            font-family: digit;
            src: url('digital-7_mono.ttf') format("truetype");
        }
    </style>
    <meta name="keywords" content="520" />
    <meta name="description" content="" />	<link href="css/default.css" type="text/css" rel="stylesheet">
    <script type="text/javascript" src="js/jquery.js"></script>
    <script type="text/javascript" src="js/garden.js"></script>
    <script type="text/javascript" src="js/functions.js"></script>
    <script type="text/javascript" src="js/jq.snow.js"></script>
</head>


<audio id="bgMusic" src="js/song1.mp3"   autoplay></audio>
<!--<embed id ="test" src="js/song1.mp3" hidden="true" width="0" height="0"  autostart="true" loop="true" /> 这个可以的-->
<body>
<!--下面是调用方法和参数说明-->
<script>
    $(function(){
        $.fn.snow({
            minSize: 5,		//雪花的最小尺寸
            maxSize: 50, 	//雪花的最大尺寸
            newOn: 130		//雪花出现的频率 这个数值越小雪花越多
        });

    });


</script>
<div id="mainDiv">
    <div id="content">
        <div id="code">
            <span class="comments">/**</span><br />
            <span class="space"/><span class="comments">* 嗨！</span><br />
            <span class="space"/><span class="comments">* 今天是2020年12月24日。</span><br />
            <span class="space"/><span class="comments">* 经过简单的聊天，相信你大概知道了我是个怎么样的人。 </span><br />
            <span class="space"/><span class="comments">* 我不是一个很擅长表达言语的人。 </span><br />
            <span class="space"/><span class="comments">* 就准备了这个页面来陪你过这个特殊的日子。</span><br />
            <span class="space"/><span class="comments">*/</span><br />
            Girl u = <span class="keyword">new</span> Girl(<span class="string">"***"</span>);<br/>
            Boy i = <span class="keyword">new</span> Boy(<span class="string">"***"</span>);<br />
            <span class="comments"> 每次想找你聊天时，都想了各种场景😋😍🤑</span><br />
            <span class="comments"> 然而往往不按预想场景走</span><br />
            <span class="comments"> 从第一次开始和你聊天。</span><br />
            <span class="comments"> 到今天，时间不长，但印象很深。</span><br />
            <span class="comments"> 每次想到和你聊天的样子，都忍不住在笑😋😋😋 </span><br />
            <span class="comments"> 很开心，认识一个开朗，善良的女孩。 </span><br />
            🤣😛😜🇨🇳😋😍🤑<br />
            <span class="comments"> 下面是自我吐槽时间。。。😝。 </span><br />
            <span class="comments">我虽然有的时候总是充当话题终结者</span><br />
            <span class="comments">总是想不到一句像样的开场白.....</span><br />
            <span class="comments">总是自顾自的说下去.....</span><br />
            <span class="comments">有种种缺点</span><br />
            <span class="comments">但是，我认为我还算是一个真实😋，有担当的人😋</span><br />
            <span class="comments"></span><br />
            <span class="comments">-----吐槽我自己完毕-----</span><br />
            <span class="comments">🤣😋😋😍🤑</span><br />
            <span class="space"/><span class="comments">不管以后如何 </span><br />
            <span class="keyword">希望你有个不一样的圣诞</span> <span class="keyword"></span>！<br />
            <span class="keyword">期望与你的相见。。。</span> <span class="keyword">🤝</span><br />
        </div>
        <div id="loveHeart">
            <canvas id="garden"></canvas>
            <div id="words">
                <div id="messages">
                    <div id="elapseClock"></div>
                </div>
                <div id="loveu">
                    <div class="signature"></div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<div class="bg1">
    <div class="main">
        <footer style="line-height:20px">
            <div id="copyright">

                </object>
            </div>
    </div>
</div>
</div>

<script type="text/javascript">
    var offsetX = $("#loveHeart").width() / 2;
    var offsetY = $("#loveHeart").height() / 2 - 55;
    var together = new Date();
    together.setFullYear(2020, 4, 11);
    together.setHours(00);
    together.setMinutes(0);
    together.setSeconds(0);
    together.setMilliseconds(0);

    if (!document.createElement('canvas').getContext) {
        var msg = document.createElement("div");
        msg.id = "errorMsg";
        msg.innerHTML = "Your browser doesn't support HTML5!<br/>Recommend use Chrome 14+/IE 9+/Firefox 7+/Safari 4+";
        document.body.appendChild(msg);
        $("#code").css("display", "none")
        $("#copyright").css("position", "absolute");
        $("#copyright").css("bottom", "10px");
        document.execCommand("stop");
    } else {
        setTimeout(function () {
            startHeartAnimation();
        }, 5000);
        var text="<span>希望你</span><span class=\"digit\">开心</span>  <span class=\"digit\">快乐</span><span>每一天</span>";
        $("#elapseClock").html(text);
        $("#loveu").html("<img src='js/forlove.jsp' style='width: 10%,height:10%'> <img>");
        adjustCodePosition();
        $("#code").typewriter();
    }
</script>



</body>
</html>
