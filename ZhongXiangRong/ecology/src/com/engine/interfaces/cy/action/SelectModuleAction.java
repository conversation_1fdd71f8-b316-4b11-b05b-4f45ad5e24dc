package com.engine.interfaces.cy.action;

import com.ibm.icu.math.BigDecimal;
import com.weaver.general.Util;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class SelectModuleAction extends BaseBean implements Action {

    private String sfkzf;

    private String sfkzfval;

    private String detailxzk;

    private String detailsqsl;

    private String num;

    @Override
    public String execute(RequestInfo requestInfo) {

        RequestManager rm = requestInfo.getRequestManager();
        HashMap<String,String> maindata = new HashMap<>();
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        for( Property property : propertys) {
            String str = property.getName();
            String value = property.getValue();
            maindata.put(str, value);
        }
        if(!sfkzfval.equals(maindata.get(sfkzf))){
            return SUCCESS;
        }
        String table = rm.getBillTableName();
        String detail = table+"_dt"+num;
        writeLog("-------"+detail);
        int id = rm.getBillid();
        String sql = "SELECT * FROM "+detail+" where mainid = "+id;
        writeLog("-------"+sql);
        RecordSet recordSet = new RecordSet();
        boolean flag =  recordSet.execute(sql);
        if(flag){
            HashMap<String, BigDecimal> hashMap = new HashMap<>();
            while(recordSet.next()){
                writeLog("-------"+recordSet.getString(detailxzk));
                writeLog("-------"+recordSet.getString(detailsqsl));
                String xzkid = Util.null2String(recordSet.getString(detailxzk));
                BigDecimal sl =new BigDecimal(Util.null2o(recordSet.getString(detailsqsl)));
                if(hashMap.containsKey(xzkid)){
                    hashMap.put(xzkid,hashMap.get(xzkid).add(sl));
                }else{
                    hashMap.put(xzkid,sl);
                }
            }
            writeLog("-------"+hashMap);
            Set<Map.Entry<String,BigDecimal>> sets = hashMap.entrySet();
            for( Map.Entry<String,BigDecimal> set : sets){
                String key = set.getKey();
                BigDecimal val = set.getValue();
                writeLog("-------"+key);
                writeLog("-------"+val);
                String sqlmodule = "SELECT * FROM uf_czkgl where id = "+key;
                RecordSet select = new RecordSet();
                writeLog("-------"+sqlmodule);
                boolean ans = select.execute(sqlmodule);
                if(ans){
                    if(select.next()){
                        BigDecimal num =new BigDecimal(Util.null2o(select.getString("sysl")));
                        String km =Util.null2String(select.getString("km"));
                        if(val.compareTo(num)>0){
                            rm.setMessagecontent("卡名为"+km+"剩余数量为"+num+"小于提交数量"+val);
                            return FAILURE_AND_CONTINUE;
                        }
                    }
                }else{
                    rm.setMessagecontent("sql执行出错"+sqlmodule);
                    return FAILURE_AND_CONTINUE;
                }
            }

        }
        return SUCCESS;
    }

    public String getSfkzf() {
        return sfkzf;
    }

    public void setSfkzf(String sfkzf) {
        this.sfkzf = sfkzf;
    }

    public String getSfkzfval() {
        return sfkzfval;
    }

    public void setSfkzfval(String sfkzfval) {
        this.sfkzfval = sfkzfval;
    }

    public String getDetailxzk() {
        return detailxzk;
    }

    public void setDetailxzk(String detailxzk) {
        this.detailxzk = detailxzk;
    }

    public String getDetailsqsl() {
        return detailsqsl;
    }

    public void setDetailsqsl(String detailsqsl) {
        this.detailsqsl = detailsqsl;
    }

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num;
    }
}
