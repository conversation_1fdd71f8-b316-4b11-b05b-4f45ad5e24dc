package com.engine.interfaces.cy.action;

import com.weaver.general.Util;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;

import java.math.BigDecimal;
import java.util.HashMap;


public class SelectModulemainAction extends BaseBean implements Action {

    private String sfkzf;

    private String sfkzfval;

    private String cxjecol;

    private String sqr;

    private String sout;

    @Override
    public String execute(RequestInfo requestInfo) {

        RequestManager rm = requestInfo.getRequestManager();
        HashMap<String,String> maindata = new HashMap<>();
        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        for( Property property : propertys) {
            String str = property.getName();
            String value = property.getValue();
            maindata.put(str, value);
        }
        if(!sfkzfval.equals(maindata.get(sfkzf))||"".equals(maindata.get(cxjecol))){
            return SUCCESS;
        }
        String cxje = maindata.get(cxjecol);
        String ry = maindata.get(sqr);
        BigDecimal bigDecimal = new BigDecimal(cxje);
        RecordSet recordSet = new RecordSet();
        String sql ="SELECT * FROM uf_byj where sqr = "+ry;
        boolean ans = recordSet.execute(sql);
        if(ans){
            if(recordSet.next()){
               BigDecimal sqlbigdecimal = new BigDecimal(Util.null2o(recordSet.getString("syjk")));
               if(bigDecimal.compareTo(sqlbigdecimal)>0){
                   rm.setMessagecontent(sout);
                   return FAILURE_AND_CONTINUE;
               }
            }
        }else{
            rm.setMessagecontent(recordSet.getExceptionMsg());
            return FAILURE_AND_CONTINUE;
        }
        return SUCCESS;
    }

    public String getSfkzf() {
        return sfkzf;
    }

    public void setSfkzf(String sfkzf) {
        this.sfkzf = sfkzf;
    }

    public String getSfkzfval() {
        return sfkzfval;
    }

    public void setSfkzfval(String sfkzfval) {
        this.sfkzfval = sfkzfval;
    }

    public String getCxjecol() {
        return cxjecol;
    }

    public void setCxjecol(String cxjecol) {
        this.cxjecol = cxjecol;
    }

    public String getSqr() {
        return sqr;
    }

    public void setSqr(String sqr) {
        this.sqr = sqr;
    }

    public String getSout() {
        return sout;
    }

    public void setSout(String sout) {
        this.sout = sout;
    }
}
