package com.engine.interfaces.gyl.module.service.impl;

import com.engine.core.impl.Service;
import com.engine.interfaces.gyl.module.cmd.YearExpenseReportDataCmd;
import com.engine.interfaces.gyl.module.service.YearExpenseReportService;

import java.util.Map;

/**
 * FileName: YearExpenseReportServiceImpl.java
 * 年度报销统计报表数据服务实现类
 *
 * <AUTHOR>
 * @version v1.00
 * @Date 2022/3/28
 */
public class YearExpenseReportServiceImpl extends Service implements YearExpenseReportService {
    @Override
    public Map<String, Object> listData(Map<String, Object> params) {
        return commandExecutor.execute(new YearExpenseReportDataCmd(user, params));
    }
}
