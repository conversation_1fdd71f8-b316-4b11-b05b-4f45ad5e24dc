package com.engine.interfaces.gyl.module.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * FileName: YearExpenseReportDataCmd.java
 * 年度报销统计报表数据Cmd层
 * 获取列表数据
 *
 * <AUTHOR>
 * @version v1.00
 * @Date 2022/3/28
 */
public class YearExpenseReportDataCmd extends AbstractCommonCommand<Map<String, Object>> {
    public YearExpenseReportDataCmd(User user, Map<String, Object> params) {
        this.user = user;
        this.params = params;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        //权限校验
        Map<String, Object> apiData = new HashMap<>();
        if (null == user) {
            apiData.put("hasRight", false);
            return apiData;
        }

        apiData.put("hasRight", true);
        try {
            RecordSet rs = new RecordSet();
            String sql = "select TBA.* from (select TBL.lastname,sum(TBL.sbje) as bxze FROM (select  " +
                    "(case when a.bxr=1 then '系统管理员' " +
                    "else b.lastname end ) as lastname, " +
                    "a.sbje from uf_fybxtj a " +
                    "left join hrmresource b on (a.bxr = b.id) " +
                    ")TBL " +
                    "group by TBL.lastname " +
                    ")TBA " +
                    "order by TBA.bxze desc " +
                    "limit 5 ";
            boolean rsFlag = rs.executeQuery(sql);
            if (rsFlag) {
                /*
                 * 构造最外层,模拟数据，这里根据前端格式拼接数据
                 */
                List<Object> list = new ArrayList<>();
                /*
                 * 这里的所有模拟数据都是根据前端凭借的，开发者可以自定型
                 */
                Map<String, Object> map = new HashMap<>();
                int i = 1;
                List<String> data = new ArrayList<>();
                List<Object> child = new ArrayList<>();
                Map<String, Object> childMap;
                List<String> childData;

                while (rs.next()) {
                    childMap = new HashMap<>();
                    childData = new ArrayList<>();
                    childData.add(Util.null2String(rs.getString("lastname")));
                    childData.add(Util.null2String(rs.getString("bxze")));
                    childData.add(String.valueOf(i));
                    childMap.put("data", childData);
                    child.add(childMap);
                    i++;
                }
                map.put("data", data);
                map.put("child", child);
                list.add(map);

                apiData.put("data", list);
            } else {
                throw new Exception("查询数据出错：" + rs.getMsg());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return apiData;
    }
}
