package com.engine.interfaces.gyl.module.job;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.interfaces.schedule.BaseCronJob;

/**
 * @FileName ProjectStatusUpdateJob.java
 * @Description 项目任务状态更新Job
 * <AUTHOR>
 * @Version v1.00
 * @Date 2022/8/15 10:12
 */
public class ProjectStatusUpdateJob extends BaseCronJob {
    /**
     * 建模表名
     */
    private String moduleTableName;
    /**
     * 项目期限字段名
     */
    private String xmqxFieldName;
    /**
     * 项目状态字段名
     */
    private String xmztFieldName;
    /**
     * 项目状态禁用值
     */
    private String forbiddenValue;
    private BaseBean bb;
    /**
     * class名称
     */
    private String className;

    private void initBaseBean() {
        bb = new BaseBean();
        className = this.getClass().getName();
    }

    @Override
    public void execute() {
        initBaseBean();
        bb.writeLog(className + " --- START");
        try {
            String today = TimeUtil.getToday();
            RecordSet rs = new RecordSet();
            String sql = "update " + moduleTableName + " set " + xmztFieldName + "='" + forbiddenValue + "' " +
                    "where 1=1 and " + xmqxFieldName + " <= '" + today + "' ";
            bb.writeLog("sql:" + sql);
            boolean flag = rs.executeUpdate(sql);
            if (flag) {
                bb.writeLog(className + " --- SUCCESS END");
            } else {
                bb.writeLog("sql执行出错：" + rs.getMsg() + ";" + rs.getExceptionMsg());
                bb.writeLog(className + " ---FAIL END");
            }
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch exception：" + e.getMessage());
            bb.writeLog(className + " --- FAIL END");
        }
    }

    public String getModuleTableName() {
        return moduleTableName;
    }

    public void setModuleTableName(String moduleTableName) {
        this.moduleTableName = moduleTableName;
    }

    public String getXmqxFieldName() {
        return xmqxFieldName;
    }

    public void setXmqxFieldName(String xmqxFieldName) {
        this.xmqxFieldName = xmqxFieldName;
    }

    public String getXmztFieldName() {
        return xmztFieldName;
    }

    public void setXmztFieldName(String xmztFieldName) {
        this.xmztFieldName = xmztFieldName;
    }

    public String getForbiddenValue() {
        return forbiddenValue;
    }

    public void setForbiddenValue(String forbiddenValue) {
        this.forbiddenValue = forbiddenValue;
    }
}
