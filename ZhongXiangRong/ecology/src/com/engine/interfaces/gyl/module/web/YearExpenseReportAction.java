package com.engine.interfaces.gyl.module.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.interfaces.gyl.module.service.YearExpenseReportService;
import com.engine.interfaces.gyl.module.service.impl.YearExpenseReportServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

/**
 * FileName: YearExpenseReportAction.java
 * 年度报销统计报表数据接口
 *
 * <AUTHOR>
 * @version v1.00
 * @Date 2022/3/28
 */
public class YearExpenseReportAction {
    private YearExpenseReportService getService(User user) {
        return ServiceUtil.getService(YearExpenseReportServiceImpl.class, user);
    }

    /**
     * 获取weareport的数据
     *
     * @param request  请求体
     * @param response 响应体
     * @return JSON数据
     */
    @GET
    @Path("/listData")
    @Produces({MediaType.TEXT_PLAIN})
    public String listData(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> apidatas = new HashMap<>();
        try {
            //获取当前用户
            User user = HrmUserVarify.getUser(request, response);
            apidatas.putAll(getService(user).listData(ParamUtil.request2Map(request)));
            apidatas.put("api_status", true);
        } catch (Exception e) {
            e.printStackTrace();
            apidatas.put("api_status", false);
            apidatas.put("api_errormsg", "catch exception : " + e.getMessage());
        }
        return JSONObject.toJSONString(apidatas);
    }

}
