package com.engine.interfaces.gyl.common.util;

import com.alibaba.fastjson.JSONObject;
import com.engine.interfaces.gyl.common.bean.ApiResult;
import com.engine.interfaces.gyl.common.constant.CommonCst;
import okhttp3.*;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * FileName: HttpUtil.java
 * rest接口工具类
 *
 * <AUTHOR>
 * @version v1.00
 * @Date 2022/3/22
 */
public class HttpUtil {
    /**
     * 发送post接口,效果等同postman发送raw格式数据
     *
     * @param url    请求地址
     * @param params 请求参数
     * @return 返回ApiResult类
     */
    public static ApiResult postData(String url, Map<String, Object> params) {
        ApiResult res = new ApiResult();
        res.setSuccessFlag(false);
        try {
            OkHttpClient client = new OkHttpClient().newBuilder().
                    connectTimeout(30, TimeUnit.SECONDS)
                    .writeTimeout(30, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .build();
            MediaType mediaType = MediaType.parse("application/json");
            String aa = JSONObject.toJSONString(params, true);
            RequestBody body = RequestBody.create(mediaType, JSONObject.toJSONString(params, true));
            Request request = new Request.Builder()
                    .url(url)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();

            Response response = client.newCall(request).execute();
            JSONObject jo = JSONObject.parseObject(response.body().string());
            if (response.code() == CommonCst.API_SUCCESS_CODE) {
                String code = jo.getString("code");
                if (CommonCst.API_RESULT_SUCCESS.equals(code)) {
                    res.setSuccessFlag(true);
                }
                res.setData(jo.getString("data"));
                res.setResMsg(jo.getString("msg"));
            } else {
                res.setResMsg("调用失败，失败Code:" + response.code());
            }
        } catch (Exception e) {
            e.printStackTrace();
            res.setResMsg("执行异常：" + e.getMessage());
        }
        return res;
    }

    /**
     * 发送get接口,参数拼接在url后
     *
     * @param url    请求地址
     * @param params 请求参数
     * @return 返回ApiResult类
     */
    public static ApiResult getData(String url, Map<String, Object> params) {
        ApiResult res = new ApiResult();
        res.setSuccessFlag(false);
        try {
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .connectTimeout(30, TimeUnit.SECONDS)
                    .writeTimeout(30, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .build();
            url = appendUrlParam(url, params);
            Request request = new Request.Builder()
                    .url(url)
                    .method("GET", null)
                    .build();
            Response response = client.newCall(request).execute();
            JSONObject jo = JSONObject.parseObject(response.body().string());
            if (response.code() == CommonCst.API_SUCCESS_CODE) {
                String code = jo.getString("code");
                if (CommonCst.API_RESULT_SUCCESS.equals(code)) {
                    res.setSuccessFlag(true);
                    res.setData(jo);
                } else {
                    res.setSuccessFlag(false);
                }
            } else {
                res.setResMsg("调用失败，失败Code:" + response.code());
            }
        } catch (Exception e) {
            e.printStackTrace();
            res.setResMsg("执行异常：" + e.getMessage());
        }
        return res;
    }

    /**
     * 拼接接口url参数
     *
     * @param url    请求地址
     * @param params 请求参数
     * @return url拼接完成的值
     */
    private static String appendUrlParam(String url, Map<String, Object> params) {
        StringBuilder urlBuilder = new StringBuilder(url);
        if (params != null && !params.isEmpty()) {
            //第一个参数拼接
            if (url.contains("?")) {
                urlBuilder.append("&");
            } else {
                urlBuilder.append("?");
            }
            int i = 0;
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                System.out.println("key: " + entry.getKey() + "; value: " + entry.getValue());
                if (i > 0) {
                    urlBuilder.append("&");
                }
                urlBuilder.append(entry.getKey()).append("=").append(entry.getValue());
                i++;
            }
        }
        return urlBuilder.toString();
    }
}
