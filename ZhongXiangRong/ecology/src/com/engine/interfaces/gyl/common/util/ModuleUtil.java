package com.engine.interfaces.gyl.common.util;

import com.weaver.formmodel.util.DateHelper;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.formmode.data.ModeDataIdUpdate;
import weaver.formmode.setup.ModeRightInfo;
import weaver.general.BaseBean;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * FileName: ModuleUtil.java
 * 建模工具类
 * 建模的数据处理
 *
 * <AUTHOR>
 * @version v1.00
 * @Date 2022/3/28
 */
public class ModuleUtil {
    /**
     * 基础类
     */
    private static BaseBean bb;

    /**
     * 插入建模子表一条数据
     *
     * @param table       表名
     * @param insertField 插入的字段集合
     * @param values      字段对应的值得集合
     */
    public static void detailModuleInsert(String table, String[] insertField, List<Object> values) {
        bb = new BaseBean();
        try {
            if (insertField.length != values.size()) {
                bb.writeLog("调用方法失败");
            }
            int len = insertField.length;
            StringBuilder sql = new StringBuilder("insert into " + table + " (");
            for (int i = 0; i < len; i++) {
                sql.append(insertField[i]);
                if (i != len - 1) {
                    sql.append(",");
                }
            }
            sql.append(") VALUES (");
            for (int i = 0; i < len; i++) {
                sql.append("?");
                if (i != len - 1) {
                    sql.append(",");
                }
            }
            sql.append(")");
            RecordSet rs = new RecordSet();
            bb.writeLog("插入sql:" + sql);
            rs.executeUpdate(sql.toString(), values);
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    /**
     * 插入建模表一条数据
     *
     * @param table        表名uf_xxxx
     * @param insertFields 插入字段的id数组
     * @param value        插入字段的id对应的值数组(和字段id顺序对应)
     * @param creator      创建人id
     * @param module       模块id
     */
    public static int moduleInsert(String table, String[] insertFields, String[] value, int creator, int module) throws Exception {
        List<String> insertFieldsAry = new ArrayList<>();
        Collections.addAll(insertFieldsAry, insertFields);
        List<Object> values = new ArrayList<>();
        Collections.addAll(values, value);
        return doModuleInsert(table, insertFieldsAry, values, creator, module, null);
    }

    /**
     * 插入建模表一条数据
     *
     * @param table        表名uf_xxxx
     * @param insertFields 插入字段的id数组
     * @param value        插入字段的id对应的值数组(和字段id顺序对应)
     * @param creator      创建人id
     * @param module       模块id
     */
    public static int moduleInsert(String table, String[] insertFields, String[] value, int creator, int module, RecordSetTrans recordSetTrans) throws Exception {
        List<String> insertFieldsAry = new ArrayList<>();
        Collections.addAll(insertFieldsAry, insertFields);
        List<Object> values = new ArrayList<>();
        Collections.addAll(values, value);
        return doModuleInsert(table, insertFieldsAry, values, creator, module, recordSetTrans);
    }

    /**
     * 插入建模表一条数据
     *
     * @param table        表名uf_xxxx
     * @param insertFields 插入字段的id数组
     * @param values       插入字段的id对应的值数组(和字段id顺序对应)
     * @param creator      创建人id
     * @param module       模块id
     */
    public static int moduleInsert(String table, String[] insertFields, List<Object> values, int creator, int module, RecordSetTrans recordSetTrans) throws Exception {
        List<String> insertFieldsAry = new ArrayList<>();
        Collections.addAll(insertFieldsAry, insertFields);
        return doModuleInsert(table, insertFieldsAry, values, creator, module, recordSetTrans);
    }

    /**
     * 插入建模表一条数据
     *
     * @param table        表名uf_xxxx
     * @param insertFields 插入字段的id数组
     * @param values       插入字段的id对应的值数组(和字段id顺序对应)
     * @param creator      创建人id
     * @param module       模块id
     */
    public static int moduleInsert(String table, List<String> insertFields, List<Object> values, int creator, int module, RecordSetTrans recordSetTrans) throws Exception {
        return doModuleInsert(table, insertFields, values, creator, module, recordSetTrans);
    }


    /**
     * 执行建模数据插入
     *
     * @param table          表名
     * @param insertFields   插入的字段集合
     * @param values         字段对应的值集合
     * @param creator        创建人id
     * @param module         建模id
     * @param recordSetTrans 事务类
     * @return 插入的数据id
     * @throws Exception 异常
     */
    private static int doModuleInsert(String table, List<String> insertFields, List<Object> values, int creator, int module, RecordSetTrans recordSetTrans) throws Exception {
        bb = new BaseBean();
        bb.writeLog("执行建模数据插入操作：");
        bb.writeLog("插入table名：" + table);
        bb.writeLog("插入字段列表：" + insertFields);
        bb.writeLog("插入数据列表：" + values);
        bb.writeLog("创建人id：" + creator);
        bb.writeLog("模块id：" + module);
        if (insertFields.size() != values.size()) {
            bb.writeLog("字段数和数值数量不等，退出此次插入操作");
            return -1;
        }
        User user = new User(creator);
        RecordSet rs = new RecordSet();
        StringBuilder sb = new StringBuilder();
        int billId = ModeDataIdUpdate.getInstance().getModeDataNewIdByUUID(
                table, module, user.getUID(), (user.getLogintype()).equals("1") ? 0 : 1, DateHelper.getCurrentDate(), DateHelper.getCurrentTime());
        bb.writeLog("插入后的billId :" + billId);
        //将数据的其他内容更新到表中
        for (int i = 0; i < insertFields.size(); i++) {
            sb.append(insertFields.get(i)).append("=").append("?");
            if (i != insertFields.size() - 1) {
                sb.append(", ");
            }
        }
        //更新业务字段值
        if (recordSetTrans != null) {
            recordSetTrans.executeUpdate("update " + table + " set " + sb + " where id=" + billId, values);
        } else {
            rs.executeUpdate("update " + table + " set " + sb + " where id=" + billId, values);
        }
        //权限重构
        ModeRightInfo ModeRightInfo = new ModeRightInfo();
        ModeRightInfo.setNewRight(true);
        ModeRightInfo.editModeDataShare(creator, module, billId);
        return billId;
    }


}
