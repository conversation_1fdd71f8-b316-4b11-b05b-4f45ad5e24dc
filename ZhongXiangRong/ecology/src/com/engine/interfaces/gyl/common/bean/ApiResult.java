package com.engine.interfaces.gyl.common.bean;

/**
 * FileName: ApiResult.java
 * 类的详细说明
 *
 * <AUTHOR>
 * @version v1.00
 * @Date 2022/3/22
 */
public class ApiResult {
    /**
     * 返回成功失败标识 true 成功，false 失败
     */

    private boolean successFlag;
    /**
     * 返回信息
     */
    private String resMsg;
    /**
     * 返回的数据
     */
    private Object data;

    public boolean isSuccessFlag() {
        return successFlag;
    }

    public void setSuccessFlag(boolean successFlag) {
        this.successFlag = successFlag;
    }

    public String getResMsg() {
        return resMsg;
    }

    public void setResMsg(String resMsg) {
        this.resMsg = resMsg;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
