package com.engine.interfaces.gyl.hrm.biz;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.interfaces.gyl.common.bean.ApiResult;
import com.engine.interfaces.gyl.common.util.HttpUtil;
import com.engine.interfaces.gyl.hrm.util.OrgDataUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.HashMap;
import java.util.Map;

/**
 * FileName: SyncHrmUserJob.java
 * 同步人员岗位的job
 *
 * <AUTHOR>
 * @version v1.00
 * @Date 2022/3/24
 */
public class SyncHrmUserJob extends BaseCronJob {
    
    /**
     * JOB核心执行方法
     */
    @Override
    public void execute() {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "----START");
        try {
            //获取配置文件的接口地址
            String orgUrl = Util.null2String(bb.getPropValue("exam_hrm_api", "user_api_url"));
            //固定查询参数
            Map<String, Object> params = new HashMap<>(1);
            params.put("systemid", "examine");
            ApiResult ar = HttpUtil.getData(orgUrl, params);
            if (ar.isSuccessFlag()) {
                //接口调用成功的情况
                JSONArray ja = JSONObject.parseObject(Util.null2String(ar.getData())).getJSONArray("resultData");
                if (ja != null && !ja.isEmpty()) {
                    bb.writeLog("resultData size:" + ja.size());
                    insertData(ja);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch exception：" + e.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "----END");
    }

    /**
     * 根据接口返回的数据，同步人员岗位信息
     *
     * @param ja 接口返回结果集
     * @throws Exception 方法内部返回异常
     */
    private void insertData(JSONArray ja) throws Exception {
        BaseBean bb = new BaseBean();
        JSONObject jo;
        String employeeNumber, higherLevelCode, higherId, myId;
        Map<String, Object> params;
        int newSupId;
        Map<String, String> mapCodeId = new HashMap<>();
        for (int i = 0; i < ja.size(); i++) {
            jo = ja.getJSONObject(i);
            //将所有人插入，默认无上级
            employeeNumber = jo.getString("employeeNumber");
            //生成参数
            params = generateParams(jo);
            newSupId = OrgDataUtil.userInsert(params);
            //将编号和插入的id匹配
            mapCodeId.put(employeeNumber, String.valueOf(newSupId));
        }
        //新增人员之后，更新上级
        for (int i = 0; i < ja.size(); i++) {
            jo = ja.getJSONObject(i);
            employeeNumber = jo.getString("employeeNumber");
            higherLevelCode = jo.getString("higherLevelCode");
            //顶级人员无higherLevelCode属性
            if (jo.containsKey("higherLevelCode")) {
                if (mapCodeId.containsKey(higherLevelCode) && mapCodeId.containsKey(employeeNumber)) {
                    //根据上级code，找id
                    higherId = mapCodeId.get(higherLevelCode);
                    myId = mapCodeId.get(employeeNumber);
                    bb.writeLog("找到:" + employeeNumber + "编号的人员的上级id:" + higherId);
                    updateHigherId(higherId, myId);
                } else {
                    bb.writeLog("未找到:" + employeeNumber + "编号的人员的上级人员对应的id或本人id");
                }
            }
        }
    }


    /**
     * 生成方法的通用参数
     *
     * @param jo 原接口数据参数对象
     * @return 人员同步的方法参数
     */
    private Map<String, Object> generateParams(JSONObject jo) {
        //状态转换
        String status;
        if ("0".equals(Util.null2String(jo.getString("employeeStatus")))) {
            status = "5";
        } else {
            status = "1";
        }
        Map<String, Object> result = new HashMap<>(10);
        result.put("employeeName", Util.null2String(jo.getString("employeeName")));
        result.put("employeeNumber", Util.null2String(jo.getString("employeeNumber")));
        result.put("employeeStatus", status);
        result.put("institutionCode", Util.null2String(jo.getString("institutionCode")));
        result.put("jobtitlecode", Util.null2String(jo.getString("jobtitlecode")));
        result.put("loginAccount", Util.null2String(jo.getString("loginAccount")));
        result.put("securityLevel", Util.null2String(jo.getString("securityLevel")));
        result.put("sex", Util.null2String(jo.getString("sex")));
        result.put("showOrder", Util.null2String(jo.getString("showOrder")));
        result.put("supUserId", "0");
        return result;
    }

    /**
     * 更新上级人员id
     *
     * @param managerId 上级id
     * @param myId      本人id
     */
    private void updateHigherId(String managerId, String myId) {
        RecordSet rs = new RecordSet();
        rs.executeUpdate("update hrmresource set managerid = ? where id = ?", managerId, myId);
    }
}
