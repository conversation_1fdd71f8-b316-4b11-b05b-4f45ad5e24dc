package com.engine.interfaces.gyl.hrm.util;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.company.DepartmentComInfo;
import weaver.hrm.company.SubCompanyComInfo;
import weaver.hrm.finance.SalaryManager;
import weaver.hrm.resource.ResourceComInfo;
import weaver.interfaces.hrm.HrmServiceManager;
import weaver.matrix.MatrixUtil;

import java.util.*;

/**
 * FileName: OrgDataUtil.java
 * 组织结构数据更新工具类
 *
 * <AUTHOR>
 * @version v1.00
 * @Date 2022/3/23
 */
public class OrgDataUtil {
    /**
     * 分部数据插入
     *
     * @param params 接口参数
     * @return 新增分部id
     */
    public static int supInsert(Map<String, Object> params) {
        //新增的分部id
        int id = 0;
        char separator = weaver.general.Util.getSeparator();
        RecordSet rsSch = new RecordSet();
        //分部简称
        String subCompanyName = Util.null2String(params.get("subCompanyName"));
        //分部描述
        String subCompanyDesc = Util.null2String(params.get("subCompanyDesc"));
        //总部id
        String companyId = Util.null2String(params.get("companyId"));
        //上级分部id
        String supSubComId = Util.null2String(params.get("supSubComId"));
        //网址
        String url = "";
        //显示顺序
        String showOrder = Util.null2String(params.get("showOrder"));
        //使用存储过程新增分部
        String para = subCompanyName + separator + subCompanyDesc + separator + companyId + separator + supSubComId + separator + url + separator + showOrder;
        rsSch.executeProc("HrmSubCompany_Insert", para);
        if (rsSch.next()) {
            id = rsSch.getInt(1);
            rsSch.getString(1);
        }
        //更新分部表中的其他字段
        updateSupOtherFields(id, params);
        SubCompanyComInfo sci = new SubCompanyComInfo();
        //清除全部分部缓存
        sci.removeCompanyCache();
        //新增单个分部缓存
        sci.addCache(String.valueOf(id));
        //同步分部数据到矩阵
        MatrixUtil.updateSubcompayData("" + id);
        // 初始化应用分权
        new weaver.hrm.appdetach.AppDetachComInfo().initSubDepAppData();
        return id;
    }

    /**
     * 部门数据插入
     *
     * @param params 插入机构的各个参数
     * @return 新建部门的id
     */
    public static int deptInsert(Map<String, Object> params) {
        BaseBean bb = new BaseBean();
        bb.writeLog("OrgDataUtil/deptInsert ---START");
        bb.writeLog("params :" + params);
        //新增的分部id
        int id = 0;
        char separator = Util.getSeparator();
        RecordSet rsSch = new RecordSet();
        //部门标识
        String departmentMark = Util.null2String(params.get("departmentMark"));
        //部门名称
        String departmentName = Util.null2String(params.get("departmentName"));
        //上级部门id
        String supDepId = Util.null2String(params.get("supDepId"));
        //所有上级部门id
        String allSupDepId = Util.null2String(params.get("allSupDepId"));
        //所属分部1
        String subCompanyId1 = Util.null2String(params.get("subCompanyId1"));
        //显示顺序
        String showOrder = Util.null2String(params.get("showOrder"));
        //协办人id
        int coAdjutant = 0;
        //使用存储过程新增分部
        String para = departmentMark + separator + departmentName + separator + supDepId + separator + allSupDepId + separator + subCompanyId1 + separator + showOrder + separator + coAdjutant;
        rsSch.executeProc("HrmDepartment_Insert", para);
        if (rsSch.next()) {
            id = rsSch.getInt(1);
        }
        bb.writeLog("id :" + id);
        //更新部门表中的其他字段
        updateDepOtherFields(id, params);
        DepartmentComInfo dci = new DepartmentComInfo();
        //清除全部部门缓存
        dci.removeCompanyCache();
        //新增单个部门缓存
        dci.addCache(String.valueOf(id));
        HrmServiceManager hrmServiceManager = new HrmServiceManager();
        hrmServiceManager.SynInstantDepartment(String.valueOf(id), "2");
        //同步分部数据到矩阵
        MatrixUtil.updateDepartmentData("" + id);
        // 初始化应用分权
        new weaver.hrm.appdetach.AppDetachComInfo().initSubDepAppData();
        bb.writeLog("OrgDataUtil/deptInsert ---END");
        return id;
    }

    /**
     * 人员数据插入
     *
     * @param params 插入人员的各个参数
     * @return 新建人员的id
     */
    public static int userInsert(Map<String, Object> params) throws Exception {
        int newId;
        BaseBean bb = new BaseBean();
        bb.writeLog("OrgDataUtil/userInsert ---START");

        String[] insertFields = ("id,lastname,workcode,status,managerid,departmentid,subcompanyid1," +
                "jobtitle,loginid,seclevel,sex,dsporder").split(",");

        char separator = Util.getSeparator();
        Calendar todayCal = Calendar.getInstance();
        String today = Util.add0(todayCal.get(Calendar.YEAR), 4) + "-" +
                Util.add0(todayCal.get(Calendar.MONTH) + 1, 2) + "-" +
                Util.add0(todayCal.get(Calendar.DAY_OF_MONTH), 2);
        String userPara = "" + 1 + separator + today;
        RecordSet rsSch = new RecordSet();
        //获取即将目前最大的人员id，+1后得到即将新增的人员id
        rsSch.executeProc("HrmResourceMaxId_Get", "");
        rsSch.next();
        //新增的人员id
        newId = rsSch.getInt(1);
        //上级人员id
        String supUserId = Util.null2String(params.get("supUserId"));
        //组织code（即部门code）
        String deptCode = Util.null2String(params.get("institutionCode"));
        //获取部门相关信息
        Map<String, Object> deptParams = getDepInfoByCode(deptCode);
        //所属部门id
        String departmentId = Util.null2String(deptParams.get("departmentId"));
        //所属分部id
        String subCompanyId1 = Util.null2String(deptParams.get("subCompanyId1"));
        //插入的数据list
        List<Object> values = new ArrayList<>();
        values.add(newId);
        values.add(Util.null2String(params.get("employeeName")));
        values.add(Util.null2String(params.get("employeeNumber")));
        values.add(Util.null2String(params.get("employeeStatus")));
        values.add(Util.null2String(params.get("supUserId")));
        values.add(departmentId);
        values.add(subCompanyId1);
        values.add(Util.getIntValue(Util.null2String(params.get("jobtitlecode")), 0));
        values.add(Util.null2String(params.get("loginAccount")));
        values.add(Util.null2String(params.get("securityLevel")));
        values.add(Util.null2String(params.get("sex")));
        values.add(Util.getIntValue(Util.null2String(params.get("showOrder")), 0));

        //使用sql新增人员
        int len = insertFields.length;
        StringBuilder sql = new StringBuilder("insert into HrmResource (");
        for (int i = 0; i < len; i++) {
            sql.append(insertFields[i]);
            if (i != len - 1) {
                sql.append(",");
            }
        }
        sql.append(") values (");
        for (int i = 0; i < len; i++) {
            sql.append("?");
            if (i != len - 1) {
                sql.append(",");
            }
        }
        sql.append(")");

        bb.writeLog("插入sql:" + sql);
        boolean flag = rsSch.executeUpdate(sql.toString(), values);
        if (!flag) {
            throw new Exception("插入人员信息失败！" + rsSch.getMsg());
        }
        rsSch.executeProc("HrmResource_CreateInfo", "" + newId + separator + userPara + separator + userPara);
        ResourceComInfo resourceComInfo = new ResourceComInfo();
        resourceComInfo.addResourceInfoCache(String.valueOf(newId));
        SalaryManager salaryManager = new SalaryManager();
        salaryManager.initResourceSalary(String.valueOf(newId));
        //人员id，上级人员id，部门id，分部id
        String para1 = "" + newId + separator + supUserId + separator + departmentId + separator + subCompanyId1 + separator + "0" + separator + "0";
        rsSch.executeProc("HrmResource_Trigger_Insert", para1);
        String sql_1 = ("insert into HrmInfoStatus (itemid,hrmid) values(1," + newId + ")");
        rsSch.execute(sql_1);
        String sql_2 = ("insert into HrmInfoStatus (itemid,hrmid) values(2," + newId + ")");
        rsSch.execute(sql_2);
        String sql_3 = ("insert into HrmInfoStatus (itemid,hrmid) values(3," + newId + ")");
        rsSch.execute(sql_3);
        String sql_10 = ("insert into HrmInfoStatus (itemid,hrmid) values(10," + newId + ")");
        rsSch.execute(sql_10);
        resourceComInfo.updateResourceInfoCache(String.valueOf(newId));
        return newId;
    }

    /**
     * 更新分部表中的其他字段
     *
     * @param id     机构id
     * @param params 接口参数
     */
    private static void updateSupOtherFields(int id, Map<String, Object> params) {
        RecordSet rs = new RecordSet();
        String departmentCode = Util.null2String(params.get("subCompanyCode"));
        String canceled = Util.null2String(params.get("canceled"));
        String level = Util.null2String(params.get("level"));
        String sql = "update hrmsubcompany set subcompanycode = ?,canceled = ?,tlevel = ? where id = ? ";
        rs.executeUpdate(sql, departmentCode, canceled, level, id);
    }

    /**
     * 更新部门表中的其他字段
     *
     * @param id     机构id
     * @param params 接口参数
     */
    private static void updateDepOtherFields(int id, Map<String, Object> params) {
        RecordSet rs = new RecordSet();
        String departmentCode = Util.null2String(params.get("departmentCode"));
        String canceled = Util.null2String(params.get("canceled"));
        String level = Util.null2String(params.get("level"));
        String sql = "update hrmdepartment set departmentcode = ?,canceled = ?,tlevel = ? where id = ? ";
        rs.executeUpdate(sql, departmentCode, canceled, level, id);
    }

    /**
     * 根据部门code获取部门相关信息
     *
     * @param deptCode 部门code
     * @return 部门相关信息
     */
    private static Map<String, Object> getDepInfoByCode(String deptCode) {
        Map<String, Object> result = new HashMap<>(2);
        RecordSet rs = new RecordSet();
        String sql = "select id,subcompanyid1 from hrmdepartment where departmentcode = ? ";
        rs.executeQuery(sql, deptCode);
        if (rs.next()) {
            result.put("departmentId", rs.getInt("id"));
            result.put("subCompanyId1", rs.getInt("subcompanyid1"));
        }
        return result;
    }
}
