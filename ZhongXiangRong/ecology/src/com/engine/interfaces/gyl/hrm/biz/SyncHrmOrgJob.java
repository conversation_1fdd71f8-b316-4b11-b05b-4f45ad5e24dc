package com.engine.interfaces.gyl.hrm.biz;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.interfaces.gyl.common.bean.ApiResult;
import com.engine.interfaces.gyl.common.util.HttpUtil;
import com.engine.interfaces.gyl.hrm.util.OrgDataUtil;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.HashMap;
import java.util.Map;

/**
 * FileName: SyncHrmOrgJob.java
 * 同步分部部门组织架构的job
 *
 * <AUTHOR>
 * @version v1.00
 * @Date 2022/3/23
 */
public class SyncHrmOrgJob extends BaseCronJob {

    /**
     * JOB核心执行方法
     */
    @Override
    public void execute() {
        BaseBean bb = new BaseBean();
        bb.writeLog(this.getClass().getName() + "----START");
        try {
            //获取配置文件的接口地址
            String orgUrl = Util.null2String(bb.getPropValue("exam_hrm_api", "org_api_url"));
            //固定查询参数
            Map<String, Object> params = new HashMap<>(1);
            params.put("systemid", "examine");
            ApiResult ar = HttpUtil.getData(orgUrl, params);
            if (ar.isSuccessFlag()) {
                //接口调用成功的情况
                JSONArray ja = JSONObject.parseObject(Util.null2String(ar.getData())).getJSONArray("resultData");
                if (ja != null && !ja.isEmpty()) {
                    insertData(ja);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch exception：" + e.getMessage());
        }
        bb.writeLog(this.getClass().getName() + "----END");
    }

    /**
     * 根据接口返回的数据，同步分部部门数据
     *
     * @param ja 接口列表机构数据
     */
    private void insertData(JSONArray ja) {
        JSONObject jo;
        //机构标识,上级机构编码，机构编码
        String sign, higherLevelCode, departmentCode, status, canceled;
        Map<String, Object> params;
        JSONArray jaTopSub = new JSONArray();
        JSONArray jaOthers = new JSONArray();
        for (int i = 0; i < ja.size(); i++) {
            jo = ja.getJSONObject(i);
            sign = jo.getString("sign");
            higherLevelCode = jo.getString("higherLevelCode");
            //找出所有顶级分部
            if ("0".equals(sign) && "".equals(higherLevelCode)) {
                jaTopSub.add(jo);
            } else {
                //其他所有的机构
                jaOthers.add(jo);
            }
        }
        int newSupId;

        //将顶级分部插入
        for (int i = 0; i < jaTopSub.size(); i++) {
            params = new HashMap<>(5);
            jo = jaTopSub.getJSONObject(i);
            departmentCode = jo.getString("departmentCode");
            status = jo.getString("departmentCode");
            if ("0".equals(status)) {
                canceled = "1";
            } else {
                canceled = "";
            }
            params.put("subCompanyCode", Util.null2String(jo.getString("departmentCode")));
            params.put("subCompanyName", Util.null2String(jo.getString("departmentMark")));
            params.put("subCompanyDesc", Util.null2String(jo.getString("departmentName")));
            params.put("canceled", canceled);
            params.put("level", Util.null2String(jo.getString("level")));
            params.put("companyId", "1");
            params.put("supSubComId", "0");
            params.put("showOrder", Util.getIntValue(Util.null2String(jo.getString("order")), 0));
            newSupId = OrgDataUtil.supInsert(params);
            //更新该分部下的所有机构
            updateOtherData(jaOthers, newSupId, String.valueOf(newSupId), newSupId, departmentCode);
        }
    }

    /**
     * 更新所有下级机构
     *
     * @param jaOtherSub    除了顶级分部的所有数据
     * @param subId         上级的机构id
     * @param allSubId      所有上级机构id
     * @param subCompanyId1 上级分部id
     * @param subCode       上级机构code
     */
    private void updateOtherData(JSONArray jaOtherSub, int subId, String allSubId, int subCompanyId1, String subCode) {
        JSONObject jo;
        String higherLevelCode, departmentCode, sign, newAllSubId;
        Map<String, Object> params;
        int newId = 0, newSubCompanyId1 = 0;
        JSONArray nextArray = new JSONArray();
        nextArray.addAll(jaOtherSub);
        for (int i = 0; i < jaOtherSub.size(); i++) {
            jo = jaOtherSub.getJSONObject(i);
            higherLevelCode = jo.getString("higherLevelCode");
            departmentCode = jo.getString("departmentCode");
            sign = jo.getString("sign");
            //匹配上级机构代码
            if (subCode.equals(higherLevelCode)) {
                //匹配到下级机构后，下一次递归的array删除调这个obj
                nextArray.remove(jo);
                //生成通用参数
                params = generateParams(jo, sign);
                if ("0".equals(sign)) {
                    //分部
                    params.put("supSubComId", subId);
                    newId = OrgDataUtil.supInsert(params);
                    newSubCompanyId1 = newId;
                } else if ("1".equals(sign)) {
                    //部门
                    params.put("supDepId", Util.null2String(subId));
                    params.put("allSupDepId", Util.null2String(allSubId));
                    params.put("subCompanyId1", Util.null2String(subCompanyId1));
                    //插入部门
                    newId = OrgDataUtil.deptInsert(params);
                    newSubCompanyId1 = subCompanyId1;
                }
                newAllSubId = allSubId + "," + newId;
                //递归调用
                updateOtherData(nextArray, newId, newAllSubId, newSubCompanyId1, departmentCode);
            }
        }
    }


    /**
     * 生成各自方法的参数
     *
     * @param jo   原接口数据参数对象
     * @param sign 标志分部/部门
     * @return 分部/部门的方法参数
     */
    private Map<String, Object> generateParams(JSONObject jo, String sign) {
        Map<String, Object> result = new HashMap<>(6);
        String canceled;
        //状态转换封存字段值
        if ("0".equals(jo.getString("status"))) {
            canceled = "1";
        } else {
            canceled = "";
        }
        result.put("canceled", canceled);
        result.put("level", Util.null2String(jo.getString("level")));
        if ("0".equals(sign)) {
            //分部
            result.put("subCompanyCode", Util.null2String(jo.getString("departmentCode")));
            result.put("subCompanyName", Util.null2String(jo.getString("departmentMark")));
            result.put("subCompanyDesc", Util.null2String(jo.getString("departmentName")));
            result.put("companyId", "1");
            result.put("showOrder", Util.getIntValue(Util.null2String(jo.getString("order")), 0));
        } else if ("1".equals(sign)) {
            //部门
            result.put("departmentCode", Util.null2String(jo.getString("departmentCode")));
            result.put("departmentMark", Util.null2String(jo.getString("departmentMark")));
            result.put("departmentName", Util.null2String(jo.getString("departmentName")));
            result.put("showOrder", Util.getIntValue(Util.null2String(jo.getString("order")), 0));
        }
        return result;
    }
}
