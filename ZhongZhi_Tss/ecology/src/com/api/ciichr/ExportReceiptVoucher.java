package com.api.ciichr;

import weaver.general.BaseBean;
import weaver.general.GCONST;
import weaver.general.Util;
import weaver.toolbox.core.convert.Convert;
import weaver.toolbox.core.date.DateUtil;
import weaver.toolbox.core.util.RandomUtil;
import weaver.toolbox.db.recordset.QueryUtil;
import weaver.toolbox.json.JSONArray;
import weaver.toolbox.json.JSONObject;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.io.File;
import java.io.IOException;

/**
 * 到款凭证导出
 * 红冲凭证导出
 *
 * <AUTHOR>
 */
@Path("/ciichr/receiptvoucher")
public class ExportReceiptVoucher {

    /**
     * 导出Excel文件
     *
     * <AUTHOR>
     */
    @POST
    @Path("/export")
    @Produces(MediaType.TEXT_PLAIN)
    public String export(@Context HttpServletRequest request, @Context HttpServletResponse response) throws IOException {

        BaseBean log = new BaseBean();

        String exportFileName;
        try {
            request.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Type", "text/html");

            String type = Util.null2String(request.getParameter("type")).trim();
            String pzh = Util.null2String(request.getParameter("pzh")).trim();
            int pzhInt = Convert.toInt(pzh);
            String ids = Util.null2String(request.getParameter("ids")).trim();
            ids = "'" + ids.replace(",", "','") + "'";

            String qry = "SELECT hszb,zdrbm,ywdybm,jyrq,fkrmc,a.je,pzdcbm,crmcode,xmbm FROM drmb a " +
                    " LEFT JOIN uf_ywhtxx b ON a.ywht = b.id " +
                    " LEFT JOIN uf_gxxx c ON b.htqsdw = c.id " +
                    " LEFT JOIN uf_kpss_dt1 d1 ON a.kpsq = d1.id " +
                    " LEFT JOIN uf_kpss d ON d1.mainid = d.id " +
                    " LEFT JOIN hrmdepartmentdefined e ON d.sqbm = e.deptid " +
                    " LEFT JOIN crm_customerinfo f ON d.khmc = f.id " +
                    " LEFT JOIN uf_lxhtxx g ON d.htmc = g.htmc " +
                    " WHERE jylsh IN (" + ids + ") " +
                    " ORDER BY jyrq,jysj";
            log.writeLog("qry ===> ", qry);
            JSONArray rtnArray = QueryUtil.doQuery(qry, "", "hszb,zdrbm,ywdybm,jyrq,fkrmc,je,pzdcbm,crmcode,xmbm");

            // 2022-09-27 科目编码可以在建模中配置
            String kmbm1 = ExportUtils.getCustomSubjectCode("0", "1");
            String kmbm2 = ExportUtils.getCustomSubjectCode("0", "2");

            // 2022-11-27 凭证类别编码可以在建模中配置
            String pzlbbm1 = ExportUtils.getPzlbbm("0", "1");
            String pzlbbm2 = ExportUtils.getPzlbbm("0", "2");

            JSONArray dataArray = new JSONArray();
            for (int i = 0; i < rtnArray.size(); i++) {
                JSONObject rtnObj = rtnArray.getJSONObject(i);

                for (int m = 0; m < 2; m++) {
                    JSONObject dataObj = new JSONObject();
                    dataObj.put("hszb", rtnObj.getStr("hszb"));
                    dataObj.put("pzh", pzhInt + i);
                    dataObj.put("fdjs", "0");
                    dataObj.put("zdrbm", rtnObj.getStr("zdrbm"));
                    String jyrq = rtnObj.getStr("jyrq");
                    if (!jyrq.isEmpty()) {
                        jyrq = DateUtil.format(jyrq, "yyyy-MM-dd", "yyyy/MM/dd");
                    }
                    dataObj.put("zdrq", jyrq);
                    dataObj.put("zy", rtnObj.getStr("fkrmc") + "到款");
                    String je = "1".equals(type) ? rtnObj.getStr("je") : "-" + rtnObj.getStr("je");
                    if (m == 0) {
                        dataObj.put("kmbm", kmbm1);
                        dataObj.put("pzlbbh", pzlbbm1);
                        dataObj.put("ybjfje", je);
                        dataObj.put("bbjfje", je);
                        dataObj.put("fzhs1", "31001512200050000359:银行账户");
                    } else {
                        dataObj.put("kmbm", kmbm2);
                        dataObj.put("pzlbbh", pzlbbm2);
                        dataObj.put("ybdfje", je);
                        dataObj.put("bbdfje", je);
                        String fzhs1 = (rtnObj.getStr("pzdcbm").isEmpty()) ? "" : rtnObj.getStr("pzdcbm") + ":部门";
                        dataObj.put("fzhs1", fzhs1);
                        String fzhs2 = (rtnObj.getStr("crmcode").isEmpty()) ? "" : rtnObj.getStr("crmcode") + ":客商";
                        dataObj.put("fzhs2", fzhs2);

                        // 2022-09-27 辅助核算3字段不用自动带出项目，默认为空。
                        //2023-08-07 再次放开，需要了
                        String fzhs3 = (rtnObj.getStr("xmbm").isEmpty()) ? "" : rtnObj.getStr("xmbm") + ":项目";
                        dataObj.put("fzhs3", fzhs3);
                    }
                    dataObj.put("bz", "人民币");
                    dataObj.put("jtbbjfje", "");
                    dataObj.put("qjbbjfje", "");
                    dataObj.put("ywdybm", rtnObj.getStr("ywdybm"));
                    dataObj.put("dj", "");
                    dataObj.put("jfsl", "");
                    dataObj.put("dfsl", "");
                    dataObj.put("jtbbdfje", "");
                    dataObj.put("qjbbdfje", "");
                    dataObj.put("jsh", "");
                    dataObj.put("sjrq", "");
                    dataObj.put("hxh", "");
                    dataObj.put("ywrq", jyrq);
                    dataObj.put("yhzh", "");
                    dataObj.put("pjlx", "");
                    dataObj.put("jsfs", "");
                    dataObj.put("bsgj", "");
                    dataObj.put("shg", "");
                    dataObj.put("jydm", "");
                    dataObj.put("khvatzcm", "");
                    dataObj.put("gysvatzcm", "");
                    dataObj.put("sm", "");
                    dataObj.put("vatfx", "");
                    dataObj.put("jsje", "");
                    dataObj.put("zzbbhl", "1.00000000");
                    dataObj.put("jtbbhl", "");
                    dataObj.put("qjbbhl", "");
                    dataObj.put("fzhs4", "");
                    dataObj.put("fzhs5", "");
                    dataObj.put("fzhs6", "");
                    dataObj.put("fzhs7", "");
                    dataObj.put("fzhs8", "");
                    dataObj.put("fzhs9", "");

                    dataArray.add(dataObj);
                }

            }

            String fileDir = Util.null2String(GCONST.getSysFilePath()) + "ciichr" + File.separator + "excelexport" + File.separator + "receiptvoucher";
            String fid = RandomUtil.randomString(20) + "";
            String fileName = fileDir + File.separator + fid + ".xlsx";
            String sheetName = "到款凭证";
            String fieldHeads = "核算账簿,凭证类别编码,凭证号,附单据数,制单人编码,制单日期,摘要,科目编码,币种,原币借方金额,本币借方金额,集团本币借方金额,全局本币借方金额,业务单元编码,单价,借方数量,贷方数量,原币贷方金额,本币贷方金额,集团本币贷方金额,全局本币贷方金额,结算号,结算日期,核销号,业务日期,银行账户,票据类型,结算方式,报税国家,收货国,交易代码,客户VAT注册码,供应商VAT注册码,税码,VAT方向,计税金额,组织本币汇率,集团本币汇率,全局本币汇率,辅助核算1,辅助核算2,辅助核算3,辅助核算4,辅助核算5,辅助核算6,辅助核算7,辅助核算8,辅助核算9";
            String fieldNames = "hszb,pzlbbh,pzh,fdjs,zdrbm,zdrq,zy,kmbm,bz,ybjfje,bbjfje,jtbbjfje,qjbbjfje,ywdybm,dj,jfsl,dfsl,ybdfje,bbdfje,jtbbdfje,qjbbdfje,jsh,sjrq,hxh,ywrq,yhzh,pjlx,jsfs,bsgj,shg,jydm,khvatzcm,gysvatzcm,sm,vatfx,jsje,zzbbhl,jtbbhl,qjbbhl,fzhs1,fzhs2,fzhs3,fzhs4,fzhs5,fzhs6,fzhs7,fzhs8,fzhs9";
            String fieldTails = "方向,分析币种,原币,组织本币,集团本币,全局本币,现金流量名称,现金流量编码,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,";
            String remark = "*导入须知:\n" +
                    "1.表格中不能增、删、改列及固有内容\n" +
                    "2.所有内容必须为文本格式;表格中有多个档案名称字段是为了实现多语,如果没有多语只录第一个名称字段即可\n" +
                    "3.枚举项输入错误时，则按默认值处理;勾选框的导入需输入N、Y\n" +
                    "4.导入带有子表的档案时,表格中主表与子表之间必须有一空行,且主、子表对应数据需加上相同的行号\n" +
                    "5.辅助核算字段保存凭证分录的辅助核算信息，格式为“辅助核算值编码：辅助核算项目类型编码”\n" +
                    "  例如：002:0004。前面的002表示辅助核算值编码，后面的0004表示辅助核算项目类型编码\n" +
                    "  如果想导入总账，需按照上述例子中的格式填好辅助核算值编码和辅助核算类型编码\n";
            String firstFieldHead = "\"null_$head,main_m_pk_accountingbook,main_m_pk_vouchertype,main_m_num,main_m_attachment,main_pk_prepared,main_m_prepareddate,m_explanation,m_accsubjcode,m_pk_currtype,m_debitamount,m_localdebitamount,m_groupdebitamount,m_globaldebitamount,unitname,m_price,m_debitquantity,m_creditquantity,m_creditamount,m_localcreditamount,m_groupcreditamount,m_globalcreditamount,m_checkno,m_checkdate,verifyno,verifydate,m_bankaccount,billtype,m_checkstyle,vat_pk_vatcountry,vat_pk_receivecountry,vat_businesscode,vat_pk_clientvatcode,vat_pk_suppliervatcode,vat_pk_taxcode,vat_direction,vat_moneyamount,m_excrate2,excrate3,excrate4,ass_1,ass_2,ass_3,ass_4,ass_5,ass_6,ass_7,ass_8,ass_9\"";
            String firstFieldTail = "\"cashflow,m_flag,cashflowcurr,m_money,m_moneymain,m_moneygroup,m_moneyglobal,cashflowName,cashflowCode\"";

            ExcelUtils utils = new ExcelUtils();
            utils.export(fileName, sheetName, fieldHeads, fieldNames, dataArray, fieldTails, remark, firstFieldHead, firstFieldTail);

            String name = "1".equals(type) ? "$本部到款凭证导出" : "$本部红冲凭证导出";
            exportFileName = fid + name;
        } catch (Exception e) {
            log.writeLog("ExportReceiptVoucher.export ===> ", e.getMessage());
            return "error";
        }

        return exportFileName;

    }
}
