package com.api.ciichr;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.*;
import weaver.general.BaseBean;
import weaver.toolbox.core.io.FileUtil;
import weaver.toolbox.json.JSONArray;
import weaver.toolbox.json.JSONObject;

import java.io.FileOutputStream;
import java.io.IOException;

public class ExcelUtils {

    private BaseBean log = new BaseBean();

    public void export(String fileName,String sheetName,String fieldHeads,
                       String fieldNames,JSONArray dataArray,String fieldTails,
                       String remark,String firstFieldHead,String firstFieldTail){

        FileOutputStream fileOutputStream = null;

        try{

            XSSFWorkbook wb = new XSSFWorkbook();

            //创建styleHead
            CellStyle styleHead = wb.createCellStyle();
            styleHead.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            styleHead.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            styleHead.setAlignment(HorizontalAlignment.CENTER);
            XSSFFont font = wb.createFont();
            //font.setBold(true);
            font.setFontHeight((short)240);
            styleHead.setFont(font);

            generateSheet(wb,styleHead,dataArray,sheetName,fieldHeads,fieldNames,fieldTails,remark,firstFieldHead,firstFieldTail);

            //生成Excel文件
            FileUtil.touch(fileName);
            fileOutputStream = new FileOutputStream(fileName);
            wb.write(fileOutputStream);
            fileOutputStream.flush();
            fileOutputStream.close();

        }catch(Exception e){
            log.writeLog("ExcelUtils.export ===> ",e.getMessage());
        }finally {
            if(fileOutputStream != null){
                try {
                    fileOutputStream.close();
                } catch (IOException e) {
                    log.writeLog("ExcelUtils.export ===> ",e.getMessage());
                }
            }
        }

    }

    /**
     *
     * @param wb                导出的Excel工作簿
     * @param styleHead         表头的样式
     * @param sheetName         Sheet的中文名称
     * @param fieldHeads        表的中文名称，逗号分隔
     * @param fieldNames        表的字段名称，逗号分隔
     * @param fieldTails        行尾的中文名称，逗号分隔
     * @param remark            文件说明
     * @param firstFieldHead    序号列的第二行文字
     * @param firstFieldTail    序号列的尾行文字
     * @throws IOException
     */
    public void generateSheet(XSSFWorkbook wb,CellStyle styleHead,JSONArray dataArray,String sheetName,
                              String fieldHeads, String fieldNames,String fieldTails,
                              String remark,String firstFieldHead,String firstFieldTail) {

        String[] fieldHeadArray = fieldHeads.split(",",-1);
        String[] fieldNameArray = fieldNames.split(",",-1);
        String[] fieldTailArray = fieldTails.split(",",-1);

        XSSFSheet sheet = wb.createSheet(sheetName);
        sheet.setColumnWidth(0,80 * 256);

        //第一行第一列 文件说明
        XSSFRow  rowRemark  = sheet.createRow(0);
        XSSFCell cellRemark = rowRemark.createCell(0);
        cellRemark.setCellValue(remark);
        //设置换行
        XSSFCellStyle csWrapText = wb.createCellStyle();
        csWrapText.setWrapText(true);
        cellRemark.setCellStyle(csWrapText);

        //创建表头
        XSSFRow rowHead = sheet.createRow(1);
        //序号列
        XSSFCell cellHeadXh = rowHead.createCell(0);
        cellHeadXh.setCellValue(firstFieldHead);
        cellHeadXh.setCellStyle(styleHead);

        //表单字段列
        for(int i=0; i<fieldHeadArray.length; i++){
            XSSFCell cell = rowHead.createCell(i+1);
            cell.setCellValue(fieldHeadArray[i]);
            cell.setCellStyle(styleHead);
            sheet.setColumnWidth(i+1,fieldHeadArray[i].length() * 3 * 256);
        }

        //生成数据
        for(int j=0; j<dataArray.size(); j++){
            //数据从第三行开始
            XSSFRow row = sheet.createRow(j+2);

            //序号
            XSSFCell cell0 = row.createCell(0);
            cell0.setCellValue(j+1);

            //表单字段
            JSONObject dataObj = dataArray.getJSONObject(j);
            for(int k=0; k<fieldNameArray.length; k++){
                XSSFCell cell = row.createCell(k+1);

                String fieldName = fieldNameArray[k];
                cell.setCellValue(dataObj.getStr(fieldName));
            }
        }

        //尾行
        XSSFRow rowTail = sheet.createRow(dataArray.size() + 2);
        XSSFCell cellTailXh = rowTail.createCell(0);
        cellTailXh.setCellValue(firstFieldTail);
        cellTailXh.setCellStyle(styleHead);

        for(int t=0; t<fieldTailArray.length; t++){
            XSSFCell cell = rowTail.createCell(t+1);
            cell.setCellValue(fieldTailArray[t]);
            if(!fieldTailArray[t].equals("")){
                cellTailXh.setCellStyle(styleHead);
            }
        }

    }

}
