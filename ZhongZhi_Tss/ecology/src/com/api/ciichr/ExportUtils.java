package com.api.ciichr;

import weaver.conn.RecordSet;
import weaver.general.Util;

/**
 * 功能说明
 *
 * <AUTHOR>
 * @create 2022-09-27 00:28
 */
public class ExportUtils {

    /**
     *
     * 根据导出类型获取每一行的自定义科目编码
     *
     */
    public static String getCustomSubjectCode(String exportType, String lineNumber){
        RecordSet rs = new RecordSet();
        String sql = "select kmbm from uf_pzkmbhpz where dclx = ? and xs = ?";
        rs.executeQuery(sql, exportType, lineNumber);
        if(rs.next()){
            return Util.null2String(rs.getString("kmbm"));
        }

        return "";
    }

    /**
     *
     * 根据导出类型获取每一行的凭证类别编码
     *
     */
    public static String getPzlbbm(String exportType, String lineNumber){
        RecordSet rs = new RecordSet();
        String sql = "select pzlbbm from uf_pzkmbhpz where dclx = ? and xs = ?";
        rs.executeQuery(sql, exportType, lineNumber);
        if(rs.next()){
            return Util.null2String(rs.getString("pzlbbm"));
        }

        return "";
    }
}
