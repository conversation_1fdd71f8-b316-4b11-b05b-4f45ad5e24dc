<%@ page language="java" contentType="application/json" pageEncoding="UTF-8"%>
<%@ page import="net.sf.json.JSONObject"%>
<%@ page import="java.util.Map"%>
<%@ page import="java.util.HashMap"%>
<%@ page import="com.weaver.general.Util"%>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />
<jsp:useBean id="rs2" class="weaver.conn.RecordSet" scope="page" />
<jsp:useBean id="rs3" class="weaver.conn.RecordSet" scope="page" />
<jsp:useBean id="log" class="weaver.general.BaseBean" scope="page" />

<%
	response.setContentType("application/json;charset=UTF-8");

	String ids = Util.null2String(request.getParameter("ids")).trim();
	//log.writeLog("AutoMatchReceiptCcb.jsp ids:",ids);

    log.writeLog("AutoMatchReceiptCcb.jsp start...");
    String qryAll = "select * from uf_jhdrmb where kpsq is null or kpsq=''";
    rs.execute(qryAll);
    while(rs.next()){
        String id = Util.null2String(rs.getString("id"));
        String fkrmc = Util.null2String(rs.getString("fkrmc")); //付款人名称
        String je = Util.null2String(rs.getString("je"));       //金额

        je = "".equals(je) ? "-10086" : je;

        String qryMatch = "select a.id,b.khmc,b.fptt,b.htmc,b.sqr from uf_kpss_dt1 a join uf_kpss b on a.mainid=b.id " +
                         " where a.zt = 0 and a.hkzt=0 and b.fpttwb='"+fkrmc+"' and jshj='"+je+"'";
        rs2.execute(qryMatch);

        String updateSql = "";
        if(rs2.getCounts() == 1){
            if(rs2.next()){
                String kpsqid = Util.null2String(rs2.getString("id")); //开票申请id
                String khmc = Util.null2String(rs2.getString("khmc")); //开票客户名称
                String fptt = Util.null2String(rs2.getString("fptt")); //发票抬头
                String htmc = Util.null2String(rs2.getString("htmc")); //合同名称
                String sqr = Util.null2String(rs2.getString("sqr"));   //申请人
                updateSql = "update uf_jhdrmb set kpsq='"+kpsqid+"', zt='1', zy='模板导入',kpkhmc='"+khmc+"',fptt='"+fptt+"',ywht='"+htmc+"',sqr='"+sqr+"' where id="+id;
            }
        }else{
            updateSql = "update uf_jhdrmb set zy='模板导入（匹配失败）' where id="+id;
        }
        rs3.execute(updateSql);
    }
    log.writeLog("AutoMatchReceiptCcb.jsp end.");

	Map returnMap = new HashMap();
	returnMap.put("status", "done");

	JSONObject jo = JSONObject.fromObject(returnMap);
	out.print(jo.toString());
%>