class simpleRoot extends React.Component {

    constructor(props) {
        super(props);
        this.state = {
            billid: "",
            url: "",

        };
    }

    componentDidMount() {
        let url = "/api/sd/hrmout/createQrData"
        let param = {}
        const {WeaLoadingGlobal} = window.ecCom;
        WeaLoadingGlobal.start({
            tip: "生成中..." // 自定义tip文字
        });
        window.GRSDK.http.postAC(url, param, (result) => {
            console.log("result", result)
            WeaLoadingGlobal.destroy(); // 销毁遮罩loading
            if (result && result.status === true) {
                if (result.billid) {
                    this.setState({
                        billid: result.billid,
                        url: "/spa/cube/index.html#/main/cube/card?billid=" + result.billid + "&type=0&modeId=239"
                    })
                }
            }
        })
    }

    render() {
        const {url} = this.state
        return (
            <div>
                <iframe src={url} width="50%" height="50%" frameBorder="0"></iframe>

            </div>
        )
    }
}

//发布模块
ecodeSDK.setCom('${appId}', 'page', simpleRoot);