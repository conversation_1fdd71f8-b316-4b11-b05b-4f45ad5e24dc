package com.engine.kailitai2.gyl.module.authtransfer.core;

import com.engine.kailitai2.gyl.module.authtransfer.bean.AuthTransferConfig;
import com.engine.kailitai2.gyl.module.authtransfer.util.AuthTransferUtil;
import com.engine.parent.common.JersyApiCoreBase;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.util.ModuleDataUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * @FileName AuthTransferCore.java
 * @Description 建模权限转移
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/9/2
 */
public class AuthRevokeCore extends JersyApiCoreBase {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 配置列表
     */
    private List<AuthTransferConfig> configList;


    public AuthRevokeCore(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        configList = null;
    }

    @Override
    public Map<String, Object> execute() {
        try {
            //初始化
            super.initBase(this.getClass().getSimpleName(), this.getClass().getName(), "建模权限转移撤销");
            //校验参数
            checkParam();
            if (!error.isEmpty()) {
                return handleResult();
            }
            //读取配置表
            configList = AuthTransferConfig.getConfigList();
            if (configList == null || configList.isEmpty()) {
                appendLog("没有配置信息，不处理");
                return handleResult();
            }
            //多线程执行每个配置信息
            appendLog("开始多线程执行权限撤销，配置列表：" + configList);
            //方式2：使用newFixedThreadPool线程池，多线程处理每一行明细数据, 使用CountDownLatch
            ExecutorService executorService = Executors.newFixedThreadPool(5);
            CountDownLatch latch = new CountDownLatch(configList.size());
            for (AuthTransferConfig eachConfig : configList) {
                executorService.submit(() -> {
                    try {
                        //处理明细的每一行数据
                        processEachConfig(eachConfig);
                    } finally {
                        latch.countDown(); // 线程完成任务后减少计数器
                    }
                });
            }
            try {
                // 等待所有线程完成任务，超时60分钟
                if (!latch.await(60, TimeUnit.MINUTES)) {
                    appendLog("超时未执行完所有线程任务，请检查!");
                }
            } catch (InterruptedException e) {
                log.error("InterruptedException error", e);
                appendLog("latch.await() 出错：" + e.getMessage());
            } finally {
                executorService.shutdown();
            }
            appendLog("多线程权限撤销，执行完毕");

        } catch (Exception e) {
            log.error("执行异常", e);
        }
        boolean needLog = Util.null2String(params.get("needLog")).equals("1");
        result.put("logNumber", Util.null2String(sdLog.getSerial_number()));
        return handleResult(needLog);
    }

    /**
     * 校验参数
     */
    private void checkParam() {
        List<String> missedParams = new ArrayList<>();
        String needParams = "user";
        for (String key : needParams.split(",")) {
            String value = Util.null2String(params.get(key));
            if (value.isEmpty()) {
                missedParams.add(key);
            }
        }
        if (!missedParams.isEmpty()) {
            error = "缺失参数:" + missedParams;
        }
    }

    /**
     * 执行每个配置
     *
     * @param eachConfig
     */
    private void processEachConfig(AuthTransferConfig eachConfig) {
        String logPrefix = "配置[" + eachConfig.getId() + "]";
        int modid = Util.getIntValue(Util.null2String(eachConfig.getJmmkid()), -1);//建模模块id
        if (modid < 0) {
            appendLog(logPrefix + "未配置模块id，不执行撤销共享权限");
            return;
        }
        int user = Util.getIntValue(Util.null2String(params.get("user")), -1);
        if (user < 0) {
            appendLog(logPrefix + "人员id有误");
            return;
        }

        List<String> billidList = AuthTransferUtil.getAllSDShareBillIdList(user, modid);
        appendLog("查询到该人员已有的权限转移的二开非默认共享数据id有：" + billidList);
        appendLog("删除已有的插入的权限转移的二开非默认共享");
        String delShareError = AuthTransferUtil.delShareByIdAndUser(user, modid);
        if (!delShareError.isEmpty()) {
            appendLog(logPrefix + "删除二开的转移非默认共享失败" + delShareError);
        } else {
            appendLog("开始重构权限");
            //重构建模权限
            ModuleDataUtil.resetModShare(modid, billidList);
        }
    }

}
