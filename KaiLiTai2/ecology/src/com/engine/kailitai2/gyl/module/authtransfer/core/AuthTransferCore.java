package com.engine.kailitai2.gyl.module.authtransfer.core;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.kailitai2.gyl.module.authtransfer.bean.AuthTransferConfig;
import com.engine.kailitai2.gyl.module.authtransfer.util.AuthTransferUtil;
import com.engine.parent.common.JersyApiCoreBase;
import com.engine.parent.common.util.EncryptUtil;
import com.engine.parent.http.dto.RestResult;
import com.engine.parent.http.util.HttpUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.sd2.time.util.SDTimeUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * @FileName AuthTransferCore.java
 * @Description 建模权限转移
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/9/2
 */
public class AuthTransferCore extends JersyApiCoreBase {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 查询建模数据pageSize
     */
    private static final int pageSize = 1000;
    /**
     * 配置列表
     */
    private List<AuthTransferConfig> configList;


    public AuthTransferCore(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        configList = null;
    }

    @Override
    public Map<String, Object> execute() {
        try {
            //初始化
            super.initBase(this.getClass().getSimpleName(), this.getClass().getName(), "建模权限转移");
            //校验参数
            checkParam();
            if (!error.isEmpty()) {
                return handleResult();
            }
            //读取配置表
            configList = AuthTransferConfig.getConfigList();
            if (configList == null || configList.isEmpty()) {
                appendLog("没有配置信息，不处理");
                return handleResult();
            }
            //多线程执行每个配置信息
            appendLog("开始多线程执行权限转移，配置列表：" + configList);
            //方式2：使用newFixedThreadPool线程池，多线程处理每一行明细数据, 使用CountDownLatch
            ExecutorService executorService = Executors.newFixedThreadPool(5);
            CountDownLatch latch = new CountDownLatch(configList.size());
            for (AuthTransferConfig eachConfig : configList) {
                executorService.submit(() -> {
                    try {
                        //处理明细的每一行数据
                        processEachConfig(eachConfig);
                    } finally {
                        latch.countDown(); // 线程完成任务后减少计数器
                    }
                });
            }
            try {
                // 等待所有线程完成任务，超时60分钟
                if (!latch.await(60, TimeUnit.MINUTES)) {
                    appendLog("超时未执行完所有线程任务，请检查!");
                }
            } catch (InterruptedException e) {
                log.error("InterruptedException error", e);
                appendLog("latch.await() 出错：" + e.getMessage());
            } finally {
                executorService.shutdown();
            }
            appendLog("多线程权限转移，执行完毕");

        } catch (Exception e) {
            log.error("执行异常", e);
        }
        boolean needLog = Util.null2String(params.get("needLog")).equals("1");
        result.put("logNumber", Util.null2String(sdLog.getSerial_number()));
        return handleResult(needLog);
    }

    /**
     * 校验参数
     */
    private void checkParam() {
        List<String> missedParams = new ArrayList<>();
        String needParams = "fromUser,toUser";
        for (String key : needParams.split(",")) {
            String value = Util.null2String(params.get(key));
            if (value.isEmpty()) {
                missedParams.add(key);
            }
        }
        if (!missedParams.isEmpty()) {
            error = "缺失参数:" + missedParams;
        }
    }

    /**
     * 执行每个配置
     *
     * @param eachConfig
     */
    private void processEachConfig(AuthTransferConfig eachConfig) {
        String logPrefix = "配置[" + eachConfig.getId() + "]";
        int modid = Util.getIntValue(Util.null2String(eachConfig.getJmmkid()), -1);//建模模块id
        if (modid < 0) {
            appendLog(logPrefix + "未配置模块id，不执行插入共享权限");
            return;
        }
        int toUser = Util.getIntValue(Util.null2String(params.get("toUser")), -1);

        //需要重构权限的列表
        List<String> billidList = AuthTransferUtil.getAllSDShareBillIdList(toUser, modid);
        appendLog("查询到该人员已有的权限转移的二开非默认共享数据id有：" + billidList);
        String delShareError = AuthTransferUtil.delShareByIdAndUser(toUser, modid);
        if (!delShareError.isEmpty()) {
            appendLog(logPrefix + "删除二开的转移非默认共享失败" + delShareError);
            return;
        } else {
            //直接先重构一次
            //重构建模权限
            ModuleDataUtil.resetModShare(eachConfig.getJmmkid(), billidList);
        }

        //分页获取所有数据
        Set<String> allIds = getAllAuthDataIds(eachConfig);
        if (allIds.isEmpty()) {
            appendLog(logPrefix + "没有获取有权限的数据，不执行转移");
            return;
        }

        //如果有配置指定数据id转移，那么只处理指定数据id的
        String testIds = Util.null2String(eachConfig.getZdjtsjidzy());
        if (!testIds.isEmpty()) {
            Set<String> testIdsSet = new HashSet<>();
            String[] testIdsArray = testIds.split(",");
            for (String testId : testIdsArray) {
                if (allIds.contains(testId)) {
                    testIdsSet.add(testId);
                }
            }
            appendLog(logPrefix + "指定数据id权限转移" + testIdsSet);
            if (!testIdsSet.isEmpty()) {
                //执行权限转移
                doTransferAuth(eachConfig, testIdsSet);
            } else {
                appendLog(logPrefix + "指定数据id都没有权限，不执行转移");
            }
        } else {
            appendLog(logPrefix + "总共获取到" + allIds.size() + "条数据，开始执行权限转移");
            //执行权限转移
            doTransferAuth(eachConfig, allIds);
        }
    }

    /**
     * 执行权限转移，增加非默认共享
     *
     * @param eachConfig
     * @param allIds
     */
    private void doTransferAuth(AuthTransferConfig eachConfig, Set<String> allIds) {

        List<String> allSqlList = new ArrayList<>();
        String toUser = Util.null2String(params.get("toUser"));
        //1查看权限，2编辑权限，3完全控制
        String rightType = Util.null2String(eachConfig.getQxlx());//权限类型
        int modid = Util.getIntValue(Util.null2String(eachConfig.getJmmkid()), -1);//建模模块id
        //给所有的数据id，给这个建模的共享表里，加上非默认共享
        String tableName = "modeDataShare_" + modid + "_set";
        for (String id : allIds) {
            String sql = "insert into " + tableName + "(sourceid,righttype,sharetype,relatedid,rolelevel,showlevel,isdefault,requestid,hrmCompanyVirtualType,jobleveltext)";
            sql += " values ('" + id + "','" + rightType + "','1','" + toUser + "','0','0',0,0,0,'" + AuthTransferUtil.SD_SHARE_CODE + "' )";
            allSqlList.add(sql);
        }
        appendLog("配置[" + eachConfig.getId() + "]非默认共享插入sql列表：" + allSqlList);
        String sqlError = DBUtil.executeTransactionSqlList(allSqlList);
        if (!sqlError.isEmpty()) {
            appendLog("配置[" + eachConfig.getId() + "]插入共享失败" + sqlError);
        } else {
            appendLog("配置[" + eachConfig.getId() + "]插入非默认共享成功！执行批量重构权限");
            List<String> billIdList = new ArrayList<>(allIds);
            //重构建模权限
            ModuleDataUtil.resetModShare(eachConfig.getJmmkid(), billIdList);
        }

    }

    /**
     * 分页获取所有权限数据ID
     *
     * @param eachConfig 配置信息
     * @return 所有数据的ID集合
     */
    private Set<String> getAllAuthDataIds(AuthTransferConfig eachConfig) {
        Set<String> allIds = new HashSet<>();
        int currentPage = 1;
        boolean hasMoreData = true;
        //接口地址
        String cubeApiAddress = Util.null2String(eachConfig.getJmcxjkdz());

        while (hasMoreData) {
            appendLog("正在查询第" + currentPage + "页数据...");

            //构建当前页的请求参数
            Map<String, String> bodyParam = buildApiParam(eachConfig, currentPage);

            //调用接口
            RestResult restResult = HttpUtil.postDataFormUrlEncoded(cubeApiAddress, bodyParam);
            appendLog("第" + currentPage + "页接口响应:" + JSONObject.toJSONString(restResult));

            if (restResult.isSuccess()) {
                //获取当前页的数据ID集合
                Set<String> currentPageIds = getAuthDataIds(restResult);

                if (!currentPageIds.isEmpty()) {
                    //将当前页的数据添加到总集合中
                    allIds.addAll(currentPageIds);
                    appendLog("第" + currentPage + "页获取到" + currentPageIds.size() + "条数据");

                    //如果当前页数据量小于pageSize，说明已经是最后一页
                    if (currentPageIds.size() < pageSize) {
                        hasMoreData = false;
                        appendLog("第" + currentPage + "页数据量小于" + pageSize + "，已到最后一页");
                    } else {
                        //继续查询下一页
                        currentPage++;
                    }
                } else {
                    //当前页没有数据，结束分页查询
                    hasMoreData = false;
                    appendLog("第" + currentPage + "页没有数据，结束分页查询");
                }
            } else {
                //接口调用失败，记录错误并结束查询
                appendLog("第" + currentPage + "页接口调用失败:" + restResult.getMsg());
                hasMoreData = false;
            }
        }

        appendLog("分页查询完成，总共获取到" + allIds.size() + "条数据");
        return allIds;
    }


    private Map<String, String> buildApiParam(AuthTransferConfig eachConfig, int pageNo) {
        Map<String, String> bodyParam = new HashMap<>();

        String systemid = Util.null2String(eachConfig.getJksqbs());
        String psw = Util.null2String(eachConfig.getJksqmm());

        //转移前人员id
        String fromUser = Util.null2String(params.get("fromUser"));

        JSONObject datajson = new JSONObject();
        //操作人信息
        JSONObject operationinfo = new JSONObject();
        operationinfo.put("operator", fromUser);
        datajson.put("operationinfo", operationinfo);

        datajson.put("mainTable", new JSONObject());

        JSONObject pageInfo = new JSONObject();
        pageInfo.put("pageNo", pageNo + "");
        pageInfo.put("pageSize", pageSize + "");
        datajson.put("pageInfo", pageInfo);

        JSONObject header = new JSONObject();
        String currentDateTime = SDTimeUtil.getCurrentTimeStringOnlyNumnbers();
        String beforePsw = systemid + psw + currentDateTime;
        appendLog("加密前的值:" + beforePsw);
        String afterPsw = EncryptUtil.md5(beforePsw);
        appendLog("加密后的值:" + afterPsw);
        header.put("systemid", systemid);
        header.put("currentDateTime", currentDateTime);
        header.put("Md5", afterPsw);
        datajson.put("header", header);


        bodyParam.put("datajson", datajson.toJSONString());
        return bodyParam;
    }


    /**
     * 解析接口响应中所有的id集合set
     *
     * @param restResult
     * @return
     */
    private Set<String> getAuthDataIds(RestResult restResult) {
        Set<String> ids = new HashSet<>();
        JSONObject jo, mainTable;
        String eachId;
        if (restResult.getResponseInfo() != null) {
            String responseBody = Util.null2String(restResult.getResponseInfo().getBody());
            JSONObject responseBodyJson = null;
            try {
                responseBodyJson = JSONObject.parseObject(responseBody);
            } catch (Exception e) {
                log.error("响应结果格式异常", e);
            }
            if (responseBodyJson != null) {
                if (responseBodyJson.containsKey("result")) {
                    JSONArray resultArray = responseBodyJson.getJSONArray("result");
                    if (resultArray != null && !resultArray.isEmpty()) {
                        for (int i = 0; i < resultArray.size(); i++) {
                            jo = resultArray.getJSONObject(i);
                            if (jo.containsKey("mainTable")) {
                                mainTable = jo.getJSONObject("mainTable");
                                if (mainTable.containsKey("id")) {
                                    eachId = Util.null2String(mainTable.get("id"));
                                    if (!eachId.isEmpty()) {
                                        ids.add(eachId);
                                    }
                                }
                            }
                        }
                    } else {
                        log.warn("当前查询结果没有result了，代表该页数据结束");
                    }
                }
            }
        }
        return ids;
    }
}
