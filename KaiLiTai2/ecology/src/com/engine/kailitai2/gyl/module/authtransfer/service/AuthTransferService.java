package com.engine.kailitai2.gyl.module.authtransfer.service;

import weaver.hrm.User;

import java.util.Map;


public interface AuthTransferService {
    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> transfer(Map<String, Object> params, User user);

    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> revoke(Map<String, Object> params, User user);
}
