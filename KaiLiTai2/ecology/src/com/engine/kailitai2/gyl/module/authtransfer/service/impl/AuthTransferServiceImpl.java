package com.engine.kailitai2.gyl.module.authtransfer.service.impl;

import com.engine.core.impl.Service;
import com.engine.kailitai2.gyl.module.authtransfer.core.AuthRevokeCore;
import com.engine.kailitai2.gyl.module.authtransfer.core.AuthTransferCore;
import com.engine.kailitai2.gyl.module.authtransfer.service.AuthTransferService;
import weaver.hrm.User;

import java.util.Map;


public class AuthTransferServiceImpl extends Service implements AuthTransferService {
    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> transfer(Map<String, Object> params, User user) {
        return new AuthTransferCore(params, user).execute();
    }

    @Override
    public Map<String, Object> revoke(Map<String, Object> params, User user) {
        return new AuthRevokeCore(params, user).execute();
    }

}
