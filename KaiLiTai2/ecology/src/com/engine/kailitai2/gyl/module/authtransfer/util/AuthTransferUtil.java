package com.engine.kailitai2.gyl.module.authtransfer.util;

import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.sd2.db.util.DBUtil;
import weaver.conn.RecordSet;

import java.util.ArrayList;
import java.util.List;

public class AuthTransferUtil {
    public static final String SD_SHARE_CODE = "SD_AUTH_TRANSFER";
    /**
     * 二开log类
     */
    private static final Logger log = LoggerFactory.getLogger(AuthTransferUtil.class);

    /**
     * 删除非默认共享
     * 删除指定数据id里的、指定人的、二开的、非默认共享
     *
     * @param userId
     * @param modeid
     */
    public static String delShareByIdAndUser(int userId, int modeid) {
        String error = "";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        //1、删除权限set表
        String setTablename = "modeDataShare_" + modeid + "_set";
        StringBuilder sql = new StringBuilder();
        sql.append(setTablename).append(" where 1=1 ");
        sql.append(" and relatedid = '").append(userId).append("' ");
        sql.append(" and jobleveltext = '").append(SD_SHARE_CODE).append("' ");

        String delSetSql = "delete from " + sql;
        log.info("delSetSql :" + delSetSql);
        if (!rs.executeUpdate(delSetSql)) {
            error = "删除" + setTablename + "权限set表出错：" + rs.getExceptionMsg() + ";delSetSql:" + delSetSql;
            log.error(error);
        }
        //2、删除建模权限表
        String selectIdSql = "select id from " + sql;
        String modeDataShareTable = "modeDataShare_" + modeid;
        log.info("modeDataShareTable:" + modeDataShareTable);
        String delModSql = "delete from " + modeDataShareTable + " where setid in (" + selectIdSql + ")";
        log.info("delModSql :" + delModSql);
        if (!rs.executeUpdate(delModSql)) {
            error = "删除" + setTablename + "权限modShare表出错：" + rs.getExceptionMsg() + ";delModSql:" + delModSql;
            log.error(error);
        }
        return error;
    }

    /**
     * 获取所有指定人员已经插入的二开权限转移的非默认共享
     *
     * @param userId
     * @param modeid
     * @return
     */
    public static List<String> getAllSDShareBillIdList(int userId, int modeid) {
        List<String> allIds = new ArrayList<>();
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        //权限set表
        String setTablename = "modeDataShare_" + modeid + "_set";
        String sql = "select sourceid from " + setTablename + " where relatedid = '" + userId + "'  and jobleveltext = '" + SD_SHARE_CODE + "'";
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                allIds.add(rs.getString("sourceid"));
            }
        }
        return allIds;

    }
}
