package com.engine.kailitai2.gyl.module.authtransfer.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.kailitai2.gyl.module.authtransfer.service.AuthTransferService;
import com.engine.kailitai2.gyl.module.authtransfer.service.impl.AuthTransferServiceImpl;
import com.engine.parent.common.util.ApiUtil;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;

/**
 * @FileName AuthTransferWeb.java
 * @Description 建模权限转移
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/9/4
 */
public class AuthTransferWeb {

    private AuthTransferService getService(User user) {
        return ServiceUtil.getService(AuthTransferServiceImpl.class, user);
    }


    /**
     * 建模权限转移
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/transfer")
    @Produces(MediaType.TEXT_PLAIN)
    public String transfer(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).transfer(params, user)
        );
    }


    /**
     * 建模权限转移
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/revoke")
    @Produces(MediaType.TEXT_PLAIN)
    public String revoke(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).revoke(params, user)
        );
    }


}
