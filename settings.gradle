rootProject.name = 'Weaver_E9_SD'

// 自动 include 所有含 build.gradle 的子项目，并设置别名
file('.').listFiles()
        .findAll { it.isDirectory() && new File(it, 'build.gradle').exists() }
        .each { dir ->
            def projectPath = dir.name
            include projectPath

            def buildFile = new File(dir, 'build.gradle')
            def alias = null

            try {
                // 读取 projectAlias = 'xxx' 的配置，给项目设置IDEA别名
                def matcher = buildFile.text =~ /projectAlias\s*=\s*['"](.+?)['"]/
                if (matcher.find()) {
                    alias = matcher.group(1)
                }

                if (alias != null) {
                    project(":${projectPath}").name = alias
                }
            } catch (Exception e) {
                println "读取别名失败: ${projectPath}，错误: ${e.message}"
            }
        }
